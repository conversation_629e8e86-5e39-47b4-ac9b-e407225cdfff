<template>
	<view class="min-h-screen bg-gray-50 flex flex-col h-screen">
		<!-- 页面头部 -->
		<!-- <view class="bg-white shadow-sm py-3 px-4 flex-shrink-0">
      <text class="text-lg font-bold text-gray-800">题目导入预览</text>
      <text class="text-xs text-gray-500 mt-1">共解析 {{ totalQuestions }} 道题目，{{ hasErrorQuestions ? `其中 ${errorQuestions.length} 道题目存在问题` : '全部解析成功' }}</text>
    </view> -->

		<!-- 题型分类栏 -->
		<scroll-view scroll-x class="bg-white border-b border-gray-100 shadow-sm flex-shrink-0" :show-scrollbar="false"
			enhanced :scroll-x="true">
			<view class="flex px-4 py-2 whitespace-nowrap space-x-2">
				<view @click="selectType('')" :class="[
            'px-2 py-2  rounded-full  transition-colors whitespace-nowrap', 
            selectedType === '' ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-700'
          ]">
					<text class="text-sm">全部 ({{ totalQuestions }})</text>
				</view>
				<view v-for="(count, type) in questionTypeCounts" :key="type" @click="selectType(type)" :class="[
            'px-2 py-2 rounded-full    transition-colors whitespace-nowrap', 
            selectedType === type ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-700'
          ]">
					<text class="text-sm">{{ getTypeText(type) }} ({{ count }})</text>
				</view>
			</view>
		</scroll-view>

		<!-- 功能栏 -->
		<view class="bg-white px-4 py-2 flex items-center justify-between shadow-sm flex-shrink-0">
			<view class="flex items-center">
				<text class="text-sm text-gray-700">共 {{ filteredQuestions.length }} 道题目</text>
				<view v-if="hasErrorFilteredQuestions" class="ml-3 px-2 py-0.5 rounded-full bg-red-100">
					<text
						class="text-xs text-red-500">{{ showOnlyErrors ? '仅显示问题题目' : `${errorFilteredQuestions.length}道题目有问题` }}</text>
				</view>
			</view>
			<view @click="toggleErrorFilter" class="flex items-center">
				<switch :checked="showOnlyErrors" color="#4f46e5" class="transform scale-75" />
				<text class="text-sm text-gray-600 ml-1">{{ showOnlyErrors ? '查看全部' : '仅看问题题目' }}</text>
			</view>
		</view>

		<!-- 题目列表 -->
		<scroll-view class="flex-1 min-h-0" scroll-y>
			<view class="p-3 space-y-3">
				<view v-if="filteredQuestions.length === 0" class="py-10 text-center">
					<text class="text-gray-500">暂无符合条件的题目</text>
				</view>

				<view v-for="(question, index) in filteredQuestions" :key="question._index"
					class="bg-white rounded-lg shadow-sm p-3 border border-gray-100 hover:shadow-md transition-shadow">
					<!-- 题目头部 -->
					<view class="flex items-center justify-between mb-1.5">
						<view class="flex items-center space-x-1.5">
							<!-- 折叠按钮最左 -->
							<view @click="toggleExpand(question._index)"
								class="w-7 h-7 bg-gray-50 rounded-full flex items-center justify-center transition-all duration-300 hover:bg-primary-50 mr-1"
								:class="{'transform rotate-180 bg-primary-50': isExpanded(question._index)}">
								<text class="fas fa-chevron-down text-gray-500 text-xs"
									:class="{'text-primary-500': isExpanded(index)}"></text>
							</view>
							<text class="text-xs font-medium text-gray-500">#{{ index + 1 }}</text>
							<view class="px-1.5 py-0.5 rounded-full text-xs"
								:class="getTypeTagClass(question.questionType)">
								<text>{{ question.questionTypeText || getTypeText(question.questionType) }}</text>
							</view>
							<view v-if="question.difficulty" class="px-1.5 py-0.5 rounded-full text-xs"
								:class="getDifficultyTagClass(question.difficulty)">
								<text>{{ question.difficultyText || getDifficultyText(question.difficulty) }}</text>
							</view>
							<view v-if="question._error && question._error.length > 0"
								class="px-1.5 py-0.5 bg-red-100 rounded-full">
								<text class="text-xs text-red-500">有问题</text>
							</view>
						</view>

						<!-- 操作按钮 -->
						<view class="flex items-center space-x-2">
							<!-- 编辑按钮 -->
							<view @click="editQuestion(question._index)"
								class="w-7 h-7 bg-gray-50 rounded-full flex items-center justify-center hover:bg-primary-50 transition-colors">
								<text class="fas fa-edit text-gray-500 text-xs hover:text-primary-500"></text>
							</view>
							<!-- 删除按钮 -->
							<view @click="showDeleteConfirm(question._index)"
								class="w-7 h-7 bg-gray-50 rounded-full flex items-center justify-center hover:bg-red-50 transition-colors">
								<text class="fas fa-trash-alt text-gray-500 text-xs hover:text-red-500"></text>
							</view>
						</view>
					</view>

					<!-- 题目内容 -->
					<view class="mb-2">
						<text class="text-sm text-gray-800"
							:class="{ 'line-clamp-2': !isExpanded(question._index) }">{{ question.questionContent }}</text>
					</view>

					<!-- 错误信息：始终显示 -->
					<view v-if="question._error && question._error.length > 0" class="mb-2 bg-red-50 p-3 rounded-lg">
						<view v-for="(error, errorIndex) in question._error" :key="errorIndex"
							class="flex items-center">
							<text class="fas fa-exclamation-circle text-red-500 mr-2"></text>
							<text class="text-xs text-red-600">{{ error }}</text>
						</view>
					</view>

					<!-- 展开的内容区域 -->
					<view v-if="isExpanded(question._index)">
						<!-- 选项列表 -->
						<view v-if="question.options && question.options.length > 0"
							class="mb-2 bg-gray-50 p-3 rounded-lg">
							<view v-for="(option, optIndex) in question.options" :key="optIndex"
								class="flex items-start mb-2 last:mb-0">
								<view
									class="flex-shrink-0 w-5 h-5 rounded-full bg-gray-100 flex items-center justify-center mr-2">
									<text class="text-xs"
										:class="isRightAnswer(question, optIndex) ? 'text-primary-500 font-bold' : 'text-gray-500'">{{ String.fromCharCode(65 + optIndex) }}</text>
								</view>
								<text class="text-xs text-gray-700 flex-1 pt-0.5">{{ option }}</text>
								<text v-if="isRightAnswer(question, optIndex)"
									class="fas fa-check text-primary-500 text-xs ml-1.5 flex-shrink-0 mt-1"></text>
							</view>
						</view>

						<!-- 答案 -->
						<view v-if="question.rightAnswers && question.rightAnswers.length > 0" class="mb-2">
							<text class="text-xs font-medium text-gray-700">答案：</text>
							<text class="text-xs text-primary-600">{{ formatAnswer(question) }}</text>
						</view>

						<!-- 解析 -->
						<view v-if="question.analysis" class="mb-2">
							<text class="text-xs font-medium text-gray-700">解析：</text>
							<text class="text-xs text-gray-600">{{ question.analysis }}</text>
						</view>

						<!-- 章节信息 -->
						<view v-if="question.chapterFullName" class="flex items-center mt-2 text-xs text-gray-500">
							<text class="fas fa-bookmark text-primary-400 mr-1.5"></text>
							<text>章节：{{ question.chapterFullName }}</text>
						</view>

					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 底部导入栏 -->
		<view class="bg-white border-t border-gray-200 px-4 py-3 shadow-lg  ">
			<button @click="importQuestions"
				class="w-full py-3 text-sm bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-lg flex items-center justify-center"
				:disabled="totalQuestions === 0">
				<text class="fas fa-upload mr-2"></text>
				<text>{{ hasErrorQuestions ? '跳过问题题目导入' : '全部导入' }}</text>
			</button>
		</view>

		<!-- 编辑题目弹窗 - 原生实现 -->
		<view v-if="showEditPopup" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
			@click="closeEditPopup">
			<view class="bg-white rounded-lg w-11/12 h-full max-h-[85vh] flex flex-col overflow-hidden" @click.stop>
				<view class="px-4 py-3 border-b border-gray-200 flex justify-between items-center flex-shrink-0">
					<text class="text-lg font-medium text-gray-800">编辑题目</text>
					<view @click="closeEditPopup" class="p-1">
						<text class="fas fa-times text-gray-500"></text>
					</view>
				</view>
				<scroll-view scroll-y class="flex-1   overflow-hidden">
					<question-form ref="questionForm" :bankId="bankId" :chapterReadOnly="true" />
				</scroll-view>

				<view class="px-4 py-2 bg-white z-5 border-t space-x-3 border-gray-200 flex justify-space-between  ">
					<button @click="closeEditPopup" class="px-4 w-1/2 py-2 border border-gray-300 rounded-lg   text-sm">
						取消
					</button>
					<button @click="saveEdit" class="px-4 w-1/2 py-2 bg-primary-500 text-white rounded-lg text-sm">
						保存修改
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				questions: [], // 从全局存储中获取的题目
				selectedType: '', // 当前选中的题型
				showOnlyErrors: false, // 是否只显示有问题的题目
				currentEditIndex: -1, // 当前编辑的题目索引
				showEditPopup: false, // 控制编辑弹窗显示
				expandedQuestions: [], // 记录每个题目是否展开
				bankId: null,
			}
		},
		onLoad(options) {
			this.bankId = options.bankId;
			// 从全局存储中获取题目数据
			const app = getApp();
			if (app.globalData && app.globalData.parsedQuestions) {
				this.questions = JSON.parse(JSON.stringify(app.globalData.parsedQuestions));
			}

			console.log(this.questions);
			// 监听页面返回
			uni.$on('onBackPress', this.handleBackPress);
		},
		onUnload() {
			uni.$off('onBackPress', this.handleBackPress);
		},
		computed: {
			// 所有题目的总数
			totalQuestions() {
				return this.questions.length;
			},

			// 有错误的题目
			errorQuestions() {
				return this.questions.filter(q => q._error && q._error.length > 0);
			},
			errorFilteredQuestions() {
				return this.filteredQuestions.filter(q => q._error && q._error.length > 0);
			},
			hasErrorFilteredQuestions() {
				return this.errorFilteredQuestions.length > 0;
			},

			// 是否存在错误题目
			hasErrorQuestions() {
				return this.errorQuestions.length > 0;
			},

			// 按题型统计题目数量
			questionTypeCounts() {
				const counts = {};
				this.questions.forEach(question => {
					const type = question.questionType ? question.questionType.toString() : 'unknown';
					if (!counts[type]) {
						counts[type] = 0;
					}
					counts[type]++;
				});
				return counts;
			},

			// 根据筛选条件过滤后的题目列表
			filteredQuestions() {
				let result = this.questions;

				// 按题型筛选
				if (this.selectedType) {
					result = result.filter(q => q.questionType && q.questionType.toString() === this.selectedType);
				}

				// 按错误筛选
				if (this.showOnlyErrors) {
					result = result.filter(q => q._error && q._error.length > 0);
				}

				return result;
			},

			// 是否为选择题类型
			isChoiceQuestion() {
				return this.editForm.questionType === 1 || this.editForm.questionType === 2;
			}
		},
		methods: {
			// 选择题型进行筛选
			selectType(type) {
				this.selectedType = type;
				this.showOnlyErrors = false;
			},

			// 切换是否只显示错误题目
			toggleErrorFilter() {
				this.showOnlyErrors = !this.showOnlyErrors;
			},

			// 获取题型文本
			getTypeText(type) {
				const typeMap = {
					'1': '单选题',
					'2': '多选题',
					'3': '判断题',
					'4': '填空题',
					'5': '问答题',
					'9': '组合题',
				};
				return typeMap[type] || '未知题型';
			},

			// 获取题型标签样式
			getTypeTagClass(type) {
				const classMap = {
					1: 'bg-blue-100 text-blue-600',
					2: 'bg-primary-100 text-primary-600',
					3: 'bg-yellow-100 text-yellow-600',
					4: 'bg-purple-100 text-purple-600',
					5: 'bg-pink-100 text-pink-600',
					9: 'bg-gray-100 text-gray-600'
				};
				return classMap[type] || 'bg-gray-100 text-gray-600';
			},

			// 获取难度文本
			getDifficultyText(difficulty) {
				const difficultyMap = {
					1: '简单',
					2: '一般',
					3: '困难'
				};
				return difficultyMap[difficulty] || '一般';
			},

			// 获取难度标签样式
			getDifficultyTagClass(difficulty) {
				const classMap = {
					1: 'bg-green-100 text-green-600',
					2: 'bg-yellow-100 text-yellow-600',
					3: 'bg-red-100 text-red-600'
				};
				return classMap[difficulty] || 'bg-gray-100 text-gray-600';
			},

			// 格式化答案显示
			formatAnswer(question) {
				if (!question.rightAnswers || question.rightAnswers.length === 0) {
					return '无';
				}

				// 判断题
				if (question.questionType === 3) {
					return question.rightAnswers[0] === '0' ? '正确' : '错误';
				}

				// 选择题
				if (question.questionType === 1 || question.questionType === 2) {
					return question.rightAnswers
						.map(index => String.fromCharCode(65 + parseInt(index)))
						.join(', ');
				}

				return question.rightAnswers.join(', ');
			},

			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 导入题目
			importQuestions() {
				// 根据是否有错误题目，决定导入的内容
				// let questionsToImport = this.questions;


				const errorQuestions = this.errorQuestions;

				const questionsToImport = this.questions.filter(q => !q._error || q._error.length === 0);

				if (questionsToImport.length < 1) {
					uni.showToast({
						title: '没有题目可导入',
						icon: 'none'
					});
					return;
				}


				uni.showModal({
					title: '确认导入',
					content: `确定${errorQuestions.length>0?'跳过'+errorQuestions.length+'道问题题目':''}导入${questionsToImport.length}道题目吗？`,
					success: (res) => {
						if (res.confirm) {
							const chapterFullNames = [];
							questionsToImport.forEach(q => {
								if (q.chapterFullName && !chapterFullNames.includes(q
									.chapterFullName)) {
									chapterFullNames.push(q.chapterFullName);
								}
							});

              const that = this;

							function requestBatchAddQuestion(chapterResult) {

								const postQuestionList = questionsToImport.map((item) => ({
									questionType: item.questionType,
									difficulty: item.difficulty,
									questionContent: item.questionContent,
									children: item.children?.map((childItem) => ({
										questionType: childItem.questionType,
										difficulty: childItem.difficulty,
										questionContent: childItem.questionContent,

										options: childItem.options,
										rightAnswer: childItem.rightAnswers.join(),
										analysis: childItem.analysis,
									})),
									options: item.options,
									rightAnswer: item.rightAnswers.join(),
									analysis: item.analysis,
									chapterId: chapterResult?.find(
										(chapter) => chapter.fullName === item.chapterFullName,
									)?.chapterId,
								}));

								that.$reqPost(`/front/edu-personal/question/batchAdd/${that.bankId}`,
										postQuestionList, true, '导入题目中')
									.then((res) => {

										if (res.success) {
											uni.showModal({
												title: '导入题目成功',
												content: '已经导入' + postQuestionList.length + '道题目',
												showCancel: false,
												success: (res) => {
													uni.navigateBack();
												}
											});

										} else {
											uni.showToast({
												title: res.errorMessage || '导入题目失败',
												icon: 'none'
											});
										}
									})

							}

							if (chapterFullNames.length) {
								  this.$reqPost('/front/edu-personal/chapter/batchAddByFullName', {
											bankId: this.bankId,
											fullNames: chapterFullNames,
										},
										true,
										'导入章节中'
									)
									.then((res) => {
										if (res.success) {
											requestBatchAddQuestion(res.data);
										} else {
											uni.showToast({
												title: res.errorMessage || '导入章节失败',
												icon: 'none'
											});
										}
									})

							} else {
								requestBatchAddQuestion();
							}


						}
					}
				});
			},

 

			// 编辑题目
			editQuestion(index) {
				this.currentEditIndex = index;
				// const question = this.filteredQuestions[index];
				const question = this.questions.find(q => q._index === index);
				if (!question) {
					return;
				}
				// 填充编辑表单
				const editForm = {
					questionType: question.questionType || 1,
					questionContent: question.questionContent || '',
					options: [...(question.options || ['', ''])],
					rightAnswer: this.convertRightAnswers(question),
					analysis: question.analysis || '',
					difficulty: question.difficulty || 2,
					chapterFullName: question.chapterFullName || '',
					chapterId: question.chapterId || null
				};



				// 打开编辑弹窗
				this.showEditPopup = true;
				this.$nextTick(() => {
					this.$refs.questionForm.updateFormValue(editForm);
				})
			},

			// 转换答案格式
			convertRightAnswers(question) {
				if (!question.rightAnswers || question.rightAnswers.length === 0) {
					return '';
				}

				// 单选题 判断题
				if (question.questionType === 1 || question.questionType === 3) {
					return question.rightAnswers[0];
				}

				// 多选题
				if (question.questionType === 2) {
					return question.rightAnswers.join(',');
				}
				return '';
			},

			// 关闭编辑弹窗
			closeEditPopup() {
				this.showEditPopup = false;
			},

			// 保存编辑
			saveEdit() {

				this.$refs.questionForm.validateForm((formValues) => {
					if (this.currentEditIndex >= 0) {
						// const question = this.filteredQuestions[this.currentEditIndex];
						const question = this.questions.find(q => q._index === this.currentEditIndex);
						if (!question) {
							return;
						}

						// 更新题目信息
						question.questionType = formValues.questionType;
						question.questionContent = formValues.questionContent;
						question.options = [...formValues.options];
						question.rightAnswers = formValues.rightAnswer ? formValues.rightAnswer.split(',') : [];
						question.analysis = formValues.analysis;
						question.difficulty = formValues.difficulty;
						question.questionTypeText = this.getTypeText(formValues.questionType);
						question.difficultyText = this.getDifficultyText(formValues.difficulty);

						// 清除错误标记
						question._error = [];

						// 更新全局数据
						// getApp().globalData.parsedQuestions = this.questions;

						// 关闭弹窗并显示提示
						this.closeEditPopup();
						uni.showToast({
							title: '修改成功',
							icon: 'success'
						});
					}
				})




			},

			// 转换为答案数组
			convertToRightAnswersArray(rightAnswer, questionType) {
				if (!rightAnswer) {
					return [];
				}

				// 单选题或判断题
				if (questionType === 1 || questionType === 3) {
					return [rightAnswer];
				}

				// 多选题
				if (questionType === 2) {
					return rightAnswer.split(',');
				}

				return [];
			},


			// 显示删除确认弹窗
			showDeleteConfirm(index) {
				const arrIndex = this.currentEditIndex = this.questions.findIndex(q => q._index === index);
				uni.showModal({
					title: '确认删除',
					content: '确定要删除这道题目吗？此操作不可恢复。',
					success: (res) => {
						if (res.confirm) {
							this.questions.splice(arrIndex, 1);
							uni.showToast({
								title: '题目已删除',
								icon: 'success'
							});

						}
					}
				});
			},
			// 切换题目展开/收起状态
			toggleExpand(index) {
				this.expandedQuestions[index] = !this.expandedQuestions[index];
			},

			// 判断题目是否展开
			isExpanded(index) {
				return this.expandedQuestions[index] || false;
			},

			// 判断选项是否为正确答案
			isRightAnswer(question, optionIndex) {
				if (!question.rightAnswers || question.rightAnswers.length === 0) {
					return false;
				}

				const answerIndex = optionIndex.toString();
				return question.rightAnswers.includes(answerIndex);
			},

			handleBackPress(e) {
				uni.showModal({
					title: '确认返回',
					content: '返回将丢失已解析的题目，确定要返回吗？',
					success: (res) => {
						if (res.confirm) {
							uni.navigateBack();
						}
					}
				});
				return true; // 阻止默认返回
			}
		}
	}
</script>

<style scoped>
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>