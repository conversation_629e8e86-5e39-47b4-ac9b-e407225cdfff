<template>
   
     
      
      <!-- 表单内容 -->
 
        <view class="bg-white rounded-xl p-5 shadow-sm">
          <!-- 章节选择 -->
          <view class="mb-5">
            <view class="mb-2  ">
              <text class="text-sm font-medium text-gray-700">章节</text>
            
            </view>
            <question-chapter-picker v-if="!chapterReadOnly" v-model="questionForm.chapterId" :bankId="bankId"    />
            <view v-else class="text-sm text-gray-500">{{ questionForm.chapterFullName||'--' }}</view>
          </view>
          
          <!-- 题型选择 -->
          <view class="mb-5">
            <view class="mb-2">
              <text class="text-sm font-medium text-gray-700">题型 <text class="text-red-500">*</text></text>
            </view>
            <radio-group @change="onQuestionTypeChange" :disabled="isEdit">
              <view class="flex flex-wrap gap-3">
                <view v-for="questionType in questionTypes" :key="questionType.id" class="flex items-center py-1 px-1 rounded-lg"  >
                  <radio   :value="questionType.id+''" :checked="questionForm.questionType == questionType.id"  >
                  <text class="ml-1 text-gray-800">{{ questionType.name }}</text>
                  </radio>
                </view>
              </view>
            </radio-group>
       
          </view>
          
          <!-- 难度选择 -->
          <view class="mb-5">
            <view class="mb-2">
              <text class="text-sm font-medium text-gray-700">难度 <text class="text-red-500">*</text></text>
            </view>
             
            <radio-group @change="onDifficultyChange">
              <view class="flex flex-wrap gap-3">
                <view v-for="difficulty in difficulties" :key="difficulty.id" class="flex items-center p-1 rounded-lg"  >
                  <radio   :value="difficulty.id+''" :checked="questionForm.difficulty == difficulty.id"   >
                  <text class="ml-1 text-gray-800">{{ difficulty.name }}</text>
                  </radio>
                </view>
              </view>
            </radio-group>
          </view>
          
          <!-- 题干内容 -->
          <view class="mb-5">
            <view class="mb-2">
              <text class="text-sm font-medium text-gray-700">题干 <text class="text-red-500">*</text></text>
            </view>
            <textarea 
              v-model="questionForm.questionContent"
              placeholder="请输入题目内容..."
              class="w-full px-4 py-3 h-40 border border-solid border-gray-300 rounded-lg text-sm"
            ></textarea>
          </view>
          
          <!-- 选项设置 (仅单选和多选题) -->
          <view v-if="isChoiceQuestion" class="mb-5">
            <view class="mb-3">
              <text class="text-sm font-medium text-gray-700">选项设置 <text class="text-red-500">*</text></text>
            </view>
            
            <!-- 选项内容 -->
            <view v-for="(option, index) in questionForm.options" :key="index" class="mb-3 bg-gray-50 rounded-lg p-3">
              <view class="flex items-center justify-between mb-2">
                <text class="text-sm font-medium">选项 {{ String.fromCharCode(65 + index) }}</text>
                <view>
                  <button 
                  v-if="questionForm.options.length > 2"
                  @click="removeOption(index)" 
                  class="h-6 w-6 bg-red-100 text-red-500 text-xs rounded-full flex items-center justify-center p-0"
                >
                  <text class="fas fa-times"></text>
                </button>
                </view>
              </view>
              
              <textarea 
                v-model="questionForm.options[index]"
                :placeholder="`请输入选项${String.fromCharCode(65 + index)}内容...`"
                class="w-full px-4 py-3 border border-gray-300 border-solid rounded-lg text-sm h-20"
              ></textarea>
            </view>
            
            <button 
                @click="addOption" 
                class="w-full py-2 mt-2 bg-primary-100 text-primary-600 text-sm rounded-lg flex items-center justify-center"
              >
                <text class="fas fa-plus mr-1.5"></text>
                <text>添加选项</text>
              </button>
            
            <!-- <view class="text-xs text-gray-500 mt-2">请至少添加2个选项</view> -->
          </view>
  
     
          <view   class="mb-5">
            <view class="mb-2">
              <text class="text-sm font-medium text-gray-700">答案 <text class="text-red-500">*</text></text>
            </view>
            <question-right-answer-edit :questionType="questionForm.questionType" :options="questionForm.options" v-model="questionForm.rightAnswer"></question-right-answer-edit> 
          </view>
  
   
          
          <!-- 题目解析 -->
          <view class="mb-3">
            <view class="mb-2">
              <text class="text-sm font-medium text-gray-700">题目解析</text>
            </view>
            <textarea 
              v-model="questionForm.analysis"
              placeholder="请输入题目解析..."
              class="w-full px-4 py-3 h-32 border border-gray-300 rounded-lg border-solid text-sm"
            ></textarea>
          </view>
          
          <!-- 提交按钮 -->
   
        </view>
     
   
   
  </template>
  
  <script>
  
  
  export default {
    props:{
      bankId:String,
      chapterReadOnly:Boolean,
      isEdit:Boolean
    },
    data() {

      return {
      
     
        
        // 用于题型选择的数据
        questionTypes: [
          { id: 1, name: '单选题' },
          { id: 2, name: '多选题' },
          { id: 3, name: '判断题' }
        ],
         
        
        // 用于难度选择的数据
        difficulties: [
          { id: 1, name: '简单' },
          { id: 2, name: '一般' },
          { id: 3, name: '困难' }
        ],
        
    
        // 表单数据
        questionForm: {
          chapterFullName:'',
          chapterId: null,
          questionType: 1, // 1: 单选, 2: 多选, 3: 判断
          questionContent: '',
          options: [
             '',''
          ],
         
          analysis: '',
          difficulty: 2,
          rightAnswer:'',
           
        }
      }
    },
    
    computed: {
      isChoiceQuestion() {
        return this.questionForm.questionType === 1 || this.questionForm.questionType === 2;
      },
    },
    
 
 
    
    methods: {
        updateFormValue(values){
            this.questionForm = {...this.questionForm,...values};
        },

        init(){
            this.questionForm.questionType = 1;
            this.questionForm.rightAnswer = '';
            this.questionForm.options = [
              '',''
            ];
            this.questionForm.analysis = '';
            this.questionForm.difficulty = 2;
            this.questionForm.questionContent = '';
            this.questionForm.chapterId = null;
        },

      // 题型选择相关方法
      onQuestionTypeChange(e) {
        

        const lastQuestionType =this.questionForm.questionType;
        this.questionForm.questionType =  parseInt(e.detail.value);
        this.questionForm.rightAnswer = '';
        // 重置选项和答案
        if (this.questionForm.questionType === 3) { // 判断题
          this.questionForm.options = [];
         
        } else {
          if (this.questionForm.options.length < 2) {
            if(lastQuestionType===3){
                this.questionForm.options = [
              '',''
            ];
            }
           
          }
        }
      },
      
      // 难度选择相关方法
      onDifficultyChange(e) {
   
        this.questionForm.difficulty = e.detail.value;
      },
      
      // 选项相关方法
      addOption() {
        this.questionForm.options.push('');
      },
      
      removeOption(index) {
        this.questionForm.options.splice(index, 1);
        const rightAnswer = this.questionForm.rightAnswer;
        if(!rightAnswer){
          return;
        }
  
        const questionType =this.questionForm.questionType;
        if(questionType == 1){
          if(rightAnswer!=index){
            this.questionForm.rightAnswer = '';
          }
         
        }else if(questionType == 2){
          this.questionForm.rightAnswer = this.questionForm.rightAnswer.split(',').filter(item=>item!=index).join(',');
        }
      },
      // 验证表单
      validateForm(callback) {
        if (!this.questionForm.questionType) {
          uni.showToast({
            title: '请选择题型',
            icon: 'none'
          });
          return false;
        }
        
        if (!this.questionForm.questionContent.trim()) {
          uni.showToast({
            title: '请输入题干内容',
            icon: 'none'
          });
          return false;
        }
        
        if (this.isChoiceQuestion) {
          // 检查是否至少有2个选项
          if (this.questionForm.options.length < 2) {
            uni.showToast({
              title: '请至少添加2个选项',
              icon: 'none'
            });
            return false;
          }
          
          // 检查是否每个选项都有内容
          for (let i = 0; i < this.questionForm.options.length; i++) {
            if (!this.questionForm.options[i].trim()) {
              uni.showToast({
                title: `请输入选项${String.fromCharCode(65 + i)}内容`,
                icon: 'none'
              });
              return false;
            }
          }
          
          
        }
        
        // 检查是否有正确答案
        if (!this.questionForm.rightAnswer) {
            uni.showToast({
              title: '请选择正确答案',
              icon: 'none'
            });
            return false;
          }
        if(this.questionForm.questionType == 1){
          const rightAnswer =parseInt(this.questionForm.rightAnswer);
          if(rightAnswer<0 || rightAnswer>=this.questionForm.options.length){
            uni.showToast({
              title: '正确答案不在选项范围内',
              icon: 'none'
            });
            return false;
          }
        }
  
        if(this.questionForm.questionType == 2){
          const rightAnswer =this.questionForm.rightAnswer.split(',');
          
  
          if(rightAnswer.length<2){
            uni.showToast({
              title: '请至少选择2个正确答案',
              icon: 'none'
            });
            return false;
          }
  
          for(let i=0;i<rightAnswer.length;i++){
            if(rightAnswer[i]<0 || rightAnswer[i]>=this.questionForm.options.length){
              uni.showToast({
                title: '正确答案不在选项范围内',
                icon: 'none'  
              });
              return false;
            }
          }
        }
  
  
        
        if (!this.questionForm.difficulty) {
          uni.showToast({
            title: '请选择难度',
            icon: 'none'
          });
          return false;
        }

        this.questionForm.bankId = this.bankId;
        if(callback){
          callback({...this.questionForm,chapterFullName:undefined});
        }
      },   
    }
  }
  </script>
  
  <style scoped>
  </style>