const key = "userInfo";

const defaultUserInfo = uni.getStorageSync(key);
export const login = {
	namespaced:true,
	state: () => ({
		loading: true,
		hasLogin: !!defaultUserInfo,
		userInfo: defaultUserInfo
	}),
	mutations: {
		login(state, val) {
			state.hasLogin = true;
			uni.setStorageSync(key, val);
			uni.setStorageSync("h5_token", val.jwt);
			state.userInfo = val;
		},
		logout(state) {
			state.hasLogin = false;
			uni.removeStorageSync(key);
			uni.removeStorageSync("h5_token");
			// console.log("清除token");
			state.userInfo = undefined;
		},
		loginEnd(state) {
			state.loading = false;
		}
	}

}