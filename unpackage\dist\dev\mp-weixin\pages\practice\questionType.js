"use strict";
const myjs_question = require("../../myjs/question.js");
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      typeList: [],
      bankId: ""
    };
  },
  onLoad(options) {
    this.bankId = options.bankId || "";
    this.loadTypeList();
  },
  methods: {
    loadTypeList() {
      if (!this.bankId)
        return;
      this.$reqGet(`/front/edu/qbank/questionTypeList/${this.bankId}`).then(
        (res) => {
          if (res.success) {
            this.typeList = (res.data || []).map((item) => {
              const match = myjs_question.questionTypeEnum.find(
                (q) => q.value === item.value
              );
              return {
                ...item,
                label: match ? match.label : item.value
              };
            });
          }
        }
      );
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($data.typeList, (item, k0, i0) => {
      return {
        a: common_vendor.t(item.label),
        b: common_vendor.t(item.questionCount),
        c: item.value
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-602e6404"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/practice/questionType.js.map
