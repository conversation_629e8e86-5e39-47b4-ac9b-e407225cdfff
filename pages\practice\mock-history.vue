<template>
  <view class="h-screen bg-gray-50 flex flex-col">
    <scroll-pagination
      class="flex-1 overflow-hidden"
      :page-size="12"
      :auto-load="true"
      ref="scrollPagination"
      :enable-refresh="true"
      @load="loadData"
    >
      <template v-slot="{ list }">
        <view class="px-3 py-2">
          <view
            v-for="item in list"
            :key="item.id"
            class="mb-3 bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden"
            @click="toResult(item)"
          >
            <!-- 左侧装饰条 -->
            <view class="flex">
              <view
                class="w-1 flex-shrink-0"
                :class="
                  item.score >= item.passScore ? 'bg-green-500' : 'bg-red-500'
                "
              ></view>

              <!-- 主体内容 -->
              <view class="flex-1 p-4">
                <!-- 第一行：标题和状态 -->
                <view class="flex items-center justify-between mb-2">
                  <view class="flex items-center flex-1 mr-2">
                    <i
                      class="fas fa-file-alt text-primary-500 mr-2 text-sm"
                    ></i>
                    <text class="text-base font-semibold text-gray-800">
                      {{ item.name || "模拟考试" }}
                    </text>
                  </view>
                  <view
                    class="px-2 py-0.5 rounded text-xs font-medium flex-shrink-0 flex items-center"
                    :class="
                      item.score >= item.passScore
                        ? 'bg-green-100 text-green-600'
                        : 'bg-red-100 text-red-600'
                    "
                  >
                    <i
                      class="mr-1 text-xs"
                      :class="
                        item.score >= item.passScore
                          ? 'fas fa-check-circle'
                          : 'fas fa-times-circle'
                      "
                    ></i>
                    {{ item.score >= item.passScore ? "通过" : "未通过" }}
                  </view>
                </view>

                <!-- 第二行：分数信息 -->
                <view class="flex items-center justify-between mb-2">
                  <view class="flex items-center space-x-4">
                    <view class="flex items-center">
                      <i class="fas fa-star text-yellow-500 mr-1 text-xs"></i>
                      <text class="text-xs text-gray-400 mr-1">得分</text>
                      <text class="text-xl font-bold text-gray-800">{{
                        item.score
                      }}</text>
                    </view>
                    <view class="flex items-center">
                      <i class="fas fa-trophy text-gray-400 mr-1 text-xs"></i>
                      <text class="text-xs text-gray-400 mr-1">总分</text>
                      <text class="text-sm font-medium text-gray-600">{{
                        item.totalScore
                      }}</text>
                    </view>
                  </view>
                  <view class="flex items-center">
                    <i class="fas fa-target text-blue-500 mr-1 text-xs"></i>
                    <text class="text-xs text-gray-400 mr-1">及格分</text>
                    <text class="text-sm font-medium text-gray-600">{{
                      item.passScore
                    }}</text>
                  </view>
                </view>

                <!-- 第三行：详细信息 -->
                <view
                  class="flex items-center justify-between text-xs text-gray-500"
                >
                  <view class="flex items-center">
                    <i class="far fa-calendar-alt text-gray-400 mr-1"></i>
                    <text>{{ item.closeTime }}</text>
                  </view>
                  <view class="flex items-center">
                    <i class="far fa-clock text-gray-400 mr-1"></i>
                    <text>用时 {{ formatDuration(item.spentDuration) }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </template>
      <template #empty>
        <view
          class="py-12 px-4 bg-white rounded-xl shadow-sm flex flex-col items-center justify-center"
        >
          <view
            class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3"
          >
            <text class="fas fa-history text-gray-300 text-2xl"></text>
          </view>
          <text class="text-gray-500 text-sm">暂无模拟考试记录</text>
        </view>
      </template>
    </scroll-pagination>
  </view>
</template>

<script>
export default {
  data() {
    return {
      bankId: "",
      total: 0,
    };
  },
  onLoad(options) {
    this.bankId = options.bankId;
  },
  methods: {
    loadData(params, callback) {
      this.$reqGet("/front/edu/user-answer/page", {
        ...params,
        bankId: this.bankId,
        searcher: {
          EQ_isExam: true,
          EQ_type: 5,
          EQ_mainId: 0,
          EQ_closed: true,
        },
      }).then((res) => {
        callback(res.data);
        this.total = res.data.total;
      });
    },
    toResult(item) {
      uni.navigateTo({
        url: `./result?answerId=${item.id}`,
      });
    },
    formatDuration(seconds) {
      if (!seconds && seconds !== 0) return "--";
      const m = Math.floor(seconds / 60);
      const s = seconds % 60;
      return `${m > 0 ? m + "分" : ""}${s}秒`;
    },
  },
};
</script>

<style scoped>
/* 你可以根据主色微调 .bg-primary-50 .text-primary-600 .text-primary-500 */
</style>
