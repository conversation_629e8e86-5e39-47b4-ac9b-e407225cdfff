<template>
  <view class="h-screen bg-gray-50 flex flex-col">
    <scroll-pagination
      class="flex-1 overflow-hidden"
      :page-size="12"
      :auto-load="true"
      ref="scrollPagination"
      :enable-refresh="true"
      @load="loadData"
    >
      <template v-slot="{ list }">
        <view class="px-3 py-2">
          <view
            v-for="item in list"
            :key="item.id"
            class="mb-3 rounded-2xl bg-white shadow hover:shadow-lg transition-shadow border border-gray-100 p-0.5 overflow-hidden relative group"
            @click="toResult(item)"
          >
            <!-- 状态标签 -->
            <view
              class="absolute top-0 right-0 m-3 px-2 py-0.5 rounded-full text-xs font-bold z-10"
              :class="
                item.score >= item.passScore
                  ? 'bg-green-100 text-green-700'
                  : 'bg-red-100 text-red-600'
              "
            >
              {{ item.score >= item.passScore ? "已通过" : "未通过" }}
            </view>
            <!-- 主体内容 -->
            <view class="px-5 pt-5 pb-3">
              <view class="flex items-center mb-2">
                <text
                  class="text-lg font-bold text-primary-600 truncate flex-1"
                >
                  {{ item.name || "模拟考试" }}
                </text>
              </view>
              <view class="flex items-end space-x-2 mb-2">
                <text class="text-2xl font-extrabold text-primary-500">{{
                  item.score
                }}</text>
                <text class="text-base text-gray-400 pb-0.5"
                  >/ {{ item.totalScore }}</text
                >
              </view>
              <view class="flex items-center space-x-4 mb-1">
                <text class="text-xs text-gray-500"
                  >及格分：{{ item.passScore }}</text
                >
                <text class="text-xs text-gray-400"
                  >用时：{{ formatDuration(item.spentDuration) }}</text
                >
              </view>
              <view class="flex items-center justify-between">
                <text class="text-xs text-gray-400"
                  >交卷时间：{{ item.closeTime }}</text
                >
              </view>
            </view>
            <!-- 悬浮效果 -->
            <view
              class="absolute inset-0 bg-primary-50 opacity-0 group-hover:opacity-10 transition-opacity pointer-events-none"
            ></view>
          </view>
        </view>
      </template>
      <template #empty>
        <view
          class="py-12 px-4 bg-white rounded-xl shadow-sm flex flex-col items-center justify-center"
        >
          <view
            class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3"
          >
            <text class="fas fa-history text-gray-300 text-2xl"></text>
          </view>
          <text class="text-gray-500 text-sm">暂无模拟考试记录</text>
        </view>
      </template>
    </scroll-pagination>
  </view>
</template>

<script>
export default {
  data() {
    return {
      bankId: "",
      total: 0,
    };
  },
  onLoad(options) {
    this.bankId = options.bankId;
  },
  methods: {
    loadData(params, callback) {
      this.$reqGet("/front/edu/user-answer/page", {
        ...params,
        bankId: this.bankId,
        searcher: {
          EQ_isExam: true,
          EQ_type: 5,
          EQ_mainId: 0,
          EQ_closed: true,
        },
      }).then((res) => {
        callback(res.data);
        this.total = res.data.total;
      });
    },
    toResult(item) {
      uni.navigateTo({
        url: `./result?answerId=${item.id}`,
      });
    },
    formatDuration(seconds) {
      if (!seconds && seconds !== 0) return "--";
      const m = Math.floor(seconds / 60);
      const s = seconds % 60;
      return `${m > 0 ? m + "分" : ""}${s}秒`;
    },
  },
};
</script>

<style scoped>
/* 你可以根据主色微调 .bg-primary-50 .text-primary-600 .text-primary-500 */
</style>
