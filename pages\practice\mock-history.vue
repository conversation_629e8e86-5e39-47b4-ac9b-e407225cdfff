<template>
  <view class="h-screen bg-gray-50 flex flex-col">
    <scroll-pagination
      class="flex-1 overflow-hidden"
      :page-size="12"
      :auto-load="true"
      ref="scrollPagination"
      :enable-refresh="true"
      @load="loadData"
    >
      <template v-slot="{ list }">
        <view class="px-3 py-2">
          <view
            v-for="item in list"
            :key="item.id"
            class="mb-4 bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden relative"
            @click="toResult(item)"
          >
            <!-- 顶部装饰条 -->
            <view
              class="h-1 w-full"
              :class="
                item.score >= item.passScore
                  ? 'bg-gradient-to-r from-green-400 to-green-500'
                  : 'bg-gradient-to-r from-red-400 to-red-500'
              "
            ></view>

            <!-- 主体内容 -->
            <view class="p-5">
              <!-- 头部：标题和状态 -->
              <view class="flex items-start justify-between mb-4">
                <view class="flex-1 mr-3">
                  <text class="text-lg font-bold text-gray-800 leading-tight">
                    {{ item.name || "模拟考试" }}
                  </text>
                  <text class="text-xs text-gray-400 mt-1 block">
                    {{ item.closeTime }}
                  </text>
                </view>
                <view
                  class="px-3 py-1 rounded-full text-xs font-medium flex items-center"
                  :class="
                    item.score >= item.passScore
                      ? 'bg-green-50 text-green-600 border border-green-200'
                      : 'bg-red-50 text-red-600 border border-red-200'
                  "
                >
                  <view
                    class="w-1.5 h-1.5 rounded-full mr-1.5"
                    :class="
                      item.score >= item.passScore
                        ? 'bg-green-500'
                        : 'bg-red-500'
                    "
                  ></view>
                  {{ item.score >= item.passScore ? "已通过" : "未通过" }}
                </view>
              </view>

              <!-- 分数展示区域 -->
              <view class="bg-gray-50 rounded-xl p-4 mb-4">
                <view class="flex items-center justify-between">
                  <view class="flex items-baseline">
                    <text class="text-3xl font-bold text-gray-800">{{
                      item.score
                    }}</text>
                    <text class="text-lg text-gray-400 ml-1"
                      >/ {{ item.totalScore }}</text
                    >
                  </view>
                  <view class="text-right">
                    <text class="text-sm text-gray-500 block">得分率</text>
                    <text
                      class="text-lg font-bold"
                      :class="
                        item.score >= item.passScore
                          ? 'text-green-600'
                          : 'text-red-500'
                      "
                    >
                      {{ Math.round((item.score / item.totalScore) * 100) }}%
                    </text>
                  </view>
                </view>
              </view>

              <!-- 详细信息 -->
              <view
                class="flex items-center justify-between text-xs text-gray-500"
              >
                <view class="flex items-center">
                  <i class="fas fa-target mr-1 text-gray-400"></i>
                  <text>及格分 {{ item.passScore }}</text>
                </view>
                <view class="flex items-center">
                  <i class="fas fa-clock mr-1 text-gray-400"></i>
                  <text>用时 {{ formatDuration(item.spentDuration) }}</text>
                </view>
                <view class="flex items-center">
                  <i class="fas fa-chevron-right text-gray-300"></i>
                </view>
              </view>
            </view>
          </view>
        </view>
      </template>
      <template #empty>
        <view
          class="py-12 px-4 bg-white rounded-xl shadow-sm flex flex-col items-center justify-center"
        >
          <view
            class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3"
          >
            <text class="fas fa-history text-gray-300 text-2xl"></text>
          </view>
          <text class="text-gray-500 text-sm">暂无模拟考试记录</text>
        </view>
      </template>
    </scroll-pagination>
  </view>
</template>

<script>
export default {
  data() {
    return {
      bankId: "",
      total: 0,
    };
  },
  onLoad(options) {
    this.bankId = options.bankId;
  },
  methods: {
    loadData(params, callback) {
      this.$reqGet("/front/edu/user-answer/page", {
        ...params,
        bankId: this.bankId,
        searcher: {
          EQ_isExam: true,
          EQ_type: 5,
          EQ_mainId: 0,
          EQ_closed: true,
        },
      }).then((res) => {
        callback(res.data);
        this.total = res.data.total;
      });
    },
    toResult(item) {
      uni.navigateTo({
        url: `./result?answerId=${item.id}`,
      });
    },
    formatDuration(seconds) {
      if (!seconds && seconds !== 0) return "--";
      const m = Math.floor(seconds / 60);
      const s = seconds % 60;
      return `${m > 0 ? m + "分" : ""}${s}秒`;
    },
  },
};
</script>

<style scoped>
/* 你可以根据主色微调 .bg-primary-50 .text-primary-600 .text-primary-500 */
</style>
