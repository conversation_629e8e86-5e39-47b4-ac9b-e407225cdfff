<template>
  <view class="min-h-screen bg-gray-50">
    <!-- 用户信息 -->
    <view class="bg-white px-4 py-6">
      <view class="flex items-center space-x-4">
        <image 
          :src="userInfo.avatar" 
          class="w-16 h-16 rounded-full"
          mode="aspectFill"
        />
        <view class="flex-1">
          <text class="text-lg font-bold text-gray-800 block">{{ userInfo.nickname }}</text>
          <text class="text-sm text-gray-500">{{ userInfo.phone }}</text>
          <view class="flex items-center mt-1">
            <view class="px-2 py-1 bg-primary-100 text-primary-600 rounded-full text-xs mr-2">
              {{ userInfo.role }}
            </view>
            <text class="text-xs text-gray-400">注册时间：{{ userInfo.registerTime }}</text>
          </view>
        </view>
        <button @click="editProfile" class="px-3 py-1 border border-gray-200 text-gray-600 rounded-lg text-sm">
          编辑
        </button>
      </view>
    </view>

    <!-- 统计数据 -->
    <view class="px-4 py-4">
      <view class="bg-white rounded-xl p-4 shadow-sm mb-4">
        <text class="text-lg font-bold text-gray-800 mb-4 block">我的数据</text>
        <view class="grid grid-cols-2 gap-4">
          <view class="text-center">
            <text class="text-2xl font-bold text-primary-600 block">{{ statistics.questionBankCount }}</text>
            <text class="text-sm text-gray-500">创建题库</text>
          </view>
          <view class="text-center">
            <text class="text-2xl font-bold text-primary-600 block">{{ statistics.joinedBankCount }}</text>
            <text class="text-sm text-gray-500">加入题库</text>
          </view>
          <view class="text-center">
            <text class="text-2xl font-bold text-blue-600 block">{{ statistics.practiceCount }}</text>
            <text class="text-sm text-gray-500">练习次数</text>
          </view>
          <view class="text-center">
            <text class="text-2xl font-bold text-purple-600 block">{{ statistics.correctRate }}%</text>
            <text class="text-sm text-gray-500">正确率</text>
          </view>
        </view>
      </view>

      <!-- 功能菜单 -->
      <view class="bg-white rounded-xl p-4 shadow-sm mb-4">
        <text class="text-lg font-bold text-gray-800 mb-4 block">功能菜单</text>
        <view class="space-y-3">
          <button 
            v-for="menu in menuItems" 
            :key="menu.id"
            @click="handleMenuClick(menu)"
            class="flex items-center justify-between w-full p-3 border border-gray-100 rounded-lg"
          >
            <view class="flex items-center">
              <view :class="[
                'w-8 h-8 rounded-full flex items-center justify-center mr-3',
                menu.bgColor
              ]">
                <text :class="['text-sm', menu.iconColor, menu.icon]"></text>
              </view>
              <view>
                <text class="text-sm font-medium text-gray-800 block">{{ menu.title }}</text>
                <text v-if="menu.subtitle" class="text-xs text-gray-500">{{ menu.subtitle }}</text>
              </view>
            </view>
            <text class="fas fa-chevron-right text-gray-400 text-sm"></text>
          </button>
        </view>
      </view>

      <!-- 学习记录 -->
      <view class="bg-white rounded-xl p-4 shadow-sm mb-4">
        <view class="flex items-center justify-between mb-4">
          <text class="text-lg font-bold text-gray-800">最近练习</text>
          <button @click="viewAllRecords" class="text-sm text-primary-600">
            查看全部 <text class="fas fa-chevron-right ml-1"></text>
          </button>
        </view>
        
        <view v-if="recentPractices.length === 0" class="text-center py-8">
          <view class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
            <text class="fas fa-history text-gray-300 text-2xl"></text>
          </view>
          <text class="text-gray-500 text-sm">暂无练习记录</text>
        </view>
        
        <view v-else class="space-y-3">
          <view 
            v-for="practice in recentPractices" 
            :key="practice.id"
            class="flex items-center justify-between p-3 border border-gray-100 rounded-lg"
          >
            <view class="flex-1">
              <text class="text-sm font-medium text-gray-800 block">{{ practice.bankName }}</text>
              <view class="flex items-center mt-1">
                <text class="text-xs text-gray-500 mr-3">{{ practice.mode }}</text>
                <text class="text-xs text-gray-500 mr-3">{{ practice.questionCount }}题</text>
                <text :class="[
                  'text-xs',
                  practice.score >= 80 ? 'text-primary-600' : practice.score >= 60 ? 'text-yellow-600' : 'text-red-600'
                ]">{{ practice.score }}分</text>
              </view>
              <text class="text-xs text-gray-400">{{ practice.practiceTime }}</text>
            </view>
            <button @click="viewPracticeDetail(practice)" class="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
              查看
            </button>
          </view>
        </view>
      </view>

      <!-- 设置选项 -->
      <view class="bg-white rounded-xl p-4 shadow-sm">
        <text class="text-lg font-bold text-gray-800 mb-4 block">设置</text>
        <view class="space-y-3">
          <view class="flex items-center justify-between">
            <text class="text-sm text-gray-800">消息通知</text>
            <switch 
              :checked="settings.notification" 
              @change="toggleNotification"
              color="#007AFF"
            />
          </view>
          
          <view class="flex items-center justify-between">
            <text class="text-sm text-gray-800">声音提示</text>
            <switch 
              :checked="settings.sound" 
              @change="toggleSound"
              color="#007AFF"
            />
          </view>
          
          <button @click="clearCache" class="flex items-center justify-between w-full py-2">
            <text class="text-sm text-gray-800">清除缓存</text>
            <text class="text-xs text-gray-500">{{ cacheSize }}</text>
          </button>
          
          <button @click="checkUpdate" class="flex items-center justify-between w-full py-2">
            <text class="text-sm text-gray-800">检查更新</text>
            <text class="text-xs text-gray-500">v1.0.0</text>
          </button>
          
          <button @click="about" class="flex items-center justify-between w-full py-2">
            <text class="text-sm text-gray-800">关于我们</text>
            <text class="fas fa-chevron-right text-gray-400 text-sm"></text>
          </button>
          
          <button @click="logout" class="w-full py-3 bg-red-500 text-white rounded-lg text-sm font-medium mt-4">
            退出登录
          </button>
        </view>
      </view>
    </view>

    <!-- 底部安全区域 -->
    <view class="h-20"></view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userInfo: {
        avatar: '/static/images/avatar.png',
        nickname: '张三',
        phone: '138****8888',
        role: '学习者',
        registerTime: '2024-01-01'
      },
      statistics: {
        questionBankCount: 5,
        joinedBankCount: 12,
        practiceCount: 156,
        correctRate: 85
      },
      menuItems: [
        {
          id: 'wrongQuestions',
          title: '错题本',
          subtitle: '查看和复习错题',
          icon: 'fas fa-times-circle',
          iconColor: 'text-red-600',
          bgColor: 'bg-red-100'
        },
        {
          id: 'favorites',
          title: '我的收藏',
          subtitle: '收藏的题目和题库',
          icon: 'fas fa-heart',
          iconColor: 'text-pink-600',
          bgColor: 'bg-pink-100'
        },
        {
          id: 'notes',
          title: '学习笔记',
          subtitle: '记录学习心得',
          icon: 'fas fa-sticky-note',
          iconColor: 'text-yellow-600',
          bgColor: 'bg-yellow-100'
        },
        {
          id: 'achievements',
          title: '成就徽章',
          subtitle: '查看学习成就',
          icon: 'fas fa-trophy',
          iconColor: 'text-yellow-600',
          bgColor: 'bg-yellow-100'
        },
        {
          id: 'feedback',
          title: '意见反馈',
          subtitle: '帮助我们改进',
          icon: 'fas fa-comment',
          iconColor: 'text-blue-600',
          bgColor: 'bg-blue-100'
        }
      ],
      recentPractices: [
        {
          id: '1',
          bankName: 'JavaScript基础题库',
          mode: '随机练习',
          questionCount: 20,
          score: 85,
          practiceTime: '2024-01-15 14:30'
        },
        {
          id: '2',
          bankName: 'Vue.js进阶题库',
          mode: '章节练习',
          questionCount: 15,
          score: 92,
          practiceTime: '2024-01-14 10:15'
        },
        {
          id: '3',
          bankName: 'CSS布局题库',
          mode: '题型练习',
          questionCount: 25,
          score: 78,
          practiceTime: '2024-01-13 16:45'
        }
      ],
      settings: {
        notification: true,
        sound: false
      },
      cacheSize: '12.5MB'
    }
  },
  onLoad() {
    this.loadUserInfo();
    this.loadStatistics();
    this.loadRecentPractices();
  },
  methods: {
    loadUserInfo() {
      // 模拟加载用户信息
      // 实际项目中这里应该调用API
    },
    
    loadStatistics() {
      // 模拟加载统计数据
      // 实际项目中这里应该调用API
    },
    
    loadRecentPractices() {
      // 模拟加载最近练习记录
      // 实际项目中这里应该调用API
    },
    
    editProfile() {
      uni.navigateTo({
        url: '/pages/profile/edit'
      });
    },
    
    handleMenuClick(menu) {
      switch (menu.id) {
        case 'wrongQuestions':
          uni.navigateTo({
            url: '/pages/study/wrong-questions'
          });
          break;
        case 'favorites':
          uni.navigateTo({
            url: '/pages/study/favorites'
          });
          break;
        case 'notes':
          uni.navigateTo({
            url: '/pages/study/notes'
          });
          break;
        case 'achievements':
          uni.navigateTo({
            url: '/pages/study/achievements'
          });
          break;
        case 'feedback':
          uni.navigateTo({
            url: '/pages/feedback/index'
          });
          break;
        default:
          uni.showToast({
            title: '功能开发中',
            icon: 'none'
          });
      }
    },
    
    viewAllRecords() {
      uni.navigateTo({
        url: '/pages/study/practice-records'
      });
    },
    
    viewPracticeDetail(practice) {
      uni.navigateTo({
        url: `/pages/study/practice-detail?id=${practice.id}`
      });
    },
    
    toggleNotification(e) {
      this.settings.notification = e.detail.value;
      this.saveSettings();
    },
    
    toggleSound(e) {
      this.settings.sound = e.detail.value;
      this.saveSettings();
    },
    
    saveSettings() {
      // 保存设置到本地存储
      uni.setStorageSync('userSettings', this.settings);
      uni.showToast({
        title: '设置已保存',
        icon: 'success'
      });
    },
    
    clearCache() {
      uni.showModal({
        title: '清除缓存',
        content: '确定要清除所有缓存数据吗？',
        success: (res) => {
          if (res.confirm) {
            // 清除缓存逻辑
            uni.clearStorageSync();
            this.cacheSize = '0MB';
            uni.showToast({
              title: '缓存已清除',
              icon: 'success'
            });
          }
        }
      });
    },
    
    checkUpdate() {
      uni.showLoading({
        title: '检查中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '已是最新版本',
          icon: 'success'
        });
      }, 1500);
    },
    
    about() {
      uni.navigateTo({
        url: '/pages/about/index'
      });
    },
    
    logout() {
      uni.showModal({
        title: '退出登录',
        content: '确定要退出当前账号吗？',
        success: (res) => {
          if (res.confirm) {
            // 清除用户信息
            uni.removeStorageSync('userInfo');
            uni.removeStorageSync('token');
            
            // 跳转到登录页
            uni.reLaunch({
              url: '/pages/login/login'
            });
          }
        }
      });
    }
  }
}
</script>

<style scoped>
 
</style>