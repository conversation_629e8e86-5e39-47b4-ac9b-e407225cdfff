const key = "globalSetting";
let gtorageGlobalSettingState = uni.getStorageSync(key) || {
	setting: {
		navBar: undefined,
		theme: undefined
	},
	// themeStyle: {},
	loading: true
};



export const globalSetting = {
	namespaced: true,
	state: () => ({
		// ...defaultGlobalSetting,
		...gtorageGlobalSettingState,
		// setting: defaultGlobalSetting,
		// themeStyle: {
		// 	'--blue': defaultGlobalSetting?.theme?.colorPrimary || ''
		// },
		// loading: true
	}),
	mutations: {
		update(state, setting) {
			const stateValue = {
				setting,
				loading: false,
				// themeStyle: {

				// }
			};
			uni.setStorageSync(key, stateValue);
			state.setting = stateValue.setting;
			// console.log(state.setting)
			state.loading = false;
			// state.setting = setting;
			// state.loading = false;
			// state.themeStyle = {
			// 	'--blue': setting?.theme?.colorPrimary
			// };
			// console.log(state.themeStyle)


		},
	}

}