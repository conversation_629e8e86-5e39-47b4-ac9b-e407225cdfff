<template>
	<view class="privacy-container">
		<view class="header">
			<text class="title">隐私政策</text>
		</view>
		
		<scroll-view class="content" scroll-y="true">
			<view class="section">
				<text class="section-title">1. 我们收集的信息</text>
				<text class="section-content">
					为了向您提供更好的服务，我们可能会收集以下信息：
					• 账户信息：手机号码、微信授权信息
					• 设备信息：设备型号、操作系统版本、设备标识符
					• 使用信息：学习记录、练习数据、访问日志
				</text>
			</view>
			
			<view class="section">
				<text class="section-title">2. 信息使用方式</text>
				<text class="section-content">
					我们使用收集的信息来：
					• 提供、维护和改进我们的服务
					• 个性化您的学习体验
					• 与您沟通，包括发送服务通知
					• 保护我们的服务安全
				</text>
			</view>
			
			<view class="section">
				<text class="section-title">3. 信息共享</text>
				<text class="section-content">
					我们不会向第三方出售、出租或以其他方式披露您的个人信息，除非：
					• 获得您的明确同意
					• 法律法规要求
					• 保护我们的合法权益
				</text>
			</view>
			
			<view class="section">
				<text class="section-title">4. 信息安全</text>
				<text class="section-content">
					我们采用行业标准的安全措施来保护您的个人信息：
					• 数据加密传输和存储
					• 访问控制和权限管理
					• 定期安全审计和监控
				</text>
			</view>
			
			<view class="section">
				<text class="section-title">5. 您的权利</text>
				<text class="section-content">
					您对自己的个人信息享有以下权利：
					• 访问和更新您的个人信息
					• 删除您的账户和相关数据
					• 撤回您的同意
					• 数据可携带权
				</text>
			</view>
			
			<view class="section">
				<text class="section-title">6. Cookie和类似技术</text>
				<text class="section-content">
					我们使用Cookie和类似技术来改善用户体验，包括：
					• 记住您的登录状态
					• 分析服务使用情况
					• 提供个性化内容
				</text>
			</view>
			
			<view class="section">
				<text class="section-title">7. 儿童隐私</text>
				<text class="section-content">
					我们非常重视儿童的隐私保护。如果您未满18岁，请在家长或监护人的指导下使用我们的服务。
				</text>
			</view>
			
			<view class="section">
				<text class="section-title">8. 政策更新</text>
				<text class="section-content">
					我们可能会不时更新本隐私政策。重大变更时，我们会通过适当方式通知您。
				</text>
			</view>
			
			<view class="section">
				<text class="section-title">9. 联系我们</text>
				<text class="section-content">
					如果您对本隐私政策有任何疑问或建议，请通过以下方式联系我们：
					• 应用内客服功能
					• 邮箱：<EMAIL>
				</text>
			</view>
		</scroll-view>
		
		<view class="footer">
			<button class="confirm-btn" @click="goBack">我已阅读并理解</button>
		</view>
	</view>
</template>

<script>
export default {
	name: 'PrivacyPolicy',
	methods: {
		goBack() {
			uni.navigateBack();
		}
	}
}
</script>

<style scoped>
.privacy-container {
	height: 100vh;
	background-color: #f8f9fa;
	display: flex;
	flex-direction: column;
}

.header {
	background-color: #007AFF;
	padding: 44px 20px 20px;
	text-align: center;
}

.title {
	color: white;
	font-size: 18px;
	font-weight: bold;
}

.content {
	flex: 1;
	padding: 20px;
}

.section {
	background-color: white;
	margin-bottom: 15px;
	padding: 20px;
	border-radius: 8px;
}

.section-title {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10px;
}

.section-content {
	font-size: 14px;
	color: #666;
	line-height: 1.6;
	display: block;
	white-space: pre-line;
}

.footer {
	padding: 20px;
	background-color: white;
	border-top: 1px solid #eee;
}

.confirm-btn {
	width: 100%;
	height: 44px;
	background-color: #007AFF;
	color: white;
	border: none;
	border-radius: 22px;
	font-size: 16px;
	font-weight: bold;
}
</style>