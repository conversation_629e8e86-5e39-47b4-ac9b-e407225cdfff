<template>
	<view class="min-h-screen bg-gray-50 pb-20">
		<answer-card :questionList="dataList" @indexClick="indexClick" :showResult="showResult"></answer-card>
		<template v-if="canSubmit">
			<view class="h-16">
				<view class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 flex items-center justify-center h-16 shadow">
					<view class="flex-1 flex items-center justify-center">
						<view class="px-4 w-full">
							<button class="w-full bg-blue-500 text-white py-2 rounded-lg text-lg font-semibold shadow hover:bg-blue-600 transition flex items-center justify-center" @click="submit">
								<i class="fas fa-paper-plane mr-2"></i>交卷
							</button>
						</view>
					</view>
				</view>
			</view>
		</template>
	</view>
</template>

<script>
 
	export default {
	 
		data() {
			return {
				showResult: false,
				canSubmit: false,
				dataList: [],
				title: '答题卡'
			}
		},
		onLoad() {
			const {
				questionList,
				index,
				isExam,
				readonly
			} =
			getApp().globalData.cardData;

			this.dataList = JSON.parse(JSON.stringify(questionList));
			const title = `答题卡 ${index + 1} / ${questionList.length}`;
			uni.setNavigationBarTitle({
				title
			});
			this.title = title;
			this.showResult = readonly || !isExam;
			this.canSubmit = !readonly;
			console.log(this.dataList)
		},
		methods: {
			indexClick(index) {
				uni.navigateBack();
				uni.$emit("updateIndex", index);
			},
			submit() {
				uni.navigateBack();
				uni.$emit("submitClose");
			}
		}
	}
</script>

<style>
 
</style>