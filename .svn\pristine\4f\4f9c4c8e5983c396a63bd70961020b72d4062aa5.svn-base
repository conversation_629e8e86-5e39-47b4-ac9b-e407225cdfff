<template>
	<view class="flex flex-col h-screen bg-gray-50">
		<!-- 添加/编辑章节弹窗 -->
		<view v-if="!!formModel" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60">
			<view class="bg-white rounded-2xl w-11/12 max-w-md overflow-hidden shadow-xl transform transition-all">
				<!-- 弹窗标题栏 -->
				<view class="flex items-center justify-between px-4 py-3 border-b border-gray-100">
					<view class="text-xl font-medium text-gray-900">{{!formModel.id ? '添加' : '编辑'}}章节</view>
					<view @click="formModel=null" class="p-2 rounded-full hover:bg-gray-100 transition-colors">
						<text class="fas fa-times text-gray-500"></text>
					</view>
				</view>
				
				<!-- 弹窗内容 -->
				<view class="p-5">
					<view class="mb-5">
						
						<text class="text-sm font-medium text-gray-700 block mb-2">章节名称 <text class="text-red-500">*</text></text>
						<view class="flex items-center">
							<view class="flex-1 relative">
								<input 
									placeholder="请输入章节名称" 
									class=" h-auto p-3 border border-solid border-gray-300 rounded-lg text-gray-800 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all" 
									maxlength="15" 
									v-model="formModel.name" 
									auto-focus="true" 
								/>
								<text class="absolute right-3 top-3 text-gray-400 text-sm">{{nameLength}}/15</text>
							</view>
						</view>
					</view>
					
			 
					<button @click="onSave" class="w-full mt-6 py-3.5 bg-primary-500 text-white rounded-xl text-sm font-medium">
          <text class="fas fa-save mr-2"></text>
		  确认保存
        </button>
				</view>
			</view>
		</view>

		<!-- 顶部操作栏 -->
		<view class="p-4 bg-white shadow-sm flex items-center justify-between sticky top-0 z-10">
			<text class="text-lg font-medium text-gray-900">章节管理</text>
			<view v-if="list.length > 0">
				<button
				class="text-sm p-2 bg-primary-500 text-white rounded-lg shadow-sm flex items-center space-x-1 transition-colors hover:bg-primary-600" 
				@click="showAdd()"
			>
				<text class="fas fa-plus text-sm"></text>
				<text>添加一级章节</text>
			</button>
			</view>
		</view>

		<!-- 空状态 -->
		<view v-if="list.length === 0" class="flex-1 flex flex-col items-center justify-center px-4 py-12">
			<text class="fas fa-book-open text-5xl text-gray-300 mb-4"></text>
			<view class="text-gray-500 text-center mb-6">暂无章节内容</view>
			<button
				class="px-5 py-3 text-sm bg-primary-500 text-white rounded-lg shadow-sm flex items-center space-x-1 transition-colors hover:bg-primary-600" 
				@click="showAdd()"
			>
				<text class="fas fa-plus text-sm"></text>
				<text>添加一级章节</text>
			</button>
		</view>

		<!-- 内容区域 -->
		<scroll-view v-else class="flex-1 px-4 py-3" scroll-y="true">
			<view v-for="(item, index) in list" :key="item.id" class="mb-3">
				<view class="bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100">
					<!-- 一级章节 -->
					<view 
						class="flex items-center justify-between p-4 transition-colors" 
						:class="{'bg-gray-50': checkIds.indexOf(item.id) >= 0}"
						@tap="onCheck(item)"
					>
						<view class="flex items-center">
							<view class="w-6 h-6 rounded-full bg-primary-100 text-primary-600 flex items-center justify-center mr-3">
								<text class="text-xs font-medium">{{index + 1}}</text>
							</view>
							<view class="text-base font-medium text-gray-800">{{ item.name }}</view>
							<view class="ml-2 w-5 h-5 rounded-full bg-gray-100 flex items-center justify-center transition-transform"
								:class="{'rotate-90': checkIds.indexOf(item.id) < 0}">
								<text class="text-xs text-gray-500" :class="checkIds.indexOf(item.id) >= 0 ? 'fas fa-chevron-down' : 'fas fa-chevron-right'"></text>
							</view>
						</view>
						
						<view class="flex items-center">
							<button @click.stop="showAddChild(item)" class="mr-1 p-2 text-sm text-primary-600 hover:bg-primary-50 rounded-lg transition-colors">
								<text class="fas fa-plus-circle mr-1"></text>子章节
							</button>
							<view class="flex space-x-1">
								<button @click.stop="onUp(item)" class="p-2 text-gray-500 hover:bg-gray-100 rounded-full transition-colors">
									<text class="fas fa-arrow-up"></text>
								</button>
								<button @click.stop="onDown(item)" class="p-2 text-gray-500 hover:bg-gray-100 rounded-full transition-colors">
									<text class="fas fa-arrow-down"></text>
								</button>
								<button @click.stop="showMore(item)" class="p-2 text-gray-500 hover:bg-gray-100 rounded-full transition-colors">
									<text class="fas fa-ellipsis-v"></text>
								</button>
							</view>
						</view>
					</view>

					<!-- 二级章节 -->
					<view 
						v-if="checkIds.indexOf(item.id) >= 0 && item.children && item.children.length > 0" 
						class="border-t border-gray-100 bg-white"
					>
						<view 
							v-for="(childItem, childIndex) in item.children" 
							:key="childItem.id"
							class="flex items-center justify-between p-4 pl-12 border-b border-gray-50 last:border-b-0 transition-colors hover:bg-gray-50"
						>
							<view class="flex items-center">
								<view class="w-5 h-5 rounded-full bg-gray-100 text-gray-600 flex items-center justify-center mr-3">
									<text class="text-xs font-medium">{{childIndex + 1}}</text>
								</view>
								<view class="text-base text-gray-700">{{ childItem.name }}</view>
							</view>
							
							<view class="flex space-x-1">
								<button @click="onUp(childItem)" class="p-2 text-gray-500 hover:bg-gray-100 rounded-full transition-colors">
									<text class="fas fa-arrow-up"></text>
								</button>
								<button @click="onDown(childItem)" class="p-2 text-gray-500 hover:bg-gray-100 rounded-full transition-colors">
									<text class="fas fa-arrow-down"></text>
								</button>
								<button @click="showMore(childItem)" class="p-2 text-gray-500 hover:bg-gray-100 rounded-full transition-colors">
									<text class="fas fa-ellipsis-v"></text>
								</button>
							</view>
						</view>
					</view>
					
					<!-- 无子章节提示 -->
					<view 
						v-else-if="checkIds.indexOf(item.id) >= 0 && (!item.children || item.children.length === 0)" 
						class="p-4 text-sm   text-center text-gray-500 border-t border-gray-100 bg-gray-50 text-sm"
					>
						暂无子章节，点击"子章节"按钮添加
					</view>
				</view>
			</view>
			
			<!-- 底部留白 -->
			<view class="h-4"></view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [],
				checkIds: [],
				formModel: undefined,
				bankId: undefined,
				hasChanges: false // 标记是否有变更
			}
		},
		onLoad(options) {
			this.bankId = options.bankId;
			this.loadList()
		},
		// 页面即将退出时触发
		onUnload() {
			// 如果有变更，通知更新章节列表
			if (this.hasChanges) {
				// const eventChannel = this.getOpenerEventChannel();
				// if (eventChannel && eventChannel.emit) {
				// 	eventChannel.emit('returnFromChapterManagement');
				// 	console.log('已通知更新章节列表');
				// }
				// 全局通知，以防万一
				uni.$emit('refreshChapterList');
			}
		},
		computed: {
			nameLength() {
				const formModel = this.formModel;
				if (formModel && formModel.name) {
					return formModel.name.length;
				}
				return 0;
			}
		},
		methods: {
			loadList() {
				this.$reqGet('/front/edu-personal/chapter/list/'+this.bankId).then((res) => {
					this.list = res.data || [];
				})
			},
			onCheck(item) {
				const i = this.checkIds.indexOf(item.id);
				if (i < 0) {
					this.checkIds.push(item.id)
				} else {
					this.checkIds.splice(i, 1)
				}
			},
			showAdd() {
				this.formModel = {
					name: '',
          bankId: this.bankId
				}
			},
			showAddChild(item) {
				this.formModel = {
					name: '',
					parentId: item.id,
          bankId: this.bankId
				}
			},
			showEdit(item) {
				this.formModel = {
					name: item.name,
					id: item.id,
          bankId: this.bankId
				}
			},
			onSave() {
				const {id, name, parentId, bankId} = this.formModel;
				if(!name || !name.length) {
					uni.showToast({
            title: '名称不能为空',
            icon:'none'
          })
					return;
				}
				this.$reqPost(`/front/edu-personal/chapter/${id?'update':'create'}`, {name, parentId, id, bankId}, true, '保存中...').then(res => {
					if(res.success) {
            uni.showToast({
              title: '保存成功',
              icon:'success'
            })
						this.formModel = null;
						this.loadList();
						this.hasChanges = true; // 标记有变更
					} else {
            uni.showToast({
              title: res.errorMessage || '系统繁忙',
              icon:'none'
            })
					}
				})
			},
			showMore(item) {
				uni.showActionSheet({
					title: '更多操作',
					itemList: ['编辑', '删除'],
					success: (res) => {
						if (res.tapIndex == 0) {
							this.showEdit(item)
						} else if (res.tapIndex == 1) {
              uni.showModal({
                title: '删除确认',
                content: '确认删除当前章节吗？',
                success: (res) => {
                  if (res.confirm) {
                    this.$reqPost('/front/edu-personal/chapter/delete', {
                      id: item.id
                    }, true, '请求中...').then(res => {
                      if (res.success) {
                        uni.showToast({
                          title: '删除成功',
                          icon: 'success'
                        })
                        this.loadList()
                        this.hasChanges = true; // 标记有变更
                      } else {
                        uni.showToast({
                          title: res.errorMessage || '系统繁忙',
                          icon: 'none'
                        })
                      }
                    })
                  }
                }
              })
						}
					}
				})
			},
			onUp(item) {
				this.$reqGet('/front/edu-personal/chapter/up', {
					id: item.id
				}).then(res => {
					if (res.success) {
						uni.showToast({
							title: '上移成功',
							icon: 'success'
						})
						this.loadList()
						this.hasChanges = true; // 标记有变更
					} else {
						uni.showToast({
							title: res.errorMessage || '系统繁忙',
							icon: 'none'
						})
					}
				})
			},
			onDown(item) {
				this.$reqGet('/front/edu-personal/chapter/down', {
					id: item.id
				}).then(res => {
					if (res.success) {
						uni.showToast({
							title: '下移成功',
							icon: 'success'
						})
						this.loadList()
						this.hasChanges = true; // 标记有变更
					} else {
						uni.showToast({
							title: res.errorMessage || '系统繁忙',
							icon: 'none'
						})
					}
				})
			}
		}
	}
</script>

<style>
/* 不需要额外的样式，所有样式都通过Tailwind类实现 */
</style>