#本前端微信小程序项目使用技术uniapp(vue3)选项式+tailwindcss3.4.17+font-awesome6.5.1实现，少写自定义style

pages/login/login用户可使用手机号快速登录，代码已写好逻辑，只要帮我把样式实现。

这个小程序我想要的功能要有：题库管理、组织考试、题库功能、学员考试功能、群组等功能。


 


题库：
	新建/编辑题库：题库名称（必填）、题库描述、是否可搜索（为是时在题库市场会搜索到）
    题库加入权限设置：是否免费、加入方式：（密码、激活码） 否时：可设置密码（密码可一直使用）、可设置激活码（一人只能使用一次）（你看能否你给点建议）


	置顶题库：可对我的题库进行置顶，有可能是我自己创建的，也可能是我加入的。
	 
	添加试题：可单题录入、批量导入。
    试题的字段：题干、题型（单选题、多选题、判断题、填空题、不定项选择题）、所属章节（最多二级章节）、解析、难度（易、偏易、适中、偏难、难）、正确答案
	编辑试题：对单个试题进行编辑
	编辑章节：章节字段：（章节名称）,对章节进行管理（添加、上移、下移、重命名）

	试题去重功能
	删除题库：删除后可在回收站恢复
	分享题库：用户可分享题库给其他人，其他人可加入题库进行练习
组织考试：
	创建考试：考试名称、考试说明、考生设置（指定考生（（姓名+准考证、姓名+手机号、姓名+手机号+验证码、姓名+身份证号、姓名+工号、姓名+学号））、指定群组、所有人都可考试（可管理录入的字段：姓名、手机号等））、及格分数、开始时间-结束时间（可不限）、考试时长、设置考题（可选择多个题库，随机选题（根据题型或者难度来随机抽题）、章节选题（设置哪些章节的题数量）、顺序选题（填写题型的第几题-第几题）、手动选题（手动选择题库里面的题目））、考试频率（不限、单次、每日一次）、考试封面、答题后显示答案、选项乱序、试题乱序（同一题型下试题顺序随机展现）、 交卷后显示分数、成绩查询（显示答案、显示正确答案和解析、显示排行榜）、多选题得分（全部答对得分、答对数平均记分、漏选，得多少百分比分）、切屏限制（多次后强制交卷，0不限制）、交卷次数
题库功能：
	顺序练习（按顺序依次练习）、随机练习、题型练习、模拟考试（设置出题规则考试）、章节练习
学员考试功能：
	从分享的考试进入，输入相应信息进入。
群组：
	创建群组：群名称、头像、群介绍、群公告、加入方式（不限制、审核后加入、口令（设置6位数数字口令））、创建后生成群号
	添加关联题库、当考试指定群组时会显示关联考试。
	如果是审核加入对加入成员审核功能。
	解散群组
	分享群组


你帮我把这些功能的页面原型写下，特别是首页如何设计，可分tabBar。要兼容微信小程序和H5来