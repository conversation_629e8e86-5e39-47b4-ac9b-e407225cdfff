<script>
 
import { reqWxCodeLogin } from './myjs/req';
	export default {
		onLaunch: function() {
			console.log('App Launch')
			//小程序检测版本更新
			// #ifdef MP
			const updateManager = uni.getUpdateManager();
			updateManager?.onCheckForUpdate(function(res) {
				// 请求完新版本信息的回调
				if (res.hasUpdate) {
					updateManager.onUpdateReady(function(res2) {
						uni.showModal({
							title: "更新提示",
							content: "新版本已经上线，请重启小程序",
							showCancel: false,
							confirmText: "重启",
							success: (res) => {
								res.confirm && updateManager.applyUpdate();
							},
						});
					});
				}
			});
			updateManager?.onUpdateFailed(function(res) {
				uni.showModal({
					title: "更新提示",
					content: '新版本已经上线，请您删除当前小程序，到微信 "发现-小程序" 页，重新搜索打开呦~',
					showCancel: false,
					confirmText: "知道了",
				});
			});
			// #endif

			// 检查登录状态
			// this.checkLoginStatus();
			reqWxCodeLogin();
			// #ifdef MP-WEIXIN
			// 隐私授权检查
			if (!wx.requirePrivacyAuthorize) {
				uni.showModal({
					title: "提示",
					content: '当前微信版本过低，请更新微信为最新版本才可使用此小程序',
					showCancel: false,
					confirmText: "好的",
					success() {
						uni.exitMiniProgram()
					}
				})
				return;
			}

			wx.requirePrivacyAuthorize({
				success: () => {
					console.log('requirePrivacyAuthorize success')
				},
				fail: (e) => {
					uni.showModal({
						title: "提示",
						content: '用户已拒绝授权，将退出小程序',
						showCancel: false,
						confirmText: "好的",
						success() {
							uni.exitMiniProgram()
						}
					})
					console.log(e)
				}, // 用户拒绝授权
				complete: () => {}
			})
			// #endif
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},
		methods: {
			 
		}
	}
</script>

<style lang="scss">
	/**twcss开始**/
	@import 'tailwindcss/base';
	@import 'tailwindcss/utilities';
	/**twcss结束**/
	// @import 'tailwindcss/components'; 
	
	/* #ifdef APP-PLUS || H5 */
	$fa-font-path: '/styles/fonts';
	/* #endif */
	/* #ifndef APP-PLUS || H5 */
	$fa-font-path: '//cdn.bootcdn.net/ajax/libs/font-awesome/6.5.1/webfonts';
	/* #endif */
	
	@import '/styles/scss/fontawesome.scss';
	@import '/styles/scss/brands.scss';
	@import '/styles/scss/solid.scss';
	@import '/styles/scss/regular.scss';
	
	
	@import '/styles/common.scss';
	
	/* App.vue 或 uni.scss */
	*,
	*::before,
	*::after,
	input,
	textarea,
	scroll-view {
		box-sizing: border-box;

	}
	
	 
	
	 
	
	button {
		// line-height: normal;
		&::after {
			display: none;
		}
		
		&::after {
			display: none;
		}
	}
	 
	
	.share-button {
		padding: 0;
		background-color: transparent;
		border: none;
	
		-webkit-tap-highlight-color: transparent;
	
		&::after {
			display: none;
		}
	
		&::after {
			border: none;
		}
	}
	
	view,
	button {
		-webkit-tap-highlight-color: transparent;
		user-select: none;
	}
	
	/* #ifdef H5 */
	 
	
	 
	
	svg {
	  display: initial;
	}
	
	/* #endif */
</style>