<view class="h-screen bg-gray-50 flex flex-col"><view class="fixed top-0 left-0 right-0 z-50"><view class="w-full bg-gradient-to-r from-primary-500 to-primary-600 relative overflow-hidden" style="{{d}}"><view class="absolute inset-0 opacity-10"><view class="absolute top-6 right-6 w-8 h-8 rounded-full bg-white"></view><view class="absolute bottom-4 left-12 w-4 h-4 rounded-full bg-white"></view><view class="absolute top-10 left-1s2 w-2 h-2 rounded-full bg-white"></view></view><view style="{{b}}" bindtap="{{c}}" class="flex items-center justify-center"><view style="{{a}}" class="bg-white bg-opacity-20 flex items-center justify-center backdrop-blur-sm rounded-full"><view class="fas fa-arrow-left text-white text-base"></view></view></view><view class="px-4 py-2 relative z-10"><view class="text-center"><view class="flex items-center justify-center"><view class="w-6 h-6 rounded-full bg-white bg-opacity-20 flex items-center justify-center mr-2 backdrop-blur-sm"><view class="fas fa-clipboard-list text-white text-xs"></view></view><text class="text-white text-base font-bold">模拟考试</text></view></view></view></view></view><view class="flex-1 pb-16" style="{{x}}"><view class="bg-gradient-to-r from-primary-500 to-primary-600 mb-4 relative overflow-hidden -mt-4"><view class="absolute inset-0 opacity-10"><view class="absolute top-8 right-12 w-10 h-10 rounded-full bg-white"></view><view class="absolute bottom-8 left-20 w-8 h-8 rounded-full bg-white"></view><view class="absolute top-16 left-1s4 w-4 h-4 rounded-full bg-white"></view></view><view class="px-4 py-6 relative z-10"><view class="text-center mb-4"><text class="text-white text-sm opacity-90">自定义题型和规则，模拟真实考试环境</text></view><view class="bg-white bg-opacity-15 rounded-xl p-4 backdrop-blur-sm"><view class="flex items-center justify-center"><view class="flex items-center"><view class="w-8 h-8 rounded-full bg-white bg-opacity-30 flex items-center justify-center mr-3"><view class="fas fa-cogs text-white text-sm"></view></view><view><text class="text-white text-sm font-medium block">自定义考试设置</text><text class="text-white text-xs opacity-80">选择题型 · 设置分值 · 控制时长</text></view></view></view></view></view></view><view wx:if="{{e}}" class="mx-4 bg-white rounded-lg shadow p-4 mb-3"><view class="text-center"><view class="mb-4"><view class="fas fa-clock text-orange-500 text-4xl"></view></view><text class="text-xl font-bold text-gray-800 mb-2 block">未完成考试</text><text class="text-gray-500 text-sm mb-6 block">您有一次模拟考试尚未完成，可以选择继续作答或重新开始</text><view class="space-y-3"><view><button class="w-full bg-primary-500 text-white py-3 rounded-lg text-sm font-medium flex items-center justify-center" bindtap="{{f}}"><view class="fas fa-play mr-2"></view>继续考试 </button></view><view><button class="w-full border border-solid border-gray-300 bg-white text-gray-600 py-3 rounded-lg text-sm font-medium flex items-center justify-center" bindtap="{{g}}"><view class="fas fa-redo mr-2"></view>重新开始 </button></view></view></view></view><view wx:else class="mx-4 bg-white rounded-xl shadow-sm p-4"><view class="mb-5"><view class="flex items-center mb-3"><view class="fas fa-list-alt text-primary-500 mr-2"></view><text class="text-base font-semibold text-gray-800">题型配置</text></view><block wx:for="{{h}}" wx:for-item="rule" wx:key="m"><view class="mb-3 bg-gray-50 rounded-lg p-3"><view class="flex items-center justify-between mb-2"><text class="text-base font-medium text-gray-800">{{rule.a}}</text><text class="text-sm text-gray-500">题库{{rule.b}}题</text></view><view class="flex items-center space-x-3"><view class="flex-1"><text class="text-sm text-gray-600 mb-1 block">出题数量</text><view class="flex items-center bg-white rounded-lg border border-solid border-gray-200 p-1"><button bindtap="{{rule.c}}" class="w-8 h-8 flex items-center justify-center text-gray-500 hoverctext-primary-500"><view class="fas fa-minus text-sm"></view></button><input type="number" min="0" max="{{rule.d}}" class="flex-1 h-auto px-2 py-1 text-center text-base border-0 bg-transparent" readonly value="{{rule.e}}" bindinput="{{rule.f}}"/><button bindtap="{{rule.g}}" class="w-8 h-8 flex items-center justify-center text-gray-500 hoverctext-primary-500"><view class="fas fa-plus text-sm"></view></button></view></view><view class="flex-1"><text class="text-sm text-gray-600 mb-1 block">每题分值</text><view class="flex items-center bg-white rounded-lg border border-solid border-gray-200 p-1"><button bindtap="{{rule.h}}" class="w-8 h-8 flex items-center justify-center text-gray-500 hoverctext-primary-500"><view class="fas fa-minus text-sm"></view></button><input type="text" bindblur="{{rule.i}}" class="w-12 h-auto px-2 py-1 text-center text-base border-0 bg-transparent" placeholder="1.0" value="{{rule.j}}" bindinput="{{rule.k}}"/><button bindtap="{{rule.l}}" class="w-8 h-8 flex items-center justify-center text-gray-500 hoverctext-primary-500"><view class="fas fa-plus text-sm"></view></button></view></view></view></view></block></view><view class="mb-4"><view class="flex items-center justify-between"><view class="flex items-center"><text class="text-base font-semibold text-gray-800">及格分数</text><text class="text-sm text-gray-500 ml-2">（总分{{i}}分）</text></view><view class="flex items-center bg-gray-50 rounded-lg border border-solid border-gray-200 p-1"><button bindtap="{{j}}" class="w-8 h-8 flex items-center justify-center text-gray-500 hoverctext-primary-500"><view class="fas fa-minus text-sm"></view></button><input type="text" bindblur="{{k}}" class="w-16 h-auto px-2 py-1 text-center text-base border-0 bg-transparent" placeholder="60" value="{{l}}" bindinput="{{m}}"/><button bindtap="{{n}}" class="w-8 h-8 flex items-center justify-center text-gray-500 hoverctext-primary-500"><view class="fas fa-plus text-sm"></view></button></view></view></view><view class="mb-4"><view class="flex items-center justify-between"><text class="text-base font-semibold text-gray-800">考试时长</text><view class="flex items-center bg-gray-50 rounded-lg border border-solid border-gray-200 p-1"><button bindtap="{{o}}" class="w-8 h-8 flex items-center justify-center text-gray-500 hoverctext-primary-500"><view class="fas fa-minus text-sm"></view></button><input type="number" min="1" max="480" step="1" bindinput="{{p}}" class="w-16 h-auto px-2 py-1 text-center text-base border-0 bg-transparent" value="{{q}}"/><text class="text-sm text-gray-500 mr-2">分钟</text><button bindtap="{{r}}" class="w-8 h-8 flex items-center justify-center text-gray-500 hoverctext-primary-500"><view class="fas fa-plus text-sm"></view></button></view></view></view><view class="mb-4"><view class="flex items-center justify-between"><text class="text-base font-semibold text-gray-800">选项乱序</text><view class="flex items-center"><switch checked="{{s}}" bindchange="{{t}}" color="#3b82f6" class="mr-3"/><text class="text-sm text-gray-500">{{v}}</text></view></view></view><button class="w-full bg-primary-500 text-white py-3 rounded-lg text-sm font-medium flex items-center justify-center" bindtap="{{w}}"><view class="fas fa-play mr-2"></view>开始考试 </button></view></view><view class="fixed bottom-6 right-6 z-50"><button class="w-12 h-12 rounded-full bg-primary-500 text-white shadow-lg flex items-center justify-center" bindtap="{{y}}"><view class="fas fa-history text-xl"></view></button><view wx:if="{{z}}" class="absolute -top-1 -right-1 w-5 h-5 rounded-full bg-red-500 flex items-center justify-center"><text class="text-white text-xs font-bold">!</text></view></view></view>