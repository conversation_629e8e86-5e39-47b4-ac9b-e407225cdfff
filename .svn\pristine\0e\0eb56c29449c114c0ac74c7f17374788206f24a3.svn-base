<template>
  <view class="">
    <!-- 自定义导航栏 -->
    <loading-view :show="loading" text="题目加载中..." />
    <question-answer
      :isRecite="mode === 'recite'"
      v-if="!loading"
      class="h-full flex-1"
      :title="data.name"
      :questionList="data.questionList"
      :isExam="data.isExam"
      :spentDuration="data.spentDuration"
      :readonly="false"
      :answerId="data.id"
      :time="time"
    />
  </view>
</template>

<script>
export default {
  data() {
    return {
      loading: true,
      data: undefined,
      statusBarHeight: 0,
      navBarHeight: 44,
      platform: "",
      time: undefined,
      mode: "answer", // 默认答题模式
    };
  },

  computed: {
    navBarStyle() {
      // H5平台
      if (this.platform === "h5") {
        return {
          paddingTop: "0px",
          height: "48px",
        };
      }
      // 微信小程序平台
      else {
        return {
          paddingTop: this.statusBarHeight + "px",
          height: this.statusBarHeight + 48 + "px",
        };
      }
    },
  },

  onLoad(options) {
    getApp().globalData.answerRecordReloadFlag = true;
    // const chapterId = options.chapterId;

    const mode = options.mode;
    let url = "";
    if (mode == "chapter") {
      url = `/front/edu/user-answer/doChapter/${options.chapterId}`;
    } else if (mode == "random") {
      const randomQuestionNum =
        this.$store.state.qbankSetting.settingValue.randomQuestionNum || 30;
      url = `/front/edu/user-answer/doRandom/${options.bankId}/${randomQuestionNum}`;
    } else if (mode == "paper") {
      url = `/front/edu/user-answer/doPaper/${options.paperId}`;
    } else if (mode == "type") {
      url = `/front/edu/user-answer/doQuestionType/${options.bankId}/${options.type}`;
    } else if (mode == "mock") {
      url = `/front/edu/user-answer/doMockExam/${options.mockId}`;
    } else if (mode == "sequence") {
      url = `/front/edu/user-answer/doAll/${options.bankId}`;
    } else if (mode == "wrong") {
      url = `/front/edu/user-answer/doWrong/${options.bankId}`;
    }

    this.initNavBar();
    this.$reqGet(
      url,
      mode == "type" ? { typeName: options.typeName } : {}
    ).then((res) => {
      this.loading = false;
      const { data, success, errorMessage } = res || {};
      console.log(data);
      if (success) {
        this.data = data;
        if (data.isExam) {
          this.time = data.remainingTime || 0;
        }
      } else {
        uni.showModal({
          title: "提示",
          content: errorMessage || "系统繁忙",
          showCancel: false,
        });
      }
    });
  },
  methods: {
    initNavBar() {
      try {
        const systemInfo = uni.getSystemInfoSync();
        this.platform = systemInfo.platform;

        // H5平台
        if (systemInfo.platform === "h5") {
          this.statusBarHeight = 0;
          this.navBarHeight = 48;
        }
        // 微信小程序平台
        else {
          this.statusBarHeight = systemInfo.statusBarHeight || 0;

          // 获取胶囊按钮位置信息（仅微信小程序）
          const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
          if (menuButtonInfo) {
            this.navBarHeight =
              menuButtonInfo.height +
              (menuButtonInfo.top - this.statusBarHeight) * 2;
          } else {
            // 降级方案
            this.navBarHeight = 44;
          }
        }
      } catch (e) {
        console.log("获取系统信息失败", e);
        // 默认值
        this.statusBarHeight = 0;
        this.navBarHeight = 48;
        this.platform = "h5";
      }
    },

    goBack() {
      uni.navigateBack();
    },
    openSettings() {
      uni.navigateTo({
        url: "/pages/practice/setting",
      });
    },
    switchMode(newMode) {
      this.mode = newMode;
      // 这里可以添加模式切换的逻辑
      // console.log('切换到模式:', newMode);
    },
  },
};
</script>

<style></style>
