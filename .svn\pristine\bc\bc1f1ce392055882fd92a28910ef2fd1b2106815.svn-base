import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'

// 导入LoadingView组件
import LoadingView from './components/loading-view/loading-view.vue'

Vue.config.productionTip = false
// 注册为全局组件
Vue.component('loading-view', LoadingView)

App.mpType = 'app'
const app = new Vue({
	...App
})
app.$mount()
// #endif

// #ifdef VUE3
import {
	createSSRApp
} from 'vue'
import { reqGet, reqPost } from './myjs/req.js'

import store from './store'

 
 

// 登录守卫 - 检查需要登录的页面
function checkLoginRequired(url) {
	// 不需要登录的页面列表
	const publicPages = [
		'/pages/login/login',
		'/pages/index/index',
		'/pages/agreement/user',
		'/pages/agreement/privacy'
	];
	
	// 检查当前页面是否在公开页面列表中
	return !publicPages.some(page => url.includes(page));
}

// 页面跳转拦截器
function setupNavigationGuard() {
	// 拦截 uni.navigateTo
	const originalNavigateTo = uni.navigateTo;
	uni.navigateTo = function(options) {
		if (checkLoginRequired(options.url)) {
			const token = uni.getStorageSync('h5_token');
			if (!token) {
				const loginUrl = "/pages/login/login";
				return uni.redirectTo({
					url: `${loginUrl}?loginCallbackUrl=${encodeURIComponent(options.url)}`
				});
			}
		}
		return originalNavigateTo.call(this, options);
	};
	
	// 拦截 uni.redirectTo
	const originalRedirectTo = uni.redirectTo;
	uni.redirectTo = function(options) {
		if (checkLoginRequired(options.url)) {
			const token = uni.getStorageSync('h5_token');
			if (!token) {
				const loginUrl = "/pages/login/login";
				options.url = `${loginUrl}?loginCallbackUrl=${encodeURIComponent(options.url)}`;
			}
		}
		return originalRedirectTo.call(this, options);
	};
	
	// 拦截 uni.switchTab
	const originalSwitchTab = uni.switchTab;
	uni.switchTab = function(options) {
		if (checkLoginRequired(options.url)) {
			const token = uni.getStorageSync('h5_token');
			if (!token) {
				const loginUrl = "/pages/login/login";
				return uni.redirectTo({
					url: `${loginUrl}?loginCallbackUrl=${encodeURIComponent(options.url)}`
				});
			}
		}
		return originalSwitchTab.call(this, options);
	};
}

export function createApp() {
	const app = createSSRApp(App)
	app.config.globalProperties.$reqGet = reqGet;
	app.config.globalProperties.$reqPost = reqPost;
	
 
	
	// 设置导航守卫
	setupNavigationGuard();
	app.use(store)
	
	return {
		app
	}
}
// #endif