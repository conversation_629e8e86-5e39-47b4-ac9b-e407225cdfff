<template>
	<view :class="['px-4',fontSizeClass]">
		 <mp-html :content="text" /> 
	</view>
</template>

<script>
	const cacheMap = new Map();

	export default {
		name: "question-answer-parent-content",
		props: {
			questionVersion: Number,
			fontSizeClass: String,
			parentId: String
		},
		data() {
			return {
				text: ''
			};
		},
		watch: {
			parentId: {
				handler(nv) {
					// console.log(nv)
					this.loadData(nv, this.questionVersion)
				},
				immediate:true
			}
		},

		methods: {
			loadData(parentId, questionVersion) {
				const key = `${parentId}_${questionVersion}`;
				const v = cacheMap.get(key);
				if (v) {
					this.text = v;
					return;
				}
				this.$http
					.get(
						`/front/edu/user-answer/questionContent/${parentId}/${questionVersion}`
					)
					.then((res) => {
						const {
							data,
							success
						} = res.data || {};
						if (success) {
							this.text = "【组合题】" + data.text;
					 
							cacheMap.set(key, this.text);
						}
					});
			}
		}
	}
</script>

<style>

</style>