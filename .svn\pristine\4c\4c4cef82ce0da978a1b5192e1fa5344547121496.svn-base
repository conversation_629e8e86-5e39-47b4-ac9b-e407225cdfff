<template>
  <view class="flex flex-col h-screen bg-gray-50">
    <!-- 内容区域 -->
    <view class="flex-1 p-4">
      <!-- 文件选择区域 -->
      <view
        class="bg-white rounded-lg shadow mb-4 p-5 flex flex-col items-center justify-center"
        @click="chooseFile"
      >
        <template v-if="!selectedFile">
          <view class="mb-4 text-primary-500 text-5xl">
            <text class="fas fa-file-import"></text>
          </view>
          <text class="text-base mb-2 font-semibold">点击选择文件</text>
          <text class="text-sm text-gray-500 text-center"
            >支持从聊天记录中选择Excel文件(.xlsx格式)</text
          >
        </template>
        <view v-else class="w-full">
          <view class="flex items-center justify-between">
            <view class="flex items-center">
              <text
                class="fas fa-file-excel text-green-500 mr-3 text-2xl"
              ></text>
              <view class="flex-1 min-w-0">
                <text class="font-medium block truncate">{{
                  selectedFile.name
                }}</text>
                <text class="text-xs text-gray-500">{{
                  formatFileSize(selectedFile.size)
                }}</text>
              </view>
            </view>
            <view class="flex items-center">
              <text class="text-primary-500 text-sm mr-2">重新选择</text>
              <text class="fas fa-redo text-primary-500"></text>
            </view>
          </view>
        </view>
      </view>

      <!-- 删除:已选择文件显示 -->
      <!-- 删除:<view v-if="selectedFile" class="bg-white rounded-lg shadow mb-4"> -->
      <!-- 删除:	<view class="p-4 border-b border-gray-100"> -->
      <!-- 删除:		<text class="font-semibold">已选择文件</text> -->
      <!-- 删除:	</view> -->
      <!-- 删除:	<view class="p-4"> -->
      <!-- 删除:		<view class="flex items-center justify-between py-2"> -->
      <!-- 删除:			<view class="flex items-center"> -->
      <!-- 删除:				<text class="fas fa-file-excel text-green-500 mr-3"></text> -->
      <!-- 删除:				<view> -->
      <!-- 删除:					<text class="font-medium">{{ selectedFile.name }}</text> -->
      <!-- 删除:					<text class="text-xs text-gray-500 block">{{ formatFileSize(selectedFile.size) }}</text> -->
      <!-- 删除:				</view> -->
      <!-- 删除:			</view> -->
      <!-- 删除:			<view class="flex items-center"> -->
      <!-- 删除:				<view @click.stop="removeFile" class="ml-3 h-8 w-8 flex items-center justify-center rounded-full hover:bg-gray-100"> -->
      <!-- 删除:					<text class="fas fa-times text-gray-500"></text> -->
      <!-- 删除:				</view> -->
      <!-- 删除:			</view> -->
      <!-- 删除:		</view> -->
      <!-- 删除:	</view> -->
      <!-- 删除:</view> -->

      <!-- 导入说明 -->
      <view class="bg-white rounded-lg shadow mb-4">
        <view
          class="p-4 border-b border-gray-100 flex justify-between items-center"
        >
          <text class="font-semibold">导入说明</text>
          <view
            @click="downloadTemplate"
            class="flex items-center text-primary-500"
          >
            <text class="fas fa-download mr-1 text-sm"></text>
            <text class="text-sm">下载模板</text>
          </view>
        </view>
        <view class="p-4">
          <view class="mt-3 mb-2">
            <text class="text-sm text-gray-700 font-medium">操作步骤：</text>
          </view>
          <view class="space-y-2">
            <view class="flex items-start">
              <view
                class="w-5 h-5 bg-primary-500 text-white rounded-full flex items-center justify-center mr-2 flex-shrink-0 mt-0.5"
              >
                <text class="text-xs">1</text>
              </view>
              <text class="text-sm text-gray-700"
                >点击上方"下载模板"按钮获取Excel模板，点击右上角"..."按钮，可将模板文件转发、选择其他应用打开、保存到手机等操作</text
              >
            </view>
            <view class="flex items-start">
              <view
                class="w-5 h-5 bg-primary-500 text-white rounded-full flex items-center justify-center mr-2 flex-shrink-0 mt-0.5"
              >
                <text class="text-xs">2</text>
              </view>
              <text class="text-sm text-gray-700"
                >填写Excel模板中的题目数据并保存，具体格式请参考模板文件</text
              >
            </view>
            <view class="flex items-start">
              <view
                class="w-5 h-5 bg-primary-500 text-white rounded-full flex items-center justify-center mr-2 flex-shrink-0 mt-0.5"
              >
                <text class="text-xs">3</text>
              </view>
              <text class="text-sm text-gray-700"
                >将填好的Excel文件发送给文件传输助手或任意好友</text
              >
            </view>
            <view class="flex items-start">
              <view
                class="w-5 h-5 bg-primary-500 text-white rounded-full flex items-center justify-center mr-2 flex-shrink-0 mt-0.5"
              >
                <text class="text-xs">4</text>
              </view>
              <text class="text-sm text-gray-700"
                >点击上方"点击选择文件"区域</text
              >
            </view>
            <view class="flex items-start">
              <view
                class="w-5 h-5 bg-primary-500 text-white rounded-full flex items-center justify-center mr-2 flex-shrink-0 mt-0.5"
              >
                <text class="text-xs">5</text>
              </view>
              <text class="text-sm text-gray-700"
                >在聊天记录中找到并选择之前发送的Excel文件</text
              >
            </view>
            <view class="flex items-start">
              <view
                class="w-5 h-5 bg-primary-500 text-white rounded-full flex items-center justify-center mr-2 flex-shrink-0 mt-0.5"
              >
                <text class="text-xs">6</text>
              </view>
              <text class="text-sm text-gray-700"
                >选择文件后点击"预览导入"按钮</text
              >
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view
      class="fixed bottom-0 left-0 right-0 p-4 bg-white shadow-md border-t border-gray-100 z-10"
    >
      <button
        :disabled="!selectedFile || processing"
        :class="[
          processing ? 'opacity-75 cursor-not-allowed' : 'hover:opacity-90',
        ]"
        class="w-full flex items-center justify-center py-3 text-sm px-5 rounded-md font-medium bg-primary-500 text-white"
        @click="uploadAndParse"
      >
        <text v-if="processing" class="fas fa-circle-notch fa-spin mr-1"></text>
        <text>预览导入</text>
      </button>
    </view>

    <!-- 平台不支持提示弹窗 -->
    <view
      v-if="showPlatformAlert"
      class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-30 z-50"
    >
      <view class="bg-white rounded-lg p-5 mx-4 w-full max-w-sm">
        <view class="text-center mb-4">
          <text
            class="fas fa-exclamation-triangle text-yellow-500 text-4xl"
          ></text>
        </view>
        <view class="text-center mb-4">
          <text class="text-lg font-semibold">平台不支持</text>
          <text class="text-sm text-gray-600 block mt-2"
            >当前平台不支持从聊天记录选择文件，请在微信小程序中使用该功能。</text
          >
        </view>
        <view class="mt-5">
          <button
            class="w-full text-sm py-3 px-5 bg-primary-500 text-white rounded-md"
            @click="closePlatformAlert"
          >
            我知道了
          </button>
        </view>
      </view>
    </view>

    <!-- 加载中 -->
    <loading-view :show="loading" text="解析文件中..." fullscreen />
  </view>
</template>

<script>
import myConfig from "@/config";
const apiServer = myConfig.apiServer;
import { textToQuestionList } from "@/myjs/question";
import {getHeaders} from '@/myjs/req'

export default {
  data() {
    return {
      selectedFile: null,
      showPlatformAlert: false,
      loading: false,
      processing: false,
      templateFilePath: null, // 添加templateFilePath变量
    };
  },
  onLoad(options){
	this.bankId = options.bankId;
  },
  methods: {
    chooseFile() {
      // 检查平台是否为微信小程序
      // #ifdef MP-WEIXIN
      uni.chooseMessageFile({
        count: 1,
        type: "file",
        extension: ["xlsx"],
        success: (res) => {
          console.log("文件选择成功:", res);

          // 处理选择的文件
          if (res.tempFiles && res.tempFiles.length > 0) {
            const file = res.tempFiles[0];

            // 检查文件类型
            if (!this.isSupportedFile(file.name)) {
              uni.showToast({
                title: "请选择Excel文件(.xlsx格式)",
                icon: "none",
              });
              return;
            }

            // 设置选中的文件
            this.selectedFile = file;
			this.parseText = "";
          }
        },
        fail: (err) => {
          console.error("文件选择失败:", err);
        },
      });
      // #endif

      // 在非微信小程序平台显示提示
      // #ifndef MP-WEIXIN
      this.showPlatformAlert = true;
      // #endif
    },

    downloadTemplate() {
      //先下载再打开
      //当前data中存在已下载过的就不下载了，直接打开

      if (this.templateFilePath) {
        this.openFile(this.templateFilePath);
        return;
      }

      const that = this; // 保存this引用

      uni.downloadFile({
        url: "https://zhihai-static.oss-cn-hangzhou.aliyuncs.com/datum/Excel导入模板.xlsx",
        filePath: `${wx.env.USER_DATA_PATH}/Excel导入模板.xlsx`,
        success: (res) => {
          that.templateFilePath = res.filePath; // 使用that引用
          that.openFile(that.templateFilePath);
        },
      });
    },

    openFile(filePath) {
      uni.openDocument({
        filePath: filePath,
        showMenu: true,
        fileType: "xlsx",
        success: (res) => {
          console.log("打开文件成功:", res);
        },
      });
    },
	toPreview(){
		const questionList = textToQuestionList(this.parseText);

if (questionList.length === 0) {
  uni.showToast({
	title: "未解析出题目，请检查文本格式",
	icon: "none",
  });
  return;
}

// 将解析结果存储到全局或缓存中
getApp().globalData = getApp().globalData || {};
getApp().globalData.parsedQuestions = questionList;

 

// 跳转到预览页面
uni.navigateTo({
  url: "/pages/question/impPreview?bankId=" + this.bankId,
});
	},

    uploadAndParse() {
      if (!this.selectedFile) {
        uni.showToast({
          title: "请先选择文件",
          icon: "none",
        });
        return;
      }

      this.processing = true;
      this.loading = true;


	  if(this.parseText){
		this.processing = false;
		this.loading = false;
		this.toPreview();
		return;
	  }


      // 上传文件到服务器解析
      uni.uploadFile({
        url: apiServer + "/front/edu-personal/question/excelRead",
        filePath: this.selectedFile.path,
		header: getHeaders(),
        name: "file",
        success: (res) => {
			
          try {
            const data = JSON.parse(res.data);
            if (data.success) {
				this.parseText = data.data.join("\n");
				this.toPreview();
             
            } else {
              throw new Error(data.errorMessage || "解析失败");
            }
          } catch (error) {
            console.error("解析响应失败:", error);
            uni.showToast({
              title: "解析失败: " + (error.message || "未知错误"),
              icon: "none",
              duration: 3000,
            });
          }
        },
        fail: (err) => {
          console.error("上传文件失败:", err);
          uni.showToast({
            title: "上传文件失败",
            icon: "none",
          });
        },
        complete: () => {
          this.processing = false;
          this.loading = false;
        },
      });
    },

    closePlatformAlert() {
      this.showPlatformAlert = false;
    },

    isSupportedFile(filename) {
      return filename.endsWith(".xlsx");
    },

    formatFileSize(size) {
      if (size < 1024) {
        return size + " B";
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + " KB";
      } else {
        return (size / (1024 * 1024)).toFixed(2) + " MB";
      }
    },
  },
};
</script>
