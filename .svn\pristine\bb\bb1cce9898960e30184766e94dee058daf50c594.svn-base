<template>
  <view class="min-h-screen bg-gray-50">
    <!-- 题库信息头部 -->
    <view
      class="bg-gradient-to-r from-primary-500 to-primary-600 px-4 py-6 text-white"
    >
      <view class="flex items-center mb-4">
        <view
          class="w-16 h-16 rounded-xl bg-white bg-opacity-20 flex items-center justify-center mr-4"
        >
          <text class="fas fa-book text-white text-2xl"></text>
        </view>
        <view class="flex-1">
          <text class="text-xl font-bold block mb-1">{{
            questionBank.name
          }}</text>
          <text class="text-white text-opacity-80 text-sm">{{
            questionBank.description
          }}</text>
        </view>
      </view>

      <view class="grid grid-cols-4 gap-3">
        <view class="text-center">
          <text class="text-white text-lg font-bold block">{{
            questionBank.questionCount
          }}</text>
          <text class="text-white text-opacity-80 text-xs">题目</text>
        </view>
        <view class="text-center">
          <text class="text-white text-lg font-bold block">{{
            questionBank.memberCount
          }}</text>
          <text class="text-white text-opacity-80 text-xs">成员</text>
        </view>
        <view class="text-center">
          <text class="text-white text-lg font-bold block">{{
            questionBank.chapterCount
          }}</text>
          <text class="text-white text-opacity-80 text-xs">章节</text>
        </view>
        <view class="text-center">
          <text class="text-white text-lg font-bold block">{{
            questionBank.rating || "--"
          }}</text>
          <text class="text-white text-opacity-80 text-xs">评分</text>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="px-4 py-4">
      <!-- 练习模式选择 -->
      <view class="bg-white rounded-xl p-4 shadow-sm mb-4">
        <text class="text-lg font-bold text-gray-800 mb-4 block">练习模式</text>
        <view class="grid grid-cols-2 gap-3">
          <button
            v-for="mode in practiceMode"
            :key="mode.id"
            @click="startPractice(mode)"
            class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-primary-500 hover:bg-primary-50 transition-all"
          >
            <view
              class="w-12 h-12 rounded-xl flex items-center justify-center mb-2"
              :class="mode.bgColor"
            >
              <text :class="mode.icon + ' text-white text-lg'"></text>
            </view>
            <text class="text-sm font-medium text-gray-800 text-center">{{
              mode.name
            }}</text>
            <text class="text-xs text-gray-500 text-center mt-1">{{
              mode.description
            }}</text>
          </button>
        </view>
      </view>

      <!-- 我的学习记录 -->
      <view class="bg-white rounded-xl p-4 shadow-sm mb-4">
        <text class="text-lg font-bold text-gray-800 mb-4 block">我的学习</text>
        <view class="grid grid-cols-2 gap-3">
          <button
            v-for="record in myRecords"
            :key="record.id"
            @click="goToRecord(record)"
            class="flex flex-col items-center p-4 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg hover:from-gray-100 hover:to-gray-200 transition-all"
          >
            <view
              class="w-12 h-12 rounded-xl flex items-center justify-center mb-2"
              :class="record.bgColor"
            >
              <text :class="record.icon + ' text-white text-lg'"></text>
            </view>
            <text class="text-sm font-medium text-gray-800 text-center">{{
              record.name
            }}</text>
            <text class="text-xs text-gray-500 text-center mt-1">{{
              record.count
            }}</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 底部固定操作按钮 -->
    <view
      class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-3"
    >
      <view class="flex items-center justify-between">
        <!-- 分享按钮 -->
        <button
          @click="shareQuestionBank"
          :class="
            questionBank.isMember
              ? 'w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center'
              : 'flex flex-col items-center justify-center w-16 h-16 bg-gray-100 rounded-lg'
          "
        >
          <text class="fas fa-share text-gray-600 text-2xl"></text>
          <text v-if="!questionBank.isMember" class="text-xs text-gray-600 mt-1"
            >分享</text
          >
        </button>

        <!-- 加入题库按钮 -->
        <button
          v-if="!questionBank.isMember"
          @click="joinQuestionBank"
          class="flex-1 ml-4 bg-primary-500 text-white py-3 rounded-lg text-sm font-medium"
        >
          加入题库
        </button>
      </view>
    </view>

    <!-- 底部安全区域 -->
    <view class="h-20"></view>

    <!-- 密码输入弹窗 -->
    <view
      v-if="showPasswordModal"
      class="fixed inset-0 z-50 flex items-center justify-center"
      @click="closePasswordModal"
    >
      <view class="absolute inset-0 bg-black bg-opacity-50"></view>
      <view
        class="bg-white rounded-3xl p-6 mx-6 relative z-10 w-full max-w-sm shadow-lg"
        @click.stop
      >
        <view class="flex items-center justify-between mb-6">
          <view class="flex items-center">
            <view
              class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center mr-3"
            >
              <text class="fas fa-lock text-primary-500"></text>
            </view>
            <text class="text-xl font-bold text-gray-800">输入密码</text>
          </view>
          <view>
            <button
              @click="closePasswordModal"
              class="w-8 h-8 rounded-full hover:bg-gray-100 flex items-center justify-center"
            >
              <text class="fas fa-times text-gray-400"></text>
            </button>
          </view>
        </view>

        <view class="space-y-4">
          <view>
            <text class="text-sm font-medium text-gray-700 block mb-2"
              >题库密码 <text class="text-red-500">*</text></text
            >
            <input
              v-model="passwordInput"
              type="password"
              placeholder="请输入题库密码"
              maxlength="20"
              class="h-auto p-3 border border-solid border-gray-300 rounded-xl text-sm bg-white focus:border-primary-500 hover:border-primary-500"
              @confirm="confirmPassword"
            />
          </view>
        </view>

        <button
          @click="confirmPassword"
          class="w-full mt-6 py-3.5 bg-primary-500 text-white rounded-xl text-sm font-medium"
        >
          <text class="fas fa-check mr-2"></text>
          确认加入
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      bankId: "",
      showPasswordModal: false,
      passwordInput: "",
      questionBank: {
        isMember: false,
        needPassword: false,
        id: "1",
        name: "计算机基础知识",
        description: "涵盖计算机组成原理、操作系统、数据结构等基础知识",
        questionCount: 156,
        memberCount: 89,
        chapterCount: 12,
        rating: "4.8",
      },
      practiceMode: [
        {
          id: "chapter",
          name: "章节练习",
          description: "按章节分类练习",
          icon: "fas fa-list",
          bgColor: "bg-blue-500",
        },
        {
          id: "sequence",
          name: "顺序练习",
          description: "按题目顺序练习",
          icon: "fas fa-sort-numeric-up",
          bgColor: "bg-green-500",
        },
        {
          id: "type",
          name: "题型练习",
          description: "按题型分类练习",
          icon: "fas fa-tags",
          bgColor: "bg-purple-500",
        },
        {
          id: "mock",
          name: "模拟考试",
          description: "模拟真实考试",
          icon: "fas fa-clipboard-check",
          bgColor: "bg-red-500",
        },
        {
          id: "random",
          name: "随机练习",
          description: "随机抽取题目",
          icon: "fas fa-random",
          bgColor: "bg-orange-500",
        },
      ],
      myRecords: [
        {
          id: "wrong",
          name: "我的错题",
          count: "12题",
          icon: "fas fa-times-circle",
          bgColor: "bg-red-500",
        },
        {
          id: "favorite",
          name: "我的收藏",
          count: "8题",
          icon: "fas fa-heart",
          bgColor: "bg-pink-500",
        },
      ],
    };
  },
  onLoad(options) {
    if (options.bankId) {
      this.bankId = options.bankId;
      this.loadQuestionBankDetail();
    }
  },
  methods: {
    loadQuestionBankDetail() {
      this.$reqGet("/front/edu-personal/bankHome", {
        id: this.bankId,
      }).then((res) => {
        if (res.success) {
          this.questionBank = res.data;
        }
      });
    },

    startPractice(mode) {
      if (mode.id == "chapter") {
        uni.navigateTo({
          url: `/pages/practice/chapter?bankId=${this.bankId}`,
        });
      } else if (mode.id == "mock") {
        uni.navigateTo({
          url: `/pages/practice/mock?bankId=${this.bankId}`,
        });
      } else if (mode.id == "type") {
        uni.navigateTo({
          url: `/pages/practice/questionType?bankId=${this.bankId}`,
        });
      } else if (mode.id == "random") {
        uni.navigateTo({
          url: `/pages/practice/do?mode=random&bankId=${this.bankId}`,
        });
      } else if (mode.id == "sequence") {
        uni.navigateTo({
          url: `/pages/practice/do?mode=sequence&bankId=${this.bankId}`,
        });
      }
    },

    goToRecord(record) {
      // 跳转到对应记录页面
      switch (record.id) {
        case "wrong":
          uni.navigateTo({
            url: `/pages/practice/wrongQuestion?bankId=${this.bankId}`,
          });
          break;
        case "favorite":
          uni.navigateTo({
            url: `/pages/practice/collectionQuestion?bankId=${this.bankId}`,
          });
          break;
      }
    },

    joinQuestionBank() {
      // 检查是否需要密码
      if (this.questionBank.needPassword) {
        // 显示自定义密码输入弹窗
        this.showPasswordModal = true;
        this.passwordInput = "";
      } else {
        uni.showModal({
          title: "提示",
          content: "确认将此题库加入我的题库吗？",
          success: (res) => {
            if (res.confirm) {
              this.requestJoinBank();
            }
          },
        });
      }
    },

    closePasswordModal() {
      this.showPasswordModal = false;
      this.passwordInput = "";
    },

    confirmPassword() {
      if (!this.passwordInput.trim()) {
        uni.showToast({
          title: "请输入密码",
          icon: "none",
        });
        return;
      }

      this.requestJoinBank(this.passwordInput);
      this.closePasswordModal();
    },

    requestJoinBank(password = "") {
      // 请求加入题库接口
      const params = {
        id: this.bankId,
      };

      if (password) {
        params.pwd = password;
      }

      this.$reqPost("/front/edu-personal/joinBank", params)
        .then((res) => {
          if (res.success) {
            // 更新题库状态
            this.questionBank.isMember = true;
            uni.showToast({
              title: "加入成功",
              icon: "success",
            });
          } else {
            uni.showToast({
              title: res.errorMessage || "加入失败",
              icon: "error",
            });
          }
        })
        .catch((err) => {
          uni.showToast({
            title: "网络错误，请重试",
            icon: "error",
          });
        });
    },

    shareQuestionBank() {
      // 分享题库逻辑
      uni.showActionSheet({
        itemList: ["微信好友", "朋友圈", "复制链接"],
        success: (res) => {
          console.log("分享方式:", res.tapIndex);
        },
      });
    },
  },
};
</script>

<style scoped></style>
