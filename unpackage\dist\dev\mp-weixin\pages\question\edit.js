"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      loading: false,
      bankId: null
    };
  },
  onLoad(options) {
    this.id = options.id;
    this.loadQuestion();
  },
  methods: {
    loadQuestion() {
      this.loading = true;
      this.$reqGet("/front/edu-personal/question/detail", { id: this.id }).then((res) => {
        if (res.success) {
          this.loading = false;
          this.bankId = res.data.bankId;
          this.$nextTick(() => {
            this.$refs.questionForm.updateFormValue(res.data);
          });
        } else {
          common_vendor.index.showToast({
            title: res.errorMessage,
            icon: "none"
          });
        }
      });
    },
    // 保存题目
    saveQuestion() {
      this.$refs.questionForm.validateForm((formValues) => {
        formValues.id = this.id;
        this.$reqPost("/front/edu-personal/question/edit", formValues, true, "保存中...").then((res) => {
          if (res.success) {
            common_vendor.index.showToast({
              title: "保存成功",
              icon: "success"
            });
            setTimeout(() => {
              common_vendor.index.navigateBack();
            }, 1e3);
          } else {
            common_vendor.index.showToast({
              title: res.errorMessage,
              icon: "none"
            });
          }
        });
      });
    }
  }
};
if (!Array) {
  const _easycom_loading_view2 = common_vendor.resolveComponent("loading-view");
  const _easycom_question_form2 = common_vendor.resolveComponent("question-form");
  (_easycom_loading_view2 + _easycom_question_form2)();
}
const _easycom_loading_view = () => "../../components/loading-view/loading-view.js";
const _easycom_question_form = () => "../../components/question-form/question-form.js";
if (!Math) {
  (_easycom_loading_view + _easycom_question_form)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      show: $data.loading
    }),
    b: !$data.loading
  }, !$data.loading ? {
    c: common_vendor.sr("questionForm", "b1eaa6d2-1"),
    d: common_vendor.p({
      bankId: $data.bankId,
      isEdit: true
    })
  } : {}, {
    e: common_vendor.o((...args) => $options.saveQuestion && $options.saveQuestion(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/question/edit.js.map
