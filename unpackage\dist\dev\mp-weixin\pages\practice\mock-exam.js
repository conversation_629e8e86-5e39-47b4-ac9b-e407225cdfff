"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      navBarButtonStyle: {},
      navBarStyle: {},
      // 新增导航栏样式
      backButtonStyle: {},
      // 返回按钮样式
      mainContentStyle: {},
      // 主体内容样式
      hasUnfinishedExam: true,
      hasNewHistory: false,
      // 是否有新的历史记录提示
      rules: [
        { type: 1, label: "单选题", total: 20, count: 10, score: "2.0" },
        { type: 2, label: "多选题", total: 10, count: 5, score: "3.0" },
        { type: 3, label: "判断题", total: 5, count: 5, score: "1.0" }
      ],
      passScore: 60,
      duration: 60,
      shuffleOption: true
    };
  },
  computed: {
    totalScore() {
      return this.rules.reduce(
        (sum, r) => sum + r.count * parseFloat(r.score || 0),
        0
      );
    }
  },
  methods: {
    setNavBarButtonStyle() {
      let style = {};
      const menuButton = common_vendor.index.getMenuButtonBoundingClientRect();
      style = {
        position: "absolute",
        left: "16px",
        top: menuButton.top + "px",
        width: menuButton.height + "px",
        height: menuButton.height + "px",
        "z-index": 20
      };
      this.navBarButtonStyle = style;
    },
    setNavBarStyle() {
      let style = {};
      const sys = common_vendor.index.getSystemInfoSync();
      const navTop = sys.statusBarHeight;
      style = {
        "padding-top": navTop + "px"
      };
      this.navBarStyle = style;
    },
    setBackButtonStyle() {
      let style = {};
      const menuButton = common_vendor.index.getMenuButtonBoundingClientRect();
      const buttonSize = menuButton.height;
      style = {
        width: buttonSize + "px",
        height: buttonSize + "px"
      };
      this.backButtonStyle = style;
    },
    setMainContentStyle() {
      let style = {};
      common_vendor.index.getSystemInfoSync();
      const menuButton = common_vendor.index.getMenuButtonBoundingClientRect();
      const navHeight = menuButton.top + menuButton.height;
      style = {
        "padding-top": navHeight + "px"
      };
      this.mainContentStyle = style;
    },
    goBack() {
      common_vendor.index.navigateBack();
    },
    continueExam() {
      common_vendor.index.navigateTo({ url: "/pages/practice/do-chapter?mode=mock" });
    },
    resetExam() {
      this.hasUnfinishedExam = false;
    },
    startExam() {
      if (this.passScore > this.totalScore) {
        common_vendor.index.showToast({ title: "及格分不能超过总分", icon: "none" });
        return;
      }
      if (this.duration < 1 || !Number.isInteger(this.duration)) {
        common_vendor.index.showToast({ title: "考试时长需为正整数", icon: "none" });
        return;
      }
      if (this.duration > 480) {
        common_vendor.index.showToast({ title: "考试时长不能超过480分钟", icon: "none" });
        return;
      }
      for (let rule of this.rules) {
        if (rule.count > 0 && rule.score < 0.1) {
          common_vendor.index.showToast({ title: "分数不能小于0.1", icon: "none" });
          return;
        }
      }
      common_vendor.index.navigateTo({ url: "/pages/practice/do-chapter?mode=mock" });
    },
    goHistory() {
      common_vendor.index.navigateTo({ url: "/pages/practice/mock-exam-history" });
    },
    // +/- 按钮方法
    decreaseCount(index) {
      if (this.rules[index].count > 0) {
        this.rules[index].count--;
      }
    },
    increaseCount(index) {
      if (this.rules[index].count < this.rules[index].total) {
        this.rules[index].count++;
      }
    },
    decreaseScore(index) {
      const currentScore = parseFloat(this.rules[index].score) || 0.5;
      if (currentScore > 0.5) {
        const newScore = Math.round((currentScore - 0.5) * 10) / 10;
        this.rules[index].score = newScore.toFixed(1);
      }
    },
    increaseScore(index) {
      const currentScore = parseFloat(this.rules[index].score) || 0;
      const newScore = Math.round((currentScore + 0.5) * 10) / 10;
      this.rules[index].score = newScore.toFixed(1);
    },
    decreasePassScore() {
      if (this.passScore > 1) {
        this.passScore = this.passScore - 1;
      }
    },
    increasePassScore() {
      if (this.passScore < this.totalScore) {
        this.passScore = this.passScore + 1;
      }
    },
    decreaseDuration() {
      if (this.duration > 1) {
        this.duration--;
      }
    },
    increaseDuration() {
      if (this.duration < 480) {
        this.duration++;
      }
    },
    // 解析并限制分数格式
    parseScore(value) {
      value = value.replace(/[^\d.]/g, "");
      const parts = value.split(".");
      if (parts.length > 2) {
        value = parts[0] + "." + parts.slice(1).join("");
      }
      if (parts.length === 2 && parts[1].length > 1) {
        value = parts[0] + "." + parts[1].substring(0, 1);
      }
      const numValue = parseFloat(value);
      if (isNaN(numValue))
        return 0.5;
      if (numValue < 0.5)
        return 0.5;
      return Math.round(numValue * 10) / 10;
    },
    // 题型分数失焦处理
    onScoreBlur(index, event) {
      const value = this.rules[index].score;
      const formattedScore = this.parseScore(value.toString());
      this.rules[index].score = formattedScore.toFixed(1);
    },
    // 及格分数失焦处理
    onPassScoreBlur(event) {
      const value = this.passScore;
      let score = this.parseScore(value.toString());
      if (score > this.totalScore) {
        score = this.totalScore;
      }
      this.passScore = score;
    },
    // 时长验证方法
    validateDuration() {
      this.$nextTick(() => {
        if (this.duration < 1) {
          this.duration = 1;
        } else if (this.duration > 480) {
          this.duration = 480;
        }
      });
    }
  },
  onLoad(options) {
    this.setNavBarButtonStyle();
    this.setNavBarStyle();
    this.setBackButtonStyle();
    this.setMainContentStyle();
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.s($data.backButtonStyle),
    b: common_vendor.s($data.navBarButtonStyle),
    c: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    d: common_vendor.s($data.navBarStyle),
    e: $data.hasUnfinishedExam
  }, $data.hasUnfinishedExam ? {
    f: common_vendor.o((...args) => $options.continueExam && $options.continueExam(...args)),
    g: common_vendor.o((...args) => $options.resetExam && $options.resetExam(...args))
  } : {
    h: common_vendor.f($data.rules, (rule, index, i0) => {
      return {
        a: common_vendor.t(rule.label),
        b: common_vendor.t(rule.total),
        c: common_vendor.o(($event) => $options.decreaseCount(index), rule.type),
        d: rule.total,
        e: rule.count,
        f: common_vendor.o(common_vendor.m(($event) => rule.count = $event.detail.value, {
          number: true
        }), rule.type),
        g: common_vendor.o(($event) => $options.increaseCount(index), rule.type),
        h: common_vendor.o(($event) => $options.decreaseScore(index), rule.type),
        i: common_vendor.o(($event) => $options.onScoreBlur(index, $event), rule.type),
        j: rule.score,
        k: common_vendor.o(($event) => rule.score = $event.detail.value, rule.type),
        l: common_vendor.o(($event) => $options.increaseScore(index), rule.type),
        m: rule.type
      };
    }),
    i: common_vendor.t($options.totalScore),
    j: common_vendor.o(($event) => $options.decreasePassScore()),
    k: common_vendor.o(($event) => $options.onPassScoreBlur($event)),
    l: $data.passScore,
    m: common_vendor.o(($event) => $data.passScore = $event.detail.value),
    n: common_vendor.o(($event) => $options.increasePassScore()),
    o: common_vendor.o(($event) => $options.decreaseDuration()),
    p: common_vendor.o([common_vendor.m(($event) => $data.duration = $event.detail.value, {
      number: true
    }), (...args) => $options.validateDuration && $options.validateDuration(...args)]),
    q: $data.duration,
    r: common_vendor.o(($event) => $options.increaseDuration()),
    s: $data.shuffleOption,
    t: common_vendor.o((e) => $data.shuffleOption = e.detail.value),
    v: common_vendor.t($data.shuffleOption ? "开启" : "关闭"),
    w: common_vendor.o((...args) => $options.startExam && $options.startExam(...args))
  }, {
    x: common_vendor.s($data.mainContentStyle),
    y: common_vendor.o((...args) => $options.goHistory && $options.goHistory(...args)),
    z: $data.hasNewHistory
  }, $data.hasNewHistory ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/practice/mock-exam.js.map
