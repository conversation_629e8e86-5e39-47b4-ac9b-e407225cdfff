{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<script>\n \nimport { reqWxCodeLogin } from './myjs/req';\n\texport default {\n\t\tonLaunch: function() {\n\t\t\tconsole.log('App Launch')\n\t\t\t//小程序检测版本更新\n\t\t\t// #ifdef MP\n\t\t\tconst updateManager = uni.getUpdateManager();\n\t\t\tupdateManager?.onCheckForUpdate(function(res) {\n\t\t\t\t// 请求完新版本信息的回调\n\t\t\t\tif (res.hasUpdate) {\n\t\t\t\t\tupdateManager.onUpdateReady(function(res2) {\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle: \"更新提示\",\n\t\t\t\t\t\t\tcontent: \"新版本已经上线，请重启小程序\",\n\t\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\t\tconfirmText: \"重启\",\n\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\tres.confirm && updateManager.applyUpdate();\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t\tupdateManager?.onUpdateFailed(function(res) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: \"更新提示\",\n\t\t\t\t\tcontent: '新版本已经上线，请您删除当前小程序，到微信 \"发现-小程序\" 页，重新搜索打开呦~',\n\t\t\t\t\tshowCancel: false,\n\t\t\t\t\tconfirmText: \"知道了\",\n\t\t\t\t});\n\t\t\t});\n\t\t\t// #endif\n\n\t\t\t// 检查登录状态\n\t\t\t// this.checkLoginStatus();\n\t\t\treqWxCodeLogin();\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\t// 隐私授权检查\n\t\t\tif (!wx.requirePrivacyAuthorize) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: \"提示\",\n\t\t\t\t\tcontent: '当前微信版本过低，请更新微信为最新版本才可使用此小程序',\n\t\t\t\t\tshowCancel: false,\n\t\t\t\t\tconfirmText: \"好的\",\n\t\t\t\t\tsuccess() {\n\t\t\t\t\t\tuni.exitMiniProgram()\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\twx.requirePrivacyAuthorize({\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tconsole.log('requirePrivacyAuthorize success')\n\t\t\t\t},\n\t\t\t\tfail: (e) => {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: \"提示\",\n\t\t\t\t\t\tcontent: '用户已拒绝授权，将退出小程序',\n\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\tconfirmText: \"好的\",\n\t\t\t\t\t\tsuccess() {\n\t\t\t\t\t\t\tuni.exitMiniProgram()\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\tconsole.log(e)\n\t\t\t\t}, // 用户拒绝授权\n\t\t\t\tcomplete: () => {}\n\t\t\t})\n\t\t\t// #endif\n\t\t},\n\t\tonShow: function() {\n\t\t\tconsole.log('App Show')\n\t\t},\n\t\tonHide: function() {\n\t\t\tconsole.log('App Hide')\n\t\t},\n\t\tmethods: {\n\t\t\t \n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t/**twcss开始**/\n\t@import 'tailwindcss/base';\n\t@import 'tailwindcss/utilities';\n\t/**twcss结束**/\n\t// @import 'tailwindcss/components'; \n\t\n\t/* #ifdef APP-PLUS || H5 */\n\t$fa-font-path: '/styles/fonts';\n\t/* #endif */\n\t/* #ifndef APP-PLUS || H5 */\n\t$fa-font-path: '//cdn.bootcdn.net/ajax/libs/font-awesome/6.5.1/webfonts';\n\t/* #endif */\n\t\n\t@import '/styles/scss/fontawesome.scss';\n\t@import '/styles/scss/brands.scss';\n\t@import '/styles/scss/solid.scss';\n\t@import '/styles/scss/regular.scss';\n\t\n\t\n\t@import '/styles/common.scss';\n\t\n\t/* App.vue 或 uni.scss */\n\t*,\n\t*::before,\n\t*::after,\n\tinput,\n\ttextarea,\n\tscroll-view {\n\t\tbox-sizing: border-box;\n\n\t}\n\t\n\t \n\t\n\t \n\t\n\tbutton {\n\t\t// line-height: normal;\n\t\t&::after {\n\t\t\tdisplay: none;\n\t\t}\n\t\t\n\t\t&::after {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n\t \n\t\n\t.share-button {\n\t\tpadding: 0;\n\t\tbackground-color: transparent;\n\t\tborder: none;\n\t\n\t\t-webkit-tap-highlight-color: transparent;\n\t\n\t\t&::after {\n\t\t\tdisplay: none;\n\t\t}\n\t\n\t\t&::after {\n\t\t\tborder: none;\n\t\t}\n\t}\n\t\n\tview,\n\tbutton {\n\t\t-webkit-tap-highlight-color: transparent;\n\t\tuser-select: none;\n\t}\n\t\n\t/* #ifdef H5 */\n\t \n\t\n\t \n\t\n\tsvg {\n\t  display: initial;\n\t}\n\t\n\t/* #endif */\n</style>", "import App from './App'\n\n// #ifndef VUE3\nimport Vue from 'vue'\nimport './uni.promisify.adaptor'\n\n// 导入LoadingView组件\nimport LoadingView from './components/loading-view/loading-view.vue'\n\nVue.config.productionTip = false\n// 注册为全局组件\nVue.component('loading-view', LoadingView)\n\nApp.mpType = 'app'\nconst app = new Vue({\n\t...App\n})\napp.$mount()\n// #endif\n\n// #ifdef VUE3\nimport {\n\tcreateSSRApp\n} from 'vue'\nimport { reqGet, reqPost } from './myjs/req.js'\n\nimport store from './store'\n\n \n \n\n// 登录守卫 - 检查需要登录的页面\nfunction checkLoginRequired(url) {\n\t// 不需要登录的页面列表\n\tconst publicPages = [\n\t\t'/pages/login/login',\n\t\t'/pages/index/index',\n\t\t'/pages/agreement/user',\n\t\t'/pages/agreement/privacy'\n\t];\n\t\n\t// 检查当前页面是否在公开页面列表中\n\treturn !publicPages.some(page => url.includes(page));\n}\n\n// 页面跳转拦截器\nfunction setupNavigationGuard() {\n\t// 拦截 uni.navigateTo\n\tconst originalNavigateTo = uni.navigateTo;\n\tuni.navigateTo = function(options) {\n\t\tif (checkLoginRequired(options.url)) {\n\t\t\tconst token = uni.getStorageSync('h5_token');\n\t\t\tif (!token) {\n\t\t\t\tconst loginUrl = \"/pages/login/login\";\n\t\t\t\treturn uni.redirectTo({\n\t\t\t\t\turl: `${loginUrl}?loginCallbackUrl=${encodeURIComponent(options.url)}`\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t\treturn originalNavigateTo.call(this, options);\n\t};\n\t\n\t// 拦截 uni.redirectTo\n\tconst originalRedirectTo = uni.redirectTo;\n\tuni.redirectTo = function(options) {\n\t\tif (checkLoginRequired(options.url)) {\n\t\t\tconst token = uni.getStorageSync('h5_token');\n\t\t\tif (!token) {\n\t\t\t\tconst loginUrl = \"/pages/login/login\";\n\t\t\t\toptions.url = `${loginUrl}?loginCallbackUrl=${encodeURIComponent(options.url)}`;\n\t\t\t}\n\t\t}\n\t\treturn originalRedirectTo.call(this, options);\n\t};\n\t\n\t// 拦截 uni.switchTab\n\tconst originalSwitchTab = uni.switchTab;\n\tuni.switchTab = function(options) {\n\t\tif (checkLoginRequired(options.url)) {\n\t\t\tconst token = uni.getStorageSync('h5_token');\n\t\t\tif (!token) {\n\t\t\t\tconst loginUrl = \"/pages/login/login\";\n\t\t\t\treturn uni.redirectTo({\n\t\t\t\t\turl: `${loginUrl}?loginCallbackUrl=${encodeURIComponent(options.url)}`\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t\treturn originalSwitchTab.call(this, options);\n\t};\n}\n\nexport function createApp() {\n\tconst app = createSSRApp(App)\n\tapp.config.globalProperties.$reqGet = reqGet;\n\tapp.config.globalProperties.$reqPost = reqPost;\n\t\n \n\t\n\t// 设置导航守卫\n\tsetupNavigationGuard();\n\tapp.use(store)\n\t\n\treturn {\n\t\tapp\n\t}\n}\n// #endif"], "names": ["uni", "res", "reqWxCodeLogin", "wx", "createSSRApp", "App", "reqGet", "reqPost", "store"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGC,MAAK,YAAU;AAAA,EACd,UAAU,WAAW;AACpBA,kBAAAA,MAAY,MAAA,OAAA,gBAAA,YAAY;AAGxB,UAAM,gBAAgBA,oBAAI;AAC1B,mDAAe,iBAAiB,SAAS,KAAK;AAE7C,UAAI,IAAI,WAAW;AAClB,sBAAc,cAAc,SAAS,MAAM;AAC1CA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,aAAa;AAAA,YACb,SAAS,CAACC,SAAQ;AACjB,cAAAA,KAAI,WAAW,cAAc;YAC7B;AAAA,UACF,CAAC;AAAA,QACF,CAAC;AAAA,MACF;AAAA,IACD;AACA,mDAAe,eAAe,SAAS,KAAK;AAC3CD,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,aAAa;AAAA,MACd,CAAC;AAAA,IACF;AAKAE,aAAAA;AAGA,QAAI,CAACC,cAAE,KAAC,yBAAyB;AAChCH,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,UAAU;AACTA,wBAAAA,MAAI,gBAAgB;AAAA,QACrB;AAAA,OACA;AACD;AAAA,IACD;AAEAG,kBAAAA,KAAG,wBAAwB;AAAA,MAC1B,SAAS,MAAM;AACdH,sBAAAA,MAAA,MAAA,OAAA,iBAAY,iCAAiC;AAAA,MAC7C;AAAA,MACD,MAAM,CAAC,MAAM;AACZA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,UAAU;AACTA,0BAAAA,MAAI,gBAAgB;AAAA,UACrB;AAAA,SACA;AACDA,sBAAAA,MAAA,MAAA,OAAA,iBAAY,CAAC;AAAA,MACb;AAAA;AAAA,MACD,UAAU,MAAM;AAAA,MAAC;AAAA,KACjB;AAAA,EAED;AAAA,EACD,QAAQ,WAAW;AAClBA,kBAAAA,MAAY,MAAA,OAAA,iBAAA,UAAU;AAAA,EACtB;AAAA,EACD,QAAQ,WAAW;AAClBA,kBAAAA,MAAY,MAAA,OAAA,iBAAA,UAAU;AAAA,EACtB;AAAA,EACD,SAAS,CAET;AACD;AClDD,SAAS,mBAAmB,KAAK;AAEhC,QAAM,cAAc;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAGC,SAAO,CAAC,YAAY,KAAK,UAAQ,IAAI,SAAS,IAAI,CAAC;AACpD;AAGA,SAAS,uBAAuB;AAE/B,QAAM,qBAAqBA,cAAG,MAAC;AAC/BA,sBAAI,aAAa,SAAS,SAAS;AAClC,QAAI,mBAAmB,QAAQ,GAAG,GAAG;AACpC,YAAM,QAAQA,cAAAA,MAAI,eAAe,UAAU;AAC3C,UAAI,CAAC,OAAO;AACX,cAAM,WAAW;AACjB,eAAOA,cAAAA,MAAI,WAAW;AAAA,UACrB,KAAK,GAAG,QAAQ,qBAAqB,mBAAmB,QAAQ,GAAG,CAAC;AAAA,QACzE,CAAK;AAAA,MACD;AAAA,IACD;AACD,WAAO,mBAAmB,KAAK,MAAM,OAAO;AAAA,EAC9C;AAGC,QAAM,qBAAqBA,cAAG,MAAC;AAC/BA,sBAAI,aAAa,SAAS,SAAS;AAClC,QAAI,mBAAmB,QAAQ,GAAG,GAAG;AACpC,YAAM,QAAQA,cAAAA,MAAI,eAAe,UAAU;AAC3C,UAAI,CAAC,OAAO;AACX,cAAM,WAAW;AACjB,gBAAQ,MAAM,GAAG,QAAQ,qBAAqB,mBAAmB,QAAQ,GAAG,CAAC;AAAA,MAC7E;AAAA,IACD;AACD,WAAO,mBAAmB,KAAK,MAAM,OAAO;AAAA,EAC9C;AAGC,QAAM,oBAAoBA,cAAG,MAAC;AAC9BA,sBAAI,YAAY,SAAS,SAAS;AACjC,QAAI,mBAAmB,QAAQ,GAAG,GAAG;AACpC,YAAM,QAAQA,cAAAA,MAAI,eAAe,UAAU;AAC3C,UAAI,CAAC,OAAO;AACX,cAAM,WAAW;AACjB,eAAOA,cAAAA,MAAI,WAAW;AAAA,UACrB,KAAK,GAAG,QAAQ,qBAAqB,mBAAmB,QAAQ,GAAG,CAAC;AAAA,QACzE,CAAK;AAAA,MACD;AAAA,IACD;AACD,WAAO,kBAAkB,KAAK,MAAM,OAAO;AAAA,EAC7C;AACA;AAEO,SAAS,YAAY;AAC3B,QAAM,MAAMI,cAAY,aAACC,SAAG;AAC5B,MAAI,OAAO,iBAAiB,UAAUC,SAAAA;AACtC,MAAI,OAAO,iBAAiB,WAAWC,SAAAA;AAKvC;AACA,MAAI,IAAIC,iBAAK;AAEb,SAAO;AAAA,IACN;AAAA,EACA;AACF;;;"}