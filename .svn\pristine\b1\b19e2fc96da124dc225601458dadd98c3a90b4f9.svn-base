<template>
	<view>
		<view class="flex justify-end bg-white px-4 py-2">
			<template v-if="!showResult">
				<view class="ml-4">
					<text class="tagTip bg-yellow-500"></text><text class="text-sm text-gray-500">已答：{{ answeredNum }}</text>
				</view>
				<view class="ml-4">
					<text class="tagTip bg-gray-300"></text><text
						class="text-sm text-gray-500">未答：{{ notAnswerNum }}</text>
				</view>
			</template>
			<template v-else>
				<view class="ml-4">
					<text class="tagTip bg-green-500"></text><text class="text-sm text-gray-500">答对：{{ correctNum }}</text>
				</view>
				<view class="ml-4">
					<text class="tagTip bg-red-500"></text><text class="text-sm text-gray-500">答错：{{ wrongNum }}</text>
				</view>
				<view class="ml-4">
					<text class="tagTip bg-gray-300"></text><text
						class="text-sm text-gray-500">未答：{{ notAnswerNum }}</text>
				</view>
			</template>
		</view>

		<view v-for="item in questionTypes"   :key="item.value">
			<view class="m-4">
				<view class="my-4">
					<text class="text-base font-bold">{{
					  item.label
					}}</text>
				</view>

				<view class="flex flex-wrap">
					<view :key="value.index" class="mx-1 mb-1" v-for="value in item.questionList"
						@click="$emit('indexClick', value.index, value)">
						<view class="record" :class="
	                value.state == 0
	                  ? 'bg-gray-300'
	                  : !showResult
	                  ? 'bg-yellow-500'
	                  : value.state == 1
	                  ? 'bg-red-500'
	                  : 'bg-green-500'
	              ">
							{{ value.index + 1 }}
						</view>
					</view>
				</view>


			</view>
		</view>
	</view>
</template>

<script>
	const questionTypeEnum = [{
			label: "单选题",
			value: 1
		},
		{
			label: "多选题",
			value: 2
		},
		{
			label: "判断题",
			value: 3
		},
		{
			label: "填空题",
			value: 4,

		},
		{
			label: "问答题",
			value: 5,

		},
		{
			label: "组合题",
			value: 9,

		},
	];

	export default {
		name: "answer-card",
		props: {
			questionList: Array,
			showResult: Boolean
		},
		computed: {
			questionTypes() {
				const questionList = this.questionList || [];
				const copyQuestionTypeEnum = questionTypeEnum.map((item) => ({
					...item,
					questionList: [],
				}));
				questionList.forEach((item, index) => {
					copyQuestionTypeEnum.forEach((questionType) => {
						if (questionType.value == 9 && item.parentId !== "0") {
							questionType.questionList.push({
								index,
								state: item.state
							});
						} else if (
							(item.parentId === "0" || !item.parentId) &&
							item.questionType == questionType.value
						) {
							questionType.questionList.push({
								index,
								state: item.state
							});
						}
					});
				});

				return copyQuestionTypeEnum.filter((item) => item.questionList.length > 0);
			},
			notAnswerNum() {
				const questionList = this.questionList || [];
				return questionList.filter((item) => item.state == 0).length;
			},
			answeredNum() {
				const questionList = this.questionList || [];
				return questionList.filter((item) => item.state != 0).length;
			},
			wrongNum() {
				const questionList = this.questionList || [];
				return questionList.filter((item) => item.state == 1).length;
			},
			correctNum() {
				const questionList = this.questionList || [];
				return questionList.filter((item) => item.state == 2).length;
			}
		},
		data() {
			return {

			};
		}
	}
</script>
<style lang="scss">
	.tagTip {
		width: 22rpx;
		height: 22rpx;
		line-height: 26rpx;
		text-align: center;
		padding: 0 0;
		border-radius: 50%;
		display: inline-block;
		margin-right: 10rpx;
	}

	.record {
		width: 76rpx;
		height: 76rpx;
		text-align: center;
		line-height: 76rpx;
		border-width: 2rpx;
		border-style: solid;
		border-radius: 42rpx;
		// background-color: #cfd4d8;
		color: #ffffff;
		// margin: 22rpx;
		font-size: 28rpx;
		box-sizing: border-box;
	}
 
</style>