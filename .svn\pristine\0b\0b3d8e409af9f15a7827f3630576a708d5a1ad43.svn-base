
const key = "qbankSetting";


const qbankSettingDefaultValue = {
	randomQuestionNum: 30,
	fontSize: 1,
	autoShowResult: true,
	autoJumpNext: true,
};


const qbankSettingStorageValue = uni.getStorageSync(key) || {};

const qbankSettingValue = {
	...qbankSettingDefaultValue,
	...qbankSettingStorageValue
};
export const qbankSetting = {
	namespaced:true,
	state: () => ({
		settingValue: qbankSettingValue
	}),
	mutations: {
		update(state, val) {
			uni.setStorageSync(key, val);
			state.settingValue = val;
		},
	}

}

 