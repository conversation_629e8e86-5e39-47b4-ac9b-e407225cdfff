"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const myjs_req = require("./myjs/req.js");
const store_index = require("./store/index.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/login/login.js";
  "./pages/questionbank/list.js";
  "./pages/questionbank/detail.js";
  "./pages/questionbank/practice.js";
  "./pages/questionbank/permission.js";
  "./pages/questionbank/chapter.js";
  "./pages/question/list.js";
  "./pages/question/add.js";
  "./pages/question/edit.js";
  "./pages/profile/index.js";
  "./pages/agreement/user.js";
  "./pages/agreement/privacy.js";
  "./pages/question/imp.js";
  "./pages/question/textImp.js";
  "./pages/question/impPreview.js";
  "./pages/question/fileImp.js";
  "./pages/questionbank/passwordList.js";
  "./pages/practice/chapter.js";
  "./pages/practice/do-chapter.js";
  "./pages/practice/card.js";
  "./pages/practice/result.js";
  "./pages/practice/setting.js";
  "./pages/practice/analysis.js";
  "./pages/practice/wrongQuestion.js";
  "./pages/practice/collectionQuestion.js";
  "./pages/practice/mock-exam.js";
  "./pages/practice/questionType.js";
}
const _sfc_main = {
  onLaunch: function() {
    common_vendor.index.__f__("log", "at App.vue:6", "App Launch");
    const updateManager = common_vendor.index.getUpdateManager();
    updateManager == null ? void 0 : updateManager.onCheckForUpdate(function(res) {
      if (res.hasUpdate) {
        updateManager.onUpdateReady(function(res2) {
          common_vendor.index.showModal({
            title: "更新提示",
            content: "新版本已经上线，请重启小程序",
            showCancel: false,
            confirmText: "重启",
            success: (res3) => {
              res3.confirm && updateManager.applyUpdate();
            }
          });
        });
      }
    });
    updateManager == null ? void 0 : updateManager.onUpdateFailed(function(res) {
      common_vendor.index.showModal({
        title: "更新提示",
        content: '新版本已经上线，请您删除当前小程序，到微信 "发现-小程序" 页，重新搜索打开呦~',
        showCancel: false,
        confirmText: "知道了"
      });
    });
    myjs_req.reqWxCodeLogin();
    if (!common_vendor.wx$1.requirePrivacyAuthorize) {
      common_vendor.index.showModal({
        title: "提示",
        content: "当前微信版本过低，请更新微信为最新版本才可使用此小程序",
        showCancel: false,
        confirmText: "好的",
        success() {
          common_vendor.index.exitMiniProgram();
        }
      });
      return;
    }
    common_vendor.wx$1.requirePrivacyAuthorize({
      success: () => {
        common_vendor.index.__f__("log", "at App.vue:56", "requirePrivacyAuthorize success");
      },
      fail: (e) => {
        common_vendor.index.showModal({
          title: "提示",
          content: "用户已拒绝授权，将退出小程序",
          showCancel: false,
          confirmText: "好的",
          success() {
            common_vendor.index.exitMiniProgram();
          }
        });
        common_vendor.index.__f__("log", "at App.vue:68", e);
      },
      // 用户拒绝授权
      complete: () => {
      }
    });
  },
  onShow: function() {
    common_vendor.index.__f__("log", "at App.vue:75", "App Show");
  },
  onHide: function() {
    common_vendor.index.__f__("log", "at App.vue:78", "App Hide");
  },
  methods: {}
};
function checkLoginRequired(url) {
  const publicPages = [
    "/pages/login/login",
    "/pages/index/index",
    "/pages/agreement/user",
    "/pages/agreement/privacy"
  ];
  return !publicPages.some((page) => url.includes(page));
}
function setupNavigationGuard() {
  const originalNavigateTo = common_vendor.index.navigateTo;
  common_vendor.index.navigateTo = function(options) {
    if (checkLoginRequired(options.url)) {
      const token = common_vendor.index.getStorageSync("h5_token");
      if (!token) {
        const loginUrl = "/pages/login/login";
        return common_vendor.index.redirectTo({
          url: `${loginUrl}?loginCallbackUrl=${encodeURIComponent(options.url)}`
        });
      }
    }
    return originalNavigateTo.call(this, options);
  };
  const originalRedirectTo = common_vendor.index.redirectTo;
  common_vendor.index.redirectTo = function(options) {
    if (checkLoginRequired(options.url)) {
      const token = common_vendor.index.getStorageSync("h5_token");
      if (!token) {
        const loginUrl = "/pages/login/login";
        options.url = `${loginUrl}?loginCallbackUrl=${encodeURIComponent(options.url)}`;
      }
    }
    return originalRedirectTo.call(this, options);
  };
  const originalSwitchTab = common_vendor.index.switchTab;
  common_vendor.index.switchTab = function(options) {
    if (checkLoginRequired(options.url)) {
      const token = common_vendor.index.getStorageSync("h5_token");
      if (!token) {
        const loginUrl = "/pages/login/login";
        return common_vendor.index.redirectTo({
          url: `${loginUrl}?loginCallbackUrl=${encodeURIComponent(options.url)}`
        });
      }
    }
    return originalSwitchTab.call(this, options);
  };
}
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  app.config.globalProperties.$reqGet = myjs_req.reqGet;
  app.config.globalProperties.$reqPost = myjs_req.reqPost;
  setupNavigationGuard();
  app.use(store_index.store);
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
