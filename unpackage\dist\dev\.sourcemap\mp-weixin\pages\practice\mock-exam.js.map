{"version": 3, "file": "mock-exam.js", "sources": ["pages/practice/mock-exam.vue", "D:/Program Files/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHJhY3RpY2UvbW9jay1leGFtLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"h-screen bg-gray-50 flex flex-col\">\r\n    <!-- 自定义导航栏 - 固定在顶部 -->\r\n    <view class=\"fixed top-0 left-0 right-0 z-50\">\r\n      <!-- 导航栏主体 -->\r\n      <view\r\n        class=\"w-full bg-gradient-to-r from-primary-500 to-primary-600 relative overflow-hidden\"\r\n        :style=\"navBarStyle\"\r\n      >\r\n        <!-- 装饰背景 -->\r\n        <view class=\"absolute inset-0 opacity-10\">\r\n          <view\r\n            class=\"absolute top-6 right-6 w-8 h-8 rounded-full bg-white\"\r\n          ></view>\r\n          <view\r\n            class=\"absolute bottom-4 left-12 w-4 h-4 rounded-full bg-white\"\r\n          ></view>\r\n          <view\r\n            class=\"absolute top-10 left-1/2 w-2 h-2 rounded-full bg-white\"\r\n          ></view>\r\n        </view>\r\n\r\n        <!-- 返回按钮 -->\r\n        <view\r\n          :style=\"navBarButtonStyle\"\r\n          @click=\"goBack\"\r\n          class=\"flex items-center justify-center\"\r\n        >\r\n          <view\r\n            :style=\"backButtonStyle\"\r\n            class=\"bg-white bg-opacity-20 flex items-center justify-center backdrop-blur-sm rounded-full\"\r\n          >\r\n            <i class=\"fas fa-arrow-left text-white text-base\"></i>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 导航栏标题 -->\r\n        <view class=\"px-4 py-2 relative z-10\">\r\n          <view class=\"text-center\">\r\n            <view class=\"flex items-center justify-center\">\r\n              <view\r\n                class=\"w-6 h-6 rounded-full bg-white bg-opacity-20 flex items-center justify-center mr-2 backdrop-blur-sm\"\r\n              >\r\n                <i class=\"fas fa-clipboard-list text-white text-xs\"></i>\r\n              </view>\r\n              <text class=\"text-white text-base font-bold\">模拟考试</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 主体内容 -->\r\n    <view class=\"flex-1 pb-16\" :style=\"mainContentStyle\">\r\n      <!-- 装饰区域 - 和内容一起滚动，紧贴导航栏 -->\r\n      <view\r\n        class=\"bg-gradient-to-r from-primary-500 to-primary-600 mb-4 relative overflow-hidden -mt-4\"\r\n      >\r\n        <!-- 装饰背景 -->\r\n        <view class=\"absolute inset-0 opacity-10\">\r\n          <view\r\n            class=\"absolute top-8 right-12 w-10 h-10 rounded-full bg-white\"\r\n          ></view>\r\n          <view\r\n            class=\"absolute bottom-8 left-20 w-8 h-8 rounded-full bg-white\"\r\n          ></view>\r\n          <view\r\n            class=\"absolute top-16 left-1/4 w-4 h-4 rounded-full bg-white\"\r\n          ></view>\r\n        </view>\r\n\r\n        <!-- 装饰内容 -->\r\n        <view class=\"px-4 py-6 relative z-10\">\r\n          <view class=\"text-center mb-4\">\r\n            <text class=\"text-white text-sm opacity-90\"\r\n              >自定义题型和规则，模拟真实考试环境</text\r\n            >\r\n          </view>\r\n\r\n          <!-- 功能介绍 -->\r\n          <view class=\"bg-white bg-opacity-15 rounded-xl p-4 backdrop-blur-sm\">\r\n            <view class=\"flex items-center justify-center\">\r\n              <view class=\"flex items-center\">\r\n                <view\r\n                  class=\"w-8 h-8 rounded-full bg-white bg-opacity-30 flex items-center justify-center mr-3\"\r\n                >\r\n                  <i class=\"fas fa-cogs text-white text-sm\"></i>\r\n                </view>\r\n                <view>\r\n                  <text class=\"text-white text-sm font-medium block\"\r\n                    >自定义考试设置</text\r\n                  >\r\n                  <text class=\"text-white text-xs opacity-80\"\r\n                    >选择题型 · 设置分值 · 控制时长</text\r\n                  >\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      <!-- 存在未完成考试的情况 -->\r\n      <view\r\n        v-if=\"hasUnfinishedExam\"\r\n        class=\"mx-4 bg-white rounded-lg shadow p-4 mb-3\"\r\n      >\r\n        <view class=\"text-center\">\r\n          <view class=\"mb-4\">\r\n            <i class=\"fas fa-clock text-orange-500 text-4xl\"></i>\r\n          </view>\r\n          <text class=\"text-xl font-bold text-gray-800 mb-2 block\"\r\n            >未完成考试</text\r\n          >\r\n          <text class=\"text-gray-500 text-sm mb-6 block\"\r\n            >您有一次模拟考试尚未完成，可以选择继续作答或重新开始</text\r\n          >\r\n\r\n          <view class=\"space-y-3\">\r\n            <view>\r\n              <button\r\n                class=\"w-full bg-primary-500 text-white py-3 rounded-lg text-sm font-medium flex items-center justify-center\"\r\n                @click=\"continueExam\"\r\n              >\r\n                <i class=\"fas fa-play mr-2\"></i>继续考试\r\n              </button>\r\n            </view>\r\n            <view>\r\n              <button\r\n                class=\"w-full border border-solid border-gray-300 bg-white text-gray-600 py-3 rounded-lg text-sm font-medium flex items-center justify-center\"\r\n                @click=\"resetExam\"\r\n              >\r\n                <i class=\"fas fa-redo mr-2\"></i>重新开始\r\n              </button>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      <!-- 不存在未完成考试，显示规则设置 -->\r\n      <view v-else class=\"mx-4 bg-white rounded-xl shadow-sm p-4\">\r\n        <!-- 题型配置 -->\r\n        <view class=\"mb-5\">\r\n          <!-- 题型配置标题 -->\r\n          <view class=\"flex items-center mb-3\">\r\n            <i class=\"fas fa-list-alt text-primary-500 mr-2\"></i>\r\n            <text class=\"text-base font-semibold text-gray-800\">题型配置</text>\r\n          </view>\r\n\r\n          <!-- 题型列表 -->\r\n          <block v-for=\"(rule, index) in rules\" :key=\"rule.type\">\r\n            <view class=\"mb-3 bg-gray-50 rounded-lg p-3\">\r\n              <!-- 题型标题 -->\r\n              <view class=\"flex items-center justify-between mb-2\">\r\n                <text class=\"text-base font-medium text-gray-800\">{{\r\n                  rule.label\r\n                }}</text>\r\n                <text class=\"text-sm text-gray-500\"\r\n                  >题库{{ rule.total }}题</text\r\n                >\r\n              </view>\r\n\r\n              <!-- 设置项 -->\r\n              <view class=\"flex items-center space-x-3\">\r\n                <!-- 出题数量 -->\r\n                <view class=\"flex-1\">\r\n                  <text class=\"text-sm text-gray-600 mb-1 block\">出题数量</text>\r\n                  <view\r\n                    class=\"flex items-center bg-white rounded-lg border border-solid border-gray-200 p-1\"\r\n                  >\r\n                    <button\r\n                      @click=\"decreaseCount(index)\"\r\n                      class=\"w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500\"\r\n                    >\r\n                      <i class=\"fas fa-minus text-sm\"></i>\r\n                    </button>\r\n                    <input\r\n                      type=\"number\"\r\n                      v-model.number=\"rule.count\"\r\n                      min=\"0\"\r\n                      :max=\"rule.total\"\r\n                      class=\"flex-1 h-auto px-2 py-1 text-center text-base border-0 bg-transparent\"\r\n                      readonly\r\n                    />\r\n                    <button\r\n                      @click=\"increaseCount(index)\"\r\n                      class=\"w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500\"\r\n                    >\r\n                      <i class=\"fas fa-plus text-sm\"></i>\r\n                    </button>\r\n                  </view>\r\n                </view>\r\n\r\n                <!-- 每题分值 -->\r\n                <view class=\"flex-1\">\r\n                  <text class=\"text-sm text-gray-600 mb-1 block\">每题分值</text>\r\n                  <view\r\n                    class=\"flex items-center bg-white rounded-lg border border-solid border-gray-200 p-1\"\r\n                  >\r\n                    <button\r\n                      @click=\"decreaseScore(index)\"\r\n                      class=\"w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500\"\r\n                    >\r\n                      <i class=\"fas fa-minus text-sm\"></i>\r\n                    </button>\r\n                    <input\r\n                      type=\"text\"\r\n                      v-model=\"rule.score\"\r\n                      @blur=\"onScoreBlur(index, $event)\"\r\n                      class=\"w-12 h-auto px-2 py-1 text-center text-base border-0 bg-transparent\"\r\n                      placeholder=\"1.0\"\r\n                    />\r\n                    <button\r\n                      @click=\"increaseScore(index)\"\r\n                      class=\"w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500\"\r\n                    >\r\n                      <i class=\"fas fa-plus text-sm\"></i>\r\n                    </button>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </block>\r\n        </view>\r\n\r\n        <!-- 及格分数 -->\r\n        <view class=\"mb-4\">\r\n          <view class=\"flex items-center justify-between\">\r\n            <view class=\"flex items-center\">\r\n              <text class=\"text-base font-semibold text-gray-800\"\r\n                >及格分数</text\r\n              >\r\n              <text class=\"text-sm text-gray-500 ml-2\"\r\n                >（总分{{ totalScore }}分）</text\r\n              >\r\n            </view>\r\n            <view\r\n              class=\"flex items-center bg-gray-50 rounded-lg border border-solid border-gray-200 p-1\"\r\n            >\r\n              <button\r\n                @click=\"decreasePassScore()\"\r\n                class=\"w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500\"\r\n              >\r\n                <i class=\"fas fa-minus text-sm\"></i>\r\n              </button>\r\n              <input\r\n                type=\"text\"\r\n                v-model=\"passScore\"\r\n                @blur=\"onPassScoreBlur($event)\"\r\n                class=\"w-16 h-auto px-2 py-1 text-center text-base border-0 bg-transparent\"\r\n                placeholder=\"60\"\r\n              />\r\n              <button\r\n                @click=\"increasePassScore()\"\r\n                class=\"w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500\"\r\n              >\r\n                <i class=\"fas fa-plus text-sm\"></i>\r\n              </button>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 考试时长 -->\r\n        <view class=\"mb-4\">\r\n          <view class=\"flex items-center justify-between\">\r\n            <text class=\"text-base font-semibold text-gray-800\">考试时长</text>\r\n            <view\r\n              class=\"flex items-center bg-gray-50 rounded-lg border border-solid border-gray-200 p-1\"\r\n            >\r\n              <button\r\n                @click=\"decreaseDuration()\"\r\n                class=\"w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500\"\r\n              >\r\n                <i class=\"fas fa-minus text-sm\"></i>\r\n              </button>\r\n              <input\r\n                type=\"number\"\r\n                v-model.number=\"duration\"\r\n                min=\"1\"\r\n                max=\"480\"\r\n                step=\"1\"\r\n                @input=\"validateDuration\"\r\n                class=\"w-16 h-auto px-2 py-1 text-center text-base border-0 bg-transparent\"\r\n              />\r\n              <text class=\"text-sm text-gray-500 mr-2\">分钟</text>\r\n              <button\r\n                @click=\"increaseDuration()\"\r\n                class=\"w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500\"\r\n              >\r\n                <i class=\"fas fa-plus text-sm\"></i>\r\n              </button>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 选项乱序 -->\r\n        <view class=\"mb-4\">\r\n          <view class=\"flex items-center justify-between\">\r\n            <text class=\"text-base font-semibold text-gray-800\">选项乱序</text>\r\n            <view class=\"flex items-center\">\r\n              <switch\r\n                :checked=\"shuffleOption\"\r\n                @change=\"(e) => (shuffleOption = e.detail.value)\"\r\n                color=\"#3b82f6\"\r\n                class=\"mr-3\"\r\n              />\r\n              <text class=\"text-sm text-gray-500\">{{\r\n                shuffleOption ? \"开启\" : \"关闭\"\r\n              }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 开始考试按钮 -->\r\n        <button\r\n          class=\"w-full bg-primary-500 text-white py-3 rounded-lg text-sm font-medium flex items-center justify-center\"\r\n          @click=\"startExam\"\r\n        >\r\n          <i class=\"fas fa-play mr-2\"></i>开始考试\r\n        </button>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 右下角固定历史记录按钮 -->\r\n    <view class=\"fixed bottom-6 right-6 z-50\">\r\n      <button\r\n        class=\"w-12 h-12 rounded-full bg-primary-500 text-white shadow-lg flex items-center justify-center\"\r\n        @click=\"goHistory\"\r\n      >\r\n        <i class=\"fas fa-history text-xl\"></i>\r\n      </button>\r\n      <!-- 小红点提示（可选） -->\r\n      <view\r\n        v-if=\"hasNewHistory\"\r\n        class=\"absolute -top-1 -right-1 w-5 h-5 rounded-full bg-red-500 flex items-center justify-center\"\r\n      >\r\n        <text class=\"text-white text-xs font-bold\">!</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      navBarButtonStyle: {},\r\n      navBarStyle: {}, // 新增导航栏样式\r\n      backButtonStyle: {}, // 返回按钮样式\r\n      mainContentStyle: {}, // 主体内容样式\r\n      hasUnfinishedExam: true,\r\n      hasNewHistory: false, // 是否有新的历史记录提示\r\n      rules: [\r\n        { type: 1, label: \"单选题\", total: 20, count: 10, score: \"2.0\" },\r\n        { type: 2, label: \"多选题\", total: 10, count: 5, score: \"3.0\" },\r\n        { type: 3, label: \"判断题\", total: 5, count: 5, score: \"1.0\" },\r\n      ],\r\n      passScore: 60,\r\n      duration: 60,\r\n      shuffleOption: true,\r\n    };\r\n  },\r\n  computed: {\r\n    totalScore() {\r\n      return this.rules.reduce(\r\n        (sum, r) => sum + r.count * parseFloat(r.score || 0),\r\n        0\r\n      );\r\n    },\r\n  },\r\n  methods: {\r\n    setNavBarButtonStyle() {\r\n      let style = {};\r\n      // #ifdef MP-WEIXIN\r\n      const menuButton = uni.getMenuButtonBoundingClientRect();\r\n      style = {\r\n        position: \"absolute\",\r\n        left: \"16px\",\r\n        top: menuButton.top + \"px\",\r\n        width: menuButton.height + \"px\",\r\n        height: menuButton.height + \"px\",\r\n        \"z-index\": 20,\r\n      };\r\n      // #endif\r\n      // #ifdef H5\r\n      style = {\r\n        position: \"absolute\",\r\n        left: \"16px\",\r\n        top: \"16px\",\r\n        width: \"40px\",\r\n        height: \"40px\",\r\n        \"z-index\": 20,\r\n      };\r\n      // #endif\r\n      this.navBarButtonStyle = style;\r\n    },\r\n    setNavBarStyle() {\r\n      let style = {};\r\n      // #ifdef MP-WEIXIN\r\n      const sys = uni.getSystemInfoSync();\r\n\r\n      const navTop = sys.statusBarHeight;\r\n      style = {\r\n        \"padding-top\": navTop + \"px\",\r\n      };\r\n      // #endif\r\n      // #ifdef H5\r\n      style = {\r\n        \"padding-top\": \"16px\",\r\n      };\r\n      // #endif\r\n      this.navBarStyle = style;\r\n    },\r\n    setBackButtonStyle() {\r\n      let style = {};\r\n      // #ifdef MP-WEIXIN\r\n      const menuButton = uni.getMenuButtonBoundingClientRect();\r\n      const buttonSize = menuButton.height;\r\n      style = {\r\n        width: buttonSize + \"px\",\r\n        height: buttonSize + \"px\",\r\n      };\r\n      // #endif\r\n      // #ifdef H5\r\n      style = {\r\n        width: \"36px\",\r\n        height: \"36px\",\r\n      };\r\n      // #endif\r\n      this.backButtonStyle = style;\r\n    },\r\n    setMainContentStyle() {\r\n      let style = {};\r\n      // #ifdef MP-WEIXIN\r\n      const sys = uni.getSystemInfoSync();\r\n      const menuButton = uni.getMenuButtonBoundingClientRect();\r\n      // 只计算导航栏高度，不加多余间距\r\n      const navHeight = menuButton.top + menuButton.height;\r\n      style = {\r\n        \"padding-top\": navHeight + \"px\",\r\n      };\r\n      // #endif\r\n      // #ifdef H5\r\n      const statusBarHeight = 20;\r\n      style = {\r\n        \"padding-top\": statusBarHeight + \"px\",\r\n      };\r\n      // #endif\r\n      this.mainContentStyle = style;\r\n    },\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n\r\n    continueExam() {\r\n      uni.navigateTo({ url: \"/pages/practice/do-chapter?mode=mock\" });\r\n    },\r\n    resetExam() {\r\n      this.hasUnfinishedExam = false;\r\n    },\r\n    startExam() {\r\n      if (this.passScore > this.totalScore) {\r\n        uni.showToast({ title: \"及格分不能超过总分\", icon: \"none\" });\r\n        return;\r\n      }\r\n      if (this.duration < 1 || !Number.isInteger(this.duration)) {\r\n        uni.showToast({ title: \"考试时长需为正整数\", icon: \"none\" });\r\n        return;\r\n      }\r\n      if (this.duration > 480) {\r\n        uni.showToast({ title: \"考试时长不能超过480分钟\", icon: \"none\" });\r\n        return;\r\n      }\r\n      // 验证分数格式\r\n      for (let rule of this.rules) {\r\n        if (rule.count > 0 && rule.score < 0.1) {\r\n          uni.showToast({ title: \"分数不能小于0.1\", icon: \"none\" });\r\n          return;\r\n        }\r\n      }\r\n      uni.navigateTo({ url: \"/pages/practice/do-chapter?mode=mock\" });\r\n    },\r\n    goHistory() {\r\n      uni.navigateTo({ url: \"/pages/practice/mock-exam-history\" });\r\n    },\r\n\r\n    // +/- 按钮方法\r\n    decreaseCount(index) {\r\n      if (this.rules[index].count > 0) {\r\n        this.rules[index].count--;\r\n      }\r\n    },\r\n    increaseCount(index) {\r\n      if (this.rules[index].count < this.rules[index].total) {\r\n        this.rules[index].count++;\r\n      }\r\n    },\r\n    decreaseScore(index) {\r\n      const currentScore = parseFloat(this.rules[index].score) || 0.5;\r\n      if (currentScore > 0.5) {\r\n        const newScore = Math.round((currentScore - 0.5) * 10) / 10;\r\n        this.rules[index].score = newScore.toFixed(1);\r\n      }\r\n    },\r\n    increaseScore(index) {\r\n      const currentScore = parseFloat(this.rules[index].score) || 0;\r\n      const newScore = Math.round((currentScore + 0.5) * 10) / 10;\r\n      this.rules[index].score = newScore.toFixed(1);\r\n    },\r\n    decreasePassScore() {\r\n      if (this.passScore > 1) {\r\n        this.passScore = this.passScore - 1;\r\n      }\r\n    },\r\n    increasePassScore() {\r\n      if (this.passScore < this.totalScore) {\r\n        this.passScore = this.passScore + 1;\r\n      }\r\n    },\r\n    decreaseDuration() {\r\n      if (this.duration > 1) {\r\n        this.duration--;\r\n      }\r\n    },\r\n    increaseDuration() {\r\n      if (this.duration < 480) {\r\n        this.duration++;\r\n      }\r\n    },\r\n\r\n    // 解析并限制分数格式\r\n    parseScore(value) {\r\n      // 移除非数字和小数点的字符\r\n      value = value.replace(/[^\\d.]/g, \"\");\r\n\r\n      // 确保只有一个小数点\r\n      const parts = value.split(\".\");\r\n      if (parts.length > 2) {\r\n        value = parts[0] + \".\" + parts.slice(1).join(\"\");\r\n      }\r\n\r\n      // 限制小数位数为1位\r\n      if (parts.length === 2 && parts[1].length > 1) {\r\n        value = parts[0] + \".\" + parts[1].substring(0, 1);\r\n      }\r\n\r\n      const numValue = parseFloat(value);\r\n      if (isNaN(numValue)) return 0.5;\r\n      if (numValue < 0.5) return 0.5;\r\n\r\n      return Math.round(numValue * 10) / 10;\r\n    },\r\n\r\n    // 题型分数失焦处理\r\n    onScoreBlur(index, event) {\r\n      // 失焦时进行格式化和验证\r\n      const value = this.rules[index].score;\r\n      const formattedScore = this.parseScore(value.toString());\r\n      // 统一显示为一位小数格式\r\n      this.rules[index].score = formattedScore.toFixed(1);\r\n    },\r\n\r\n    // 及格分数失焦处理\r\n    onPassScoreBlur(event) {\r\n      // 失焦时进行格式化和验证\r\n      const value = this.passScore;\r\n      let score = this.parseScore(value.toString());\r\n      if (score > this.totalScore) {\r\n        score = this.totalScore;\r\n      }\r\n      this.passScore = score;\r\n    },\r\n\r\n    // 时长验证方法\r\n    validateDuration() {\r\n      this.$nextTick(() => {\r\n        if (this.duration < 1) {\r\n          this.duration = 1;\r\n        } else if (this.duration > 480) {\r\n          this.duration = 480;\r\n        }\r\n      });\r\n    },\r\n  },\r\n  onLoad(options) {\r\n    this.setNavBarButtonStyle();\r\n    this.setNavBarStyle();\r\n    this.setBackButtonStyle();\r\n    this.setMainContentStyle();\r\n  },\r\n};\r\n</script>\r\n", "import MiniProgramPage from 'E:/work/code/edu/edu-personal-uniapp/pages/practice/mock-exam.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAqVA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,mBAAmB,CAAE;AAAA,MACrB,aAAa,CAAE;AAAA;AAAA,MACf,iBAAiB,CAAE;AAAA;AAAA,MACnB,kBAAkB,CAAE;AAAA;AAAA,MACpB,mBAAmB;AAAA,MACnB,eAAe;AAAA;AAAA,MACf,OAAO;AAAA,QACL,EAAE,MAAM,GAAG,OAAO,OAAO,OAAO,IAAI,OAAO,IAAI,OAAO,MAAO;AAAA,QAC7D,EAAE,MAAM,GAAG,OAAO,OAAO,OAAO,IAAI,OAAO,GAAG,OAAO,MAAO;AAAA,QAC5D,EAAE,MAAM,GAAG,OAAO,OAAO,OAAO,GAAG,OAAO,GAAG,OAAO,MAAO;AAAA,MAC5D;AAAA,MACD,WAAW;AAAA,MACX,UAAU;AAAA,MACV,eAAe;AAAA;EAElB;AAAA,EACD,UAAU;AAAA,IACR,aAAa;AACX,aAAO,KAAK,MAAM;AAAA,QAChB,CAAC,KAAK,MAAM,MAAM,EAAE,QAAQ,WAAW,EAAE,SAAS,CAAC;AAAA,QACnD;AAAA;IAEH;AAAA,EACF;AAAA,EACD,SAAS;AAAA,IACP,uBAAuB;AACrB,UAAI,QAAQ,CAAA;AAEZ,YAAM,aAAaA,oBAAI;AACvB,cAAQ;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,QACN,KAAK,WAAW,MAAM;AAAA,QACtB,OAAO,WAAW,SAAS;AAAA,QAC3B,QAAQ,WAAW,SAAS;AAAA,QAC5B,WAAW;AAAA;AAab,WAAK,oBAAoB;AAAA,IAC1B;AAAA,IACD,iBAAiB;AACf,UAAI,QAAQ,CAAA;AAEZ,YAAM,MAAMA,oBAAI;AAEhB,YAAM,SAAS,IAAI;AACnB,cAAQ;AAAA,QACN,eAAe,SAAS;AAAA;AAQ1B,WAAK,cAAc;AAAA,IACpB;AAAA,IACD,qBAAqB;AACnB,UAAI,QAAQ,CAAA;AAEZ,YAAM,aAAaA,oBAAI;AACvB,YAAM,aAAa,WAAW;AAC9B,cAAQ;AAAA,QACN,OAAO,aAAa;AAAA,QACpB,QAAQ,aAAa;AAAA;AASvB,WAAK,kBAAkB;AAAA,IACxB;AAAA,IACD,sBAAsB;AACpB,UAAI,QAAQ,CAAA;AAEAA,oBAAAA,MAAI,kBAAmB;AACnC,YAAM,aAAaA,oBAAI;AAEvB,YAAM,YAAY,WAAW,MAAM,WAAW;AAC9C,cAAQ;AAAA,QACN,eAAe,YAAY;AAAA;AAS7B,WAAK,mBAAmB;AAAA,IACzB;AAAA,IACD,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IAED,eAAe;AACbA,oBAAAA,MAAI,WAAW,EAAE,KAAK,uCAAwC,CAAA;AAAA,IAC/D;AAAA,IACD,YAAY;AACV,WAAK,oBAAoB;AAAA,IAC1B;AAAA,IACD,YAAY;AACV,UAAI,KAAK,YAAY,KAAK,YAAY;AACpCA,sBAAG,MAAC,UAAU,EAAE,OAAO,aAAa,MAAM,OAAK,CAAG;AAClD;AAAA,MACF;AACA,UAAI,KAAK,WAAW,KAAK,CAAC,OAAO,UAAU,KAAK,QAAQ,GAAG;AACzDA,sBAAG,MAAC,UAAU,EAAE,OAAO,aAAa,MAAM,OAAK,CAAG;AAClD;AAAA,MACF;AACA,UAAI,KAAK,WAAW,KAAK;AACvBA,sBAAG,MAAC,UAAU,EAAE,OAAO,iBAAiB,MAAM,OAAO,CAAC;AACtD;AAAA,MACF;AAEA,eAAS,QAAQ,KAAK,OAAO;AAC3B,YAAI,KAAK,QAAQ,KAAK,KAAK,QAAQ,KAAK;AACtCA,wBAAG,MAAC,UAAU,EAAE,OAAO,aAAa,MAAM,OAAK,CAAG;AAClD;AAAA,QACF;AAAA,MACF;AACAA,oBAAAA,MAAI,WAAW,EAAE,KAAK,uCAAwC,CAAA;AAAA,IAC/D;AAAA,IACD,YAAY;AACVA,oBAAAA,MAAI,WAAW,EAAE,KAAK,oCAAqC,CAAA;AAAA,IAC5D;AAAA;AAAA,IAGD,cAAc,OAAO;AACnB,UAAI,KAAK,MAAM,KAAK,EAAE,QAAQ,GAAG;AAC/B,aAAK,MAAM,KAAK,EAAE;AAAA,MACpB;AAAA,IACD;AAAA,IACD,cAAc,OAAO;AACnB,UAAI,KAAK,MAAM,KAAK,EAAE,QAAQ,KAAK,MAAM,KAAK,EAAE,OAAO;AACrD,aAAK,MAAM,KAAK,EAAE;AAAA,MACpB;AAAA,IACD;AAAA,IACD,cAAc,OAAO;AACnB,YAAM,eAAe,WAAW,KAAK,MAAM,KAAK,EAAE,KAAK,KAAK;AAC5D,UAAI,eAAe,KAAK;AACtB,cAAM,WAAW,KAAK,OAAO,eAAe,OAAO,EAAE,IAAI;AACzD,aAAK,MAAM,KAAK,EAAE,QAAQ,SAAS,QAAQ,CAAC;AAAA,MAC9C;AAAA,IACD;AAAA,IACD,cAAc,OAAO;AACnB,YAAM,eAAe,WAAW,KAAK,MAAM,KAAK,EAAE,KAAK,KAAK;AAC5D,YAAM,WAAW,KAAK,OAAO,eAAe,OAAO,EAAE,IAAI;AACzD,WAAK,MAAM,KAAK,EAAE,QAAQ,SAAS,QAAQ,CAAC;AAAA,IAC7C;AAAA,IACD,oBAAoB;AAClB,UAAI,KAAK,YAAY,GAAG;AACtB,aAAK,YAAY,KAAK,YAAY;AAAA,MACpC;AAAA,IACD;AAAA,IACD,oBAAoB;AAClB,UAAI,KAAK,YAAY,KAAK,YAAY;AACpC,aAAK,YAAY,KAAK,YAAY;AAAA,MACpC;AAAA,IACD;AAAA,IACD,mBAAmB;AACjB,UAAI,KAAK,WAAW,GAAG;AACrB,aAAK;AAAA,MACP;AAAA,IACD;AAAA,IACD,mBAAmB;AACjB,UAAI,KAAK,WAAW,KAAK;AACvB,aAAK;AAAA,MACP;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,OAAO;AAEhB,cAAQ,MAAM,QAAQ,WAAW,EAAE;AAGnC,YAAM,QAAQ,MAAM,MAAM,GAAG;AAC7B,UAAI,MAAM,SAAS,GAAG;AACpB,gBAAQ,MAAM,CAAC,IAAI,MAAM,MAAM,MAAM,CAAC,EAAE,KAAK,EAAE;AAAA,MACjD;AAGA,UAAI,MAAM,WAAW,KAAK,MAAM,CAAC,EAAE,SAAS,GAAG;AAC7C,gBAAQ,MAAM,CAAC,IAAI,MAAM,MAAM,CAAC,EAAE,UAAU,GAAG,CAAC;AAAA,MAClD;AAEA,YAAM,WAAW,WAAW,KAAK;AACjC,UAAI,MAAM,QAAQ;AAAG,eAAO;AAC5B,UAAI,WAAW;AAAK,eAAO;AAE3B,aAAO,KAAK,MAAM,WAAW,EAAE,IAAI;AAAA,IACpC;AAAA;AAAA,IAGD,YAAY,OAAO,OAAO;AAExB,YAAM,QAAQ,KAAK,MAAM,KAAK,EAAE;AAChC,YAAM,iBAAiB,KAAK,WAAW,MAAM,SAAU,CAAA;AAEvD,WAAK,MAAM,KAAK,EAAE,QAAQ,eAAe,QAAQ,CAAC;AAAA,IACnD;AAAA;AAAA,IAGD,gBAAgB,OAAO;AAErB,YAAM,QAAQ,KAAK;AACnB,UAAI,QAAQ,KAAK,WAAW,MAAM,SAAU,CAAA;AAC5C,UAAI,QAAQ,KAAK,YAAY;AAC3B,gBAAQ,KAAK;AAAA,MACf;AACA,WAAK,YAAY;AAAA,IAClB;AAAA;AAAA,IAGD,mBAAmB;AACjB,WAAK,UAAU,MAAM;AACnB,YAAI,KAAK,WAAW,GAAG;AACrB,eAAK,WAAW;AAAA,mBACP,KAAK,WAAW,KAAK;AAC9B,eAAK,WAAW;AAAA,QAClB;AAAA,MACF,CAAC;AAAA,IACF;AAAA,EACF;AAAA,EACD,OAAO,SAAS;AACd,SAAK,qBAAoB;AACzB,SAAK,eAAc;AACnB,SAAK,mBAAkB;AACvB,SAAK,oBAAmB;AAAA,EACzB;AACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3kBA,GAAG,WAAW,eAAe;"}