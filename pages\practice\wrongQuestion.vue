<template>
  <view class="h-screen bg-gray-50 flex flex-col">
    <!-- 头部统计 -->
    <view class="bg-white px-4 py-3 border-b border-gray-200">
      <view class="flex justify-between items-center">
        <view class="text-gray-700">
          共<text class="text-red-500 font-semibold mx-1">{{ total }}</text
          >题
        </view>
        <view class="flex items-center space-x-3">
          <button
            :class="[
              'flex items-center text-gray-600 text-sm',
              total < 1 ? 'opacity-50' : '',
            ]"
            :disabled="total < 1"
            @click="deleteAll"
          >
            <i class="fas fa-trash-alt mr-1"></i>
            全部清空
          </button>
          <button
            :class="[
              'px-4 py-2 rounded-lg text-sm font-medium',
              total < 1
                ? 'bg-gray-300 text-gray-500'
                : 'bg-primary-500 text-white',
            ]"
            :disabled="total < 1"
            @click="toPractice"
          >
            错题集练习
          </button>
        </view>
      </view>
    </view>

    <!-- 题目列表 -->
    <scroll-pagination
      class="flex-1 overflow-hidden"
      :page-size="12"
      :auto-load="true"
      ref="scrollPagination"
      :enable-refresh="true"
      @load="loadData"
    >
      <template v-slot="{ list }">
        <view class="px-4 py-2">
          <view
            v-for="item in list"
            :key="item.id"
            @click="toAnalysis(item)"
            class="bg-white rounded-lg shadow-sm border border-gray-100 mb-3 p-4"
          >
            <view class="mb-3">
              <mp-html :content="item.questionContent" />
            </view>

            <view class="flex justify-between items-center">
              <view class="flex items-center space-x-3">
                <view
                  class="bg-yellow-100 text-yellow-600 px-2 py-1 rounded text-xs font-medium"
                >
                  {{
                    questionTypeEnum.find(
                      (enumItem) => enumItem.value == item.questionType
                    )?.label
                  }}
                </view>
                <view class="text-sm text-gray-500">
                  错误次数:
                  <text class="text-red-500 font-medium">{{ item.num }}</text
                  >次
                </view>
              </view>
              <view class="text-sm text-gray-400">{{ item.updateTime }}</view>
            </view>
          </view>
        </view>
      </template>

      <template #empty>
        <view
          class="py-12 px-4 bg-white rounded-xl shadow-sm flex flex-col items-center justify-center"
        >
          <view
            class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3"
          >
            <text class="fas fa-smile text-gray-300 text-2xl"></text>
          </view>
          <text class="text-gray-500 text-sm">暂无错题啦，快去学习吧~</text>
        </view>
      </template>
    </scroll-pagination>
  </view>
</template>

<script>
import { questionTypeEnum } from "@/myjs/question.js";

export default {
  data() {
    return {
      // 题目类型枚举
      questionTypeEnum,
      total: 0,
    };
  },

  onLoad(options) {
    this.bankId = options.bankId;
  },
  onShow() {
    if (this._r) {
      this.$refs.scrollPagination.init();
      this._r = false;
    }
  },
  methods: {
    // 加载数据
    loadData(params, callback) {
      return this.$reqGet("/front/edu/user-wrong-question/page", {
        ...params,
        bankId: this.bankId,
      }).then((res) => {
        callback(res.data);
        this.total = res.data.total;
      });
    },

    // 跳转到解析页面
    toAnalysis(item) {
      uni.navigateTo({
        url: `/pages/practice/analysis?questionId=${item.questionId}`,
      });
    },

    // 跳转到练习页面
    toPractice() {
      this._r = true;
      uni.navigateTo({
        url: `/pages/practice/do?mode=wrong&bankId=${this.bankId}`,
      });
    },

    // 清空所有错题
    deleteAll() {
      uni.showModal({
        title: "确认清空",
        content: "确定要清空所有错题吗？",
        success: (res) => {
          if (res.confirm) {
            this.$reqPost("/front/edu/user-wrong-question/deleteAll", {
              bankId: this.bankId,
            }).then((res) => {
              if (res.success) {
                uni.showToast({
                  title: "清空成功",
                  icon: "success",
                });
                this.$refs.scrollPagination.init();
              }
            });
          }
        },
      });
    },
  },
};
</script>

<style scoped></style>
