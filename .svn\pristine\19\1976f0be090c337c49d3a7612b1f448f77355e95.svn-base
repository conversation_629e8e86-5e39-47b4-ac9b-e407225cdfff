<template>
	<view class="bg-gray-50 min-h-screen">
		<!-- Custom header with back button -->
		<view class="bg-primary-600 text-white p-4 flex items-center relative">
			<view class="absolute left-4" @click="() => uni.navigateBack()">
				<text class="fa-solid fa-chevron-left text-xl"></text>
			</view>
			<view class="flex-1 text-center">
				<text class="text-lg font-medium">登录</text>
			</view>
		</view>
		
		<!-- Main content with card design -->
		<view class="px-6 pt-8 flex flex-col items-center">
			<!-- Welcome text with better typography -->
			<view class="w-full mb-8">
				<text class="text-2xl font-bold text-gray-800">欢迎使用</text>
				<text class="text-sm text-gray-500 mt-2 block">请使用手机号快捷登录</text>
			</view>
			
			<!-- Illustration with animation -->
			<view class="flex justify-center items-center my-10 relative">
				<!-- Background circle -->
				<view class="absolute w-32 h-32 rounded-full bg-primary-100 opacity-70"></view>
				<!-- Icon with larger size and better color -->
				<text class="fa-solid fa-mobile-screen text-primary-600 text-8xl relative z-10"></text>
			</view>

      <template v-if="isPwdLogin">
        <view class="w-full space-y-6">
          <view>
            <text class="block text-sm font-medium text-gray-700 mb-2">账号</text>
            <input
              class="w-full px-4 py-3 border border-solid h-auto border-gray-200 rounded-xl text-sm focus:border-primary-500 focus:outline-none bg-white"
              placeholder="请输入账号/手机号"
              v-model="formData.username"
            />
          </view>
          <view>
            <text class="block text-sm font-medium text-gray-700 mb-2">密码</text>
            <input
              class="w-full px-4 py-3 border border-solid h-auto border-gray-200 rounded-xl text-sm focus:border-primary-500 focus:outline-none bg-white"
              placeholder="请输入密码"
              type="password"
              v-model="formData.password"
            />
          </view>
          <button
            class="w-full mt-4 py-3 bg-primary-600 text-white rounded-xl shadow-lg text-center font-medium text-lg transition-all duration-300 hover:bg-primary-700 active:bg-primary-800 active:scale-[0.98]"
            @click="submit"
          >
            登录
          </button>
        </view>
      </template>
			
			<!-- Login button with improved design -->
			<template v-else>

        <button open-type="getPhoneNumber" 
				class="w-full mt-8 py-4 bg-primary-600 text-white rounded-xl shadow-lg text-center font-medium text-lg relative overflow-hidden transition-all duration-300 hover:bg-primary-700 active:bg-primary-800 active:scale-[0.98]"
				@getphonenumber="getPhoneNumber">
				<text class="fa-solid fa-lock mr-2"></text>
				手机号快捷登录
			</button>

      </template>
			
			<!-- Additional info -->
			<view class="mt-6 text-center text-xs text-gray-400">
				登录即表示您同意我们的
				<text class="text-primary-600">服务条款</text>
				和
				<text class="text-primary-600">隐私政策</text>
			</view>
		</view>
	</view>
</template>

<script>
	let loginCallbackUrl = "";
	let navBack = "";
	 
	import store from '@/store/index'

	let isH5 = false;
	// #ifdef H5
	isH5 = true;
	// #endif

	export default {
	 
		data() {
			return {
				isPwdLogin: isH5,
				isH5,
				formData: {
					username: '',
					password: ''
				}
			}
		},
		onLoad(options) {
			loginCallbackUrl = options?.loginCallbackUrl;
			navBack = options?.navBack;
		},
		methods: {
			getPhoneNumber(e) {
				if (e.detail.errMsg == "getPhoneNumber:ok") {
					const {
						code
					} = e.detail;
					this.postLogin("/front/edu/auth/wxMaMobileLogin", {
						code
					});
				} else {
					uni.showModal({
						title: "授权失败",
						content: "请授权获取手机号登录",
						showCancel: false,
					});

				}
			},
			submit() {
				const {
					username,
					password
				} = this.formData;
				if (!username) {
					uni.showToast({
						title: '账号不能为空',
						icon: 'none'
					})
					return;
				}

				if (!password) {
					uni.showToast({
						title: '密码不能为空',
						icon: 'none'
					})
					return;
				}


				this.postLogin("/front/edu/auth/pwdLogin", {
					username,
					password
				});

			},
			postLogin(url, data) {
				const _this = this;

				function post(url, data) {
					_this.$reqPost(url, data, true,'登录中...').then((res) => {
						const {
							data,
							success,
							errorMessage
						} = res || {};


						if (success) {

							store.commit('login/login', data);
							// loginStore.login(data);
							uni.showToast({
								title: errorMessage || "登录成功",
								icon: "success"
							});



							setTimeout(() => {
								if (!!navBack) {
									uni.navigateBack();
									return;
								}

								const url = loginCallbackUrl ?
									decodeURIComponent(loginCallbackUrl) :
									"/pages/index/index";

								uni.reLaunch({
									url,
								});
							}, 800);
						} else {
							uni.showToast({
								title: errorMessage || "登录失败",
								icon: "none"
							});

						}
					});
				}

				//#ifndef H5
				uni.login({
					success: (res) => {
						const wxMaSessionCode = res.code;
						post(url, {
							...data,
							wxMaSessionCode
						});
					},
					fail: (err) => {
						console.log(err);
						uni.hideToast();
					},
				});
				//#endif

				//#ifdef H5
				post(url, data);
				//#endif
			}


		}
	}
</script>

<style>
	page {
		background-color: #f9fafb;
	}
</style>