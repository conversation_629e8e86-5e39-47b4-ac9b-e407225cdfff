<template>
	<view class="min-h-screen bg-gray-50">
		<!-- 顶部导航区 -->
		<view class="bg-gradient-to-r from-primary-600 to-primary-700" :style="{ paddingTop: statusBarHeight + 'px' }">
			<!-- 波浪形状底部 -->
			<!--     <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" class="absolute bottom-0 left-0 right-0 w-full">
        <path fill="#f9fafb" fill-opacity="1" d="M0,224L60,213.3C120,203,240,181,360,186.7C480,192,600,224,720,218.7C840,213,960,171,1080,165.3C1200,160,1320,192,1380,208L1440,224L1440,320L1380,320C1320,320,1200,320,1080,320C960,320,840,320,720,320C600,320,480,320,360,320C240,320,120,320,60,320L0,320Z"></path>
      </svg> -->

			<!-- 顶部内容 -->
			<view class="px-5 py-4">
				<view class="flex items-center justify-between mb-6">
					<view class="flex items-center">
						<text class="text-white text-xl font-bold">导刷题</text>
						<view class="ml-2 px-2 py-0.5 bg-white bg-opacity-20 rounded-full">
							<text class="text-white text-xs">Pro</text>
						</view>
					</view>

					<view class="flex items-center space-x-3">
						<button @click="goToNotifications"
							class="w-10 h-10 bg-white bg-opacity-10 rounded-full flex items-center justify-center">
							<text class="fas fa-bell text-white"></text>
						</button>
						<view class="w-10 h-10 rounded-full bg-white flex items-center justify-center">
							<text class="fas fa-user text-primary-500"></text>
						</view>
					</view>
				</view>

				<!-- 欢迎文字 -->
				<view class="mb-4">
					<text class="text-white text-opacity-90 text-sm">欢迎回来</text>
					<text class="text-white text-xl font-bold block">今天想刷什么题？</text>
				</view>

				<!-- 搜索框 -->
				<view class="relative mb-12">
					<view class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
						<text class="fas fa-search text-gray-400"></text>
					</view>
					<input v-model="searchKeyword" placeholder="搜索题库、试题或关键词"
						class="h-auto pl-12 pr-4 py-4 border border-gray-300 bg-white rounded-xl shadow-md text-sm"
						@confirm="handleSearch" />
				</view>
			</view>
		</view>

		<!-- 主内容区域 -->
		<view class="px-5 -mt-6">
			<!-- 快速操作区域 -->
			<view class="grid grid-cols-4 gap-4 mb-6">
				<view v-for="action in quickActions" :key="action.id">
					<view @click="handleQuickAction(action)" class="flex flex-col items-center">
						<view class="w-16 h-16 rounded-2xl flex items-center justify-center mb-2 shadow-sm"
							:class="action.bgClass">
							<text :class="action.icon + ' text-white text-xl'"></text>
						</view>
						<text class="text-xs font-medium text-gray-700 text-center">{{ action.name }}</text>
					</view>
				</view>
			</view>

			<!-- 统计卡片 -->
			<view class="bg-white rounded-2xl p-5 shadow-sm mb-6">
				<view class="flex items-center justify-between mb-4">
					<text class="text-lg font-bold text-gray-800">学习统计</text>
					<text class="text-primary-500 text-sm">本周</text>
				</view>

				<view class="flex justify-between">
					<view class="text-center">
						<text class="text-2xl font-bold text-gray-800 block">24</text>
						<text class="text-xs text-gray-500">已学习题目</text>
					</view>
					<view class="text-center">
						<text class="text-2xl font-bold text-green-500 block">85%</text>
						<text class="text-xs text-gray-500">正确率</text>
					</view>
					<view class="text-center">
						<text class="text-2xl font-bold text-primary-500 block">3</text>
						<text class="text-xs text-gray-500">连续学习</text>
					</view>
				</view>
			</view>

			<!-- 我的题库 -->
			<view class="mb-6">
				<view class="flex items-center justify-between mb-4">
					<view class="flex items-center">
						<text class="fas fa-book-open text-primary-500 mr-2"></text>
						<text class="text-lg font-bold text-gray-800">我的题库</text>
					</view>
					<!-- <button @click="goToQuestionBankList" class="flex items-center text-primary-500 text-sm">
            全部
            <text class="fas fa-chevron-right ml-1 text-xs"></text>
          </button> -->
				</view>

				<view v-if="myQuestionBanks.length === 0" class="bg-white rounded-2xl p-8 shadow-sm text-center">
					<view class="w-20 h-20 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center">
						<text class="fas fa-book-open text-gray-300 text-3xl"></text>
					</view>
					<text class="text-gray-800 font-medium mb-2 block">暂无题库</text>
					<text class="text-gray-500 text-sm mb-4 block">创建或加入题库开始学习</text>
					<button @click="showCreateQuestionBankModal"
						class="px-6 py-3 bg-primary-500 text-white rounded-full shadow-sm inline-flex items-center">
						<text class="fas fa-plus mr-2"></text>
						创建题库
					</button>
				</view>

				<view v-else>
					<view v-for="item in myQuestionBanks" :key="item.id"
						class="bg-white rounded-2xl shadow-sm mb-3 overflow-hidden">
						<!-- 题库头部 -->
						<view class="p-4 border-b border-gray-100">
							<view class="flex items-center justify-between">
								<view class="flex items-center">
									<view class="w-10 h-10 rounded-xl mr-3 flex items-center justify-center"
										:class="item.pinned ? 'bg-primary-500' : 'bg-primary-100'">
										<text
											:class="'fas fa-book ' + (item.pinned ? 'text-white' : 'text-primary-500')"></text>
									</view>
									<view>
										<view class="flex items-center">
											<text class="text-base font-medium text-gray-800">{{ item.name }}</text>
											<text v-if="item.pinned"
												class="fas fa-thumbtack text-yellow-500 text-xs ml-2"></text>
										</view>
										<text class="text-xs text-gray-500">{{ item.description || '' }}</text>
									</view>
								</view>
								<view>
									<button @click="showQuestionBankActions(item)"
										class="w-8 h-8 rounded-full hover:bg-gray-100 flex items-center justify-center">
										<text class="fas fa-ellipsis-v text-gray-400"></text>
									</button>
								</view>
							</view>
						</view>

						<!-- 题库数据 -->
						<view class="px-4 py-3 bg-gray-50" @click="goToQuestionBankDetail(item)">
							<view class="flex justify-between">
								<view class="flex items-center">
									<text class="fas fa-file-alt text-gray-400 mr-1 text-xs"></text>
									<text class="text-xs text-gray-500">{{ item.questionCount }}题</text>
								</view>
								<!-- <view class="flex items-center">
                  <text class="fas fa-users text-gray-400 mr-1 text-xs"></text>
                  <text class="text-xs text-gray-500">{{ item.memberCount }}人</text>
                </view> -->
								<view v-if="item.isOwner" class="flex items-center">
									<text class="fas fa-crown text-primary-500 mr-1 text-xs"></text>
									<text class="text-xs text-primary-500">创建者</text>
								</view>
								<!-- <view class="flex items-center">
                  <text class="fas fa-chevron-right text-gray-300 text-xs"></text>
                </view> -->
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 推荐题库 -->
			<!-- <view class="mb-8">
        <view class="flex items-center justify-between mb-4">
          <text class="text-lg font-bold text-gray-800">推荐题库</text>
          <text class="text-primary-500 text-sm">更多</text>
        </view>
        
        <view class="flex space-x-4 overflow-x-auto pb-4 -mx-5 px-5">
          <view v-for="i in 3" :key="i" class="bg-white rounded-2xl shadow-sm w-48 flex-shrink-0">
            <view class="h-24 bg-primary-500 rounded-t-2xl flex items-center justify-center">
              <text class="fas fa-graduation-cap text-white text-3xl"></text>
            </view>
            <view class="p-3">
              <text class="font-medium text-gray-800 block">{{ ['高等数学', '大学英语', '计算机网络'][i-1] }}</text>
              <view class="flex items-center justify-between mt-2">
                <text class="text-xs text-gray-500">{{ [245, 189, 210][i-1] }}题</text>
                <button class="px-2 py-1 bg-primary-100 rounded-full">
                  <text class="text-xs text-primary-500">加入</text>
                </button>
              </view>
            </view>
          </view>
        </view>
      </view> -->
		</view>

		<!-- 创建题库弹窗 -->
		<view v-if="showCreateModal" class="fixed inset-0 z-50 flex items-center justify-center"
			@click="closeCreateQuestionBankModal">
			<view class="absolute inset-0 bg-black bg-opacity-50"></view>
			<view class="bg-white rounded-3xl p-6 mx-6 relative z-10 w-full max-w-sm shadow-lg" @click.stop>
				<view class="flex items-center justify-between mb-6">
					<view class="flex items-center">
						<view class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center mr-3">
							<text class="fas fa-plus text-primary-500"></text>
						</view>
						<text class="text-xl font-bold text-gray-800">创建题库</text>
					</view>
					<view>
						<button @click="closeCreateQuestionBankModal"
							class="w-8 h-8 rounded-full hover:bg-gray-100 flex items-center justify-center">
							<text class="fas fa-times text-gray-400"></text>
						</button>
					</view>
				</view>

				<view class="space-y-4">
					<view>
						<text class="text-sm font-medium text-gray-700 block mb-2">题库名称 <text
								class="text-red-500">*</text></text>
						<input v-model="newQuestionBank.name" placeholder="请输入题库名称" maxlength="30"
							class="h-auto p-3 border border-solid border-gray-300 rounded-xl text-sm bg-white focus:border-primary-500 hover:border-primary-500" />
					</view>
					<view>
						<text class="text-sm font-medium text-gray-700 block mb-2">题库描述</text>

						<textarea v-model="newQuestionBank.description" placeholder="请输入题库描述"
							class="w-full p-3 border border-solid border-gray-300 rounded-xl text-sm bg-white   focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
							:maxlength="200"></textarea>

					</view>
				</view>

				<button @click="createQuestionBank"
					class="w-full mt-6 py-3.5 bg-primary-500 text-white rounded-xl text-sm font-medium">
					<text class="fas fa-plus mr-2"></text>
					创建题库
				</button>
			</view>
		</view>

		<!-- 题库操作弹窗 -->
		<view v-if="showActionsModal" class="fixed inset-0 z-50 flex items-end" @click="closeQuestionBankActionsModal">
			<view class="absolute inset-0 bg-black bg-opacity-50"></view>
			<view class="bg-white rounded-t-3xl p-6 w-full relative z-10 shadow-lg" @click.stop>
				<view class="w-12 h-1 bg-gray-200 rounded-full mx-auto mb-6"></view>

				<view class="flex items-center mb-6">
					<view class="w-12 h-12 rounded-xl mr-4 flex items-center justify-center bg-primary-100">
						<text class="fas fa-book text-primary-500 text-lg"></text>
					</view>
					<view>
						<text class="text-lg font-medium text-gray-800 block">{{ selectedQuestionBank?.name }}</text>
						<text class="text-xs text-gray-500">{{ selectedQuestionBank?.description || '' }}</text>
					</view>
				</view>

				<view class="grid grid-cols-4 gap-6 mb-6">
					<button v-for="(action, index) in questionBankActions.slice(0, 4)" :key="action.id"
						@click="handleQuestionBankAction(action)" class="flex flex-col items-center">
						<view class="w-14 h-14 rounded-full flex items-center justify-center mb-2"
							:class="index === 0 ? 'bg-yellow-100' : 'bg-gray-100'">
							<text :class="(index === 0 ? '' : 'text-gray-600')+' '+action.icon"></text>
						</view>
						<text class="text-xs text-gray-700 text-center">{{ action.name }}</text>
					</button>
				</view>

				<view class="space-y-2 mb-4">
					<button v-for="action in questionBankActions.slice(4)" :key="action.id"
						@click="handleQuestionBankAction(action)"
						class="w-full flex items-center hover:bg-gray-50 rounded-xl">
						<text :class="action.icon + ' text-gray-600 mr-3'"></text>
						<text class="text-gray-800">{{ action.name }}</text>
					</button>
				</view>

				<button @click="closeQuestionBankActionsModal"
					class="w-full py-2 bg-gray-100 text-gray-600 rounded-xl font-medium">
					取消
				</button>
			</view>
		</view>

		<!--修改题库信息弹窗-->
		<view v-if="showEditModal" class="fixed inset-0 z-50 flex items-center justify-center"
			@click="closeEditQuestionBankModal">
			<view class="absolute inset-0 bg-black bg-opacity-50"></view>
			<view class="bg-white rounded-3xl p-6 mx-6 relative z-10 w-full max-w-sm shadow-lg" @click.stop>
				<view class="flex items-center justify-between mb-6">
					<view class="flex items-center">
						<view class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center mr-3">
							<text class="fas fa-edit text-primary-500"></text>
						</view>
						<text class="text-xl font-bold text-gray-800">修改题库信息</text>
					</view>
					<view>
						<button @click="closeEditQuestionBankModal"
							class="w-8 h-8 rounded-full hover:bg-gray-100 flex items-center justify-center">
							<text class="fas fa-times text-gray-400"></text>
						</button>
					</view>
				</view>

				<view class="space-y-4">
					<view>
						<text class="text-sm font-medium text-gray-700 block mb-2">题库名称 <text
								class="text-red-500">*</text></text>
						<input v-model="editQuestionBank.name" placeholder="请输入题库名称" maxlength="30"
							class="h-auto p-3 border border-solid border-gray-300 rounded-xl text-sm bg-white focus:border-primary-500 hover:border-primary-500" />
					</view>
					<view>
						<text class="text-sm font-medium text-gray-700 block mb-2">题库描述</text>

						<textarea v-model="editQuestionBank.description" placeholder="请输入题库描述"
							class="w-full p-3 border border-solid border-gray-300 rounded-xl text-sm bg-white   focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
							:maxlength="200"></textarea>

					</view>
				</view>

				<button @click="updateQuestionBank"
					class="w-full mt-6 py-3.5 bg-primary-500 text-white rounded-xl text-sm font-medium">
					<text class="fas fa-save mr-2"></text>
					保存修改
				</button>
			</view>
		</view>


	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				searchKeyword: '',
				showCreateModal: false,
				showActionsModal: false,
				showEditModal: false,
				quickActions: [{
						id: 'upload-qbank',
						name: '创建题库',
						icon: 'fas fa-plus',
						bgClass: 'bg-primary-500'
					},
					{
						id: 'exam-center',
						name: '考试中心',
						icon: 'fas fa-clipboard-check',
						bgClass: 'bg-blue-500'
					},
					{
						id: 'my-groups',
						name: '我的群组',
						icon: 'fas fa-users',
						bgClass: 'bg-purple-500'
					},
					{
						id: 'favorites',
						name: '收藏题目',
						icon: 'fas fa-bookmark',
						bgClass: 'bg-yellow-500'
					}
				],
				myQuestionBanks: [

				],
				newQuestionBank: {
					name: '',
					description: '',

				},
				editQuestionBank: {
					name: '',
					description: '',
					id: undefined
				},
				selectedQuestionBank: null,
				questionBankActions: []
			}
		},
		onLoad() {
			this.getSystemInfo();
			this.loadMyQuestionBanks();
		},
		methods: {
			getSystemInfo() {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
			},

			loadMyQuestionBanks() {
				// 模拟加载我的题库数据
				// 实际项目中这里应该调用API
				this.$reqGet('/front/edu-personal/qbank/myList').then(res => {

					if (res.success) {
						this.myQuestionBanks = res.data
					}
				})
			},

			handleSearch() {
				if (!this.searchKeyword.trim()) {
					uni.showToast({
						title: '请输入搜索关键词',
						icon: 'none'
					});
					return;
				}

				uni.navigateTo({
					url: `/pages/questionbank/list?search=${encodeURIComponent(this.searchKeyword)}`
				});
			},

			handleQuickAction(action) {
				switch (action.id) {
					case 'upload-qbank':
						this.showCreateQuestionBankModal();
						break;
					case 'exam-center':
						uni.showToast({
							title: '考试功能暂未开放',
							icon: 'none'
						});
						break;
					case 'my-groups':
						uni.showToast({
							title: '群组功能暂未开放',
							icon: 'none'
						});
						break;
					case 'favorites':
						uni.showToast({
							title: '收藏功能暂未开放',
							icon: 'none'
						});
						break;
				}
			},

			showCreateQuestionBankModal() {
				this.newQuestionBank = {
					name: '',
					description: ''
				};
				this.showCreateModal = true;
			},

			closeCreateQuestionBankModal() {
				this.showCreateModal = false;
			},

			createQuestionBank() {
				console.log(this.newQuestionBank)
				if (!this.newQuestionBank.name.trim()) {
					uni.showToast({
						title: '请输入题库名称',
						icon: 'none'
					});


					return;
				}



				this.$reqPost('/front/edu-personal/qbank/create', this.newQuestionBank).then(res => {

					if (res.success) {
						uni.showToast({
							title: '创建成功',
							icon: 'success'
						});
						this.closeCreateQuestionBankModal();
						this.loadMyQuestionBanks();
					}
				})


			},

			goToQuestionBankDetail(item) {
				uni.navigateTo({
					url: `/pages/questionbank/detail?bankId=${item.id}`
				});
			},

			goToQuestionBankList() {
				uni.switchTab({
					url: '/pages/questionbank/list'
				});
			},

			showQuestionBankActions(item) {
				this.selectedQuestionBank = item;

				// 根据是否为题库拥有者显示不同的操作选项
				this.questionBankActions = [{
					id: 'toggle-top',
					name: item.pinned ? '取消置顶' : '置顶',
					icon: 'fas fa-thumbtack text-yellow-500'
				}];

				if (item.isOwner) {
					this.questionBankActions.push({
						id: 'edit-info',
						name: '修改信息',
						icon: 'fas fa-edit'
					}, {
						id: 'permission-setting',
						name: '权限设置',
						icon: 'fas fa-shield-alt'
					}, {
						id: 'chapter-manage',
						name: '章节管理',
						icon: 'fas fa-list'
					}, {
						id: 'add-question',
						name: '添加题目',
						icon: 'fas fa-plus'
					}, {
						id: 'edit-question',
						name: '编辑题目',
						icon: 'fas fa-edit'
					});
				}

				this.questionBankActions.push({
					id: 'exit-qbank',
					name: '退出题库',
					icon: 'fas fa-sign-out-alt'
				});

				this.showActionsModal = true;
			},

			closeQuestionBankActionsModal() {
				this.showActionsModal = false;
			},

			handleQuestionBankAction(action) {
				const item = this.selectedQuestionBank;

				switch (action.id) {
					case 'toggle-top':
						this.toggleQuestionBankTop(item);
						break;
					case 'edit-info':
						this.editQuestionBankInfo(item);
						break;
					case 'permission-setting':
						uni.navigateTo({
							url: `/pages/questionbank/permission?bankId=${item.id}`
						});
						break;
					case 'chapter-manage':
						uni.navigateTo({
							url: `/pages/questionbank/chapter?bankId=${item.id}`
						});
						break;
					case 'add-question':
						uni.navigateTo({
							url: `/pages/question/imp?bankId=${item.id}`
						});
						break;
					case 'edit-question':
						uni.navigateTo({
							url: `/pages/question/list?bankId=${item.id}`
						});
						break;
					case 'exit-qbank':
						this.exitQuestionBank(item);
						break;
				}

				this.closeQuestionBankActionsModal();
			},

			toggleQuestionBankTop(item) {
				this.$reqGet('/front/edu-personal/qbank/pin/' + item.id, {
					isRemove: item.pinned
				}).then(res => {

					if (res.success) {
						uni.showToast({
							title: !item.pinned ? '已置顶' : '已取消置顶',
							icon: 'success'
						});
						this.loadMyQuestionBanks();
					} else {
						uni.showToast({
							title: res.errorMessage || '系统繁忙',
							icon: 'none'
						});
					}
				})

			},

			editQuestionBankInfo(item) {
				this.editQuestionBank = {
					name: item.name,
					description: item.description,
					id: item.id
				};
				this.showEditModal = true;

			},

			exitQuestionBank(item) {
				uni.showModal({
					title: '确认退出',
					content: `确定要退出题库"${item.name}"吗？`,
					success: (res) => {
						if (res.confirm) {
							const index = this.myQuestionBanks.findIndex(bank => bank.id === item.id);
							if (index !== -1) {
								this.myQuestionBanks.splice(index, 1);
								uni.showToast({
									title: '已退出题库',
									icon: 'success'
								});
							}
						}
					}
				});
			},
			closeEditQuestionBankModal() {
				this.showEditModal = false;
			},
			updateQuestionBank() {
				this.$reqPost('/front/edu-personal/qbank/update', this.editQuestionBank, true).then(res => {
					if (res.success) {
						uni.showToast({
							title: '修改成功',
							icon: 'success'
						});
						this.closeEditQuestionBankModal();
						this.loadMyQuestionBanks();
					} else {
						uni.showToast({
							title: res.errorMessage || '系统繁忙',
							icon: 'none'
						});
					}
				})
			},

			goToNotifications() {
				uni.showToast({
					title: '通知功能开发中',
					icon: 'none'
				});
			}
		}
	}
</script>

<style scoped>
</style>