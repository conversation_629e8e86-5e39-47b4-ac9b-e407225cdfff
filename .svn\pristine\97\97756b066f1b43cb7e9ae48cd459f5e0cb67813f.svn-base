<template>
  <view class="h-screen bg-gray-50 flex flex-col">
    <!-- 搜索和筛选布局 -->
    <view class="bg-gray-50 shadow-sm sticky top-0 z-20">
      <!-- 优雅椭圆形搜索框 -->
      <view class="px-4 py-3.5 bg-gray-50 border-b border-gray-100">
        <view class="relative flex items-center">
          <!-- 搜索图标（左侧） -->
          <text class="fas fa-search text-gray-500 text-sm absolute left-4 top-1/2 transform -translate-y-1/2 z-10"></text>
          
          <!-- 搜索输入框 -->
          <input 
            v-model="searchKeyword"
            placeholder="搜索题目内容..."
            class="h-10 w-full pl-10 pr-14 bg-white border border-gray-200 rounded-full text-sm shadow-sm"
            @confirm="doSearch"
          />
          
          <!-- 清除按钮 -->
          <view v-if="searchKeyword" class="absolute right-12 top-1/2 -translate-y-1/2 z-30 flex items-center justify-center">
            <view @click.stop="clearSearchKeyword" class="h-6 w-6 rounded-full bg-gray-200 flex items-center justify-center cursor-pointer">
              <text class="fas fa-times text-gray-500 text-xs"></text>
            </view>
          </view>
          
          <!-- 搜索按钮 -->
          <view @click="doSearch" class="absolute right-2 transform -translate-y-0">
            <view class="h-7 w-7 rounded-full bg-gray-600 shadow-sm flex items-center justify-center hover:bg-gray-700 transition-colors">
              <text class="fas fa-arrow-right text-white text-xs"></text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 筛选条件 - 另一行 -->
      <view class="flex items-center space-x-2 px-4 py-3 bg-white border-b border-gray-100">
        <!-- 题型选择(picker) -->
        <view class="flex-1 relative">
          <picker 
            @change="onTypeChange" 
            :value="typePickerIndex" 
            :range="typePickerRange"
          >
            <view class="flex items-center justify-between h-8 px-3 bg-gray-50 border border-gray-200 rounded-full">
              <text class="text-xs truncate" :class="selectedTypeId ? 'text-gray-800' : 'text-gray-400'">
                {{ selectedTypeName || '题型' }}
              </text>
              <view class="flex items-center">
                <text 
                  v-if="selectedTypeId" 
                  @click.stop="clearTypeFilter" 
                  class="fas fa-times-circle text-gray-400 text-xs mr-1 hover:text-primary-400"
                ></text>
                <text class="fas fa-chevron-down text-gray-400 text-xs"></text>
              </view>
            </view>
          </picker>
        </view>
        
        <!-- 章节选择(弹窗) -->
        <view class="flex-1 relative">
          <view 
            @click="showChapterSelector"
            class="flex items-center justify-between h-8 px-3 bg-gray-50 border border-gray-200 rounded-full"
          >
            <text class="text-xs truncate" :class="selectedChapterId ? 'text-gray-800' : 'text-gray-400'">
              {{ selectedChapterName || '章节' }}
            </text>
            <view class="flex items-center">
              <text 
                v-if="selectedChapterId" 
                @click.stop="clearChapterFilter" 
                class="fas fa-times-circle text-gray-400 text-xs mr-1 hover:text-primary-400"
              ></text>
              <text class="fas fa-chevron-down text-gray-400 text-xs"></text>
            </view>
          </view>
        </view>
        
        <!-- 难度选择(picker) -->
        <view class="flex-1 relative">
          <picker 
            @change="onDifficultyChange" 
            :value="difficultyPickerIndex" 
            :range="difficultyPickerRange"
          >
            <view class="flex items-center justify-between h-8 px-3 bg-gray-50 border border-gray-200 rounded-full">
              <text class="text-xs truncate" :class="selectedDifficultyId ? 'text-gray-800' : 'text-gray-400'">
                {{ selectedDifficultyName || '难度' }}
              </text>
              <view class="flex items-center">
                <text 
                  v-if="selectedDifficultyId" 
                  @click.stop="clearDifficultyFilter" 
                  class="fas fa-times-circle text-gray-400 text-xs mr-1 hover:text-primary-400"
                ></text>
                <text class="fas fa-chevron-down text-gray-400 text-xs"></text>
              </view>
            </view>
          </picker>
        </view>
      </view>
    </view>

    <!-- 题目列表 -->
    <view class="flex-1 flex flex-col overflow-hidden">
      <!-- 添加题目按钮和操作栏 -->
      <view class="flex items-center justify-between px-4 py-3 mb-1" v-show="total>=0">
        <view class="flex items-center">
          <text class="text-sm text-gray-600 mr-2">共 {{ total }} 道题目</text>
          <text v-if="selectedQuestions.length > 0" class="text-xs text-primary-500 font-medium">
            已选择 {{ selectedQuestions.length }} 项
          </text>
        </view>
        <view class="flex items-center space-x-2">
          <view>
            <button 
            v-if="selectedQuestions.length > 0"
            @click="deleteSelectedQuestions" 
            class="py-1.5 px-3 bg-red-50 text-red-500 rounded-full text-xs font-medium flex items-center hover:bg-red-100 transition-colors"
          >
            <text class="fas fa-trash mr-1.5"></text>删除
          </button>
          </view>
          <view>
            <button @click="goToAddQuestion" class="py-1.5 px-3 bg-primary-50 text-primary-500 rounded-full text-xs font-medium flex items-center hover:bg-primary-100 transition-colors">
            <text class="fas fa-plus mr-1.5"></text>添加题目
          </button>
          </view>
        </view>
      </view>

      <scroll-pagination
        class="flex-1  overflow-hidden"
        :page-size="12"
        :auto-load="true"
        ref="scrollPagination"
        :enable-refresh="true"
        @load="loadData"
      >
        <template v-slot="{list}">
          <!-- 题目列表 -->
          <view class="px-3 space-y-3">
            <view 
              v-for="(question, index) in list" 
              :key="question.id"
              class="bg-white rounded-lg p-3 border border-gray-200 shadow-sm"
            >
              <!-- 题目头部信息 -->
              <view class="flex items-center justify-between mb-1.5">
                <view class="flex items-center space-x-1.5">
                  <!-- 折叠按钮最左 -->
                  <view 
                    @click="toggleExpandQuestion(question.id)" 
                    class="w-7 h-7 bg-gray-50 rounded-full flex items-center justify-center transition-all duration-300 hover:bg-primary-50 mr-1"
                    :class="{'transform rotate-180 bg-primary-50': expandedQuestions.includes(question.id)}"
                  >
                    <text class="fas fa-chevron-down text-gray-500 text-xs" :class="{'text-primary-500': expandedQuestions.includes(question.id)}"></text>
                  </view>
                  <!-- 勾选框 - 使用主题色 -->
                  <view 
                    class="w-5 h-5 rounded border flex items-center justify-center mr-1 cursor-pointer transition-colors"
                    :class="isSelected(question.id) ? 'bg-primary-500 border-primary-500' : 'border-gray-300 hover:border-primary-300'"
                    @click.stop="toggleSelection(question.id)"
                  >
                    <text v-if="isSelected(question.id)" class="fas fa-check text-white text-xs"></text>
                  </view>
                  <text class="text-xs font-medium text-gray-500">#{{ index + 1 }}</text>
                  <view :class="[
                    'px-1.5 py-0.5 rounded-full text-xs',
                    getQuestionTypeStyle(question.questionType)
                  ]">
                    <text>{{ getQuestionTypeName(question.questionType) }}</text>
                  </view>
                  <view v-if="question.difficulty" :class="[
                    'px-1.5 py-0.5 rounded-full text-xs',
                    getDifficultyStyle(question.difficulty)
                  ]">
                    <text>{{ getDifficultyName(question.difficulty) }}</text>
                  </view>
                </view>
                <view class="flex items-center space-x-2">
                  <!-- 编辑按钮 -->
                  <button @click.stop="editQuestion(question,index)" class="w-7 h-7 bg-gray-50 rounded-full flex items-center justify-center hover:bg-primary-50 transition-colors">
                    <text class="fas fa-edit text-gray-500 text-xs hover:text-primary-500"></text>
                  </button>
                </view>
              </view>
              
              <!-- 题目内容 -->
              <view class="mb-2">
                <text class="text-sm text-gray-800" :class="{ 'line-clamp-2': !expandedQuestions.includes(question.id) }">{{ question.questionContent }}</text>
              </view>
              
              <!-- 展开的内容区域 -->
              <view v-if="expandedQuestions.includes(question.id)">
                <!-- 选项列表 -->
                <view v-if="question.options && question.options.length > 0" class="mb-2 bg-gray-50 p-3 rounded-lg">
                  <view 
                    v-for="(option, optIndex) in question.options" 
                    :key="optIndex"
                    class="flex items-start mb-2 last:mb-0"
                  >
                    <view class="flex-shrink-0 w-5 h-5 rounded-full bg-gray-100 flex items-center justify-center mr-2">
                      <text class="text-xs text-primary-500 font-bold"  >{{ String.fromCharCode(65 + optIndex) }}</text>
                    </view>
                    <text class="text-xs text-gray-700 flex-1 pt-0.5">{{ option }}</text>
                    <text v-if="question.rightAnswer?.indexOf(optIndex+'')>-1"  class="fas fa-check text-primary-500 text-xs ml-1.5 flex-shrink-0 mt-1"></text>
                  </view>
                </view>
                
               		<!-- 答案 -->
						<view v-if="question.rightAnswer" class="mb-2">
							<text class="text-xs font-medium text-gray-700">答案：</text>
							<text class="text-xs text-primary-600">{{ formatAnswer(question) }}</text>
						</view>

						<!-- 解析 -->
						<view v-if="question.analysis" class="mb-2">
							<text class="text-xs font-medium text-gray-700">解析：</text>
							<text class="text-xs text-gray-600">{{ question.analysis }}</text>
						</view>

						<!-- 章节信息 -->
						<view v-if="question.chapterFullName" class="flex items-center mt-2 text-xs text-gray-500">
							<text class="fas fa-bookmark text-primary-400 mr-1.5"></text>
							<text>章节：{{ question.chapterFullName }}</text>
						</view>
                
                <!-- 创建时间信息 -->
                <view v-if="question.createTime" class="flex items-center mt-1.5 text-xs text-gray-500">
                  <text class="fas fa-clock text-primary-400 mr-1.5"></text>
                  <text>创建: {{ question.createTime }}</text>
                </view>
              </view>
            </view>
          </view>
        </template>
        
        <template #empty>
          <view class="py-12 px-4 bg-white rounded-xl shadow-sm flex flex-col items-center justify-center">
            <view class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <text class="fas fa-question-circle text-gray-300 text-2xl"></text>
            </view>
            <text class="text-gray-500 text-sm">{{ searchKeyword||selectedTypeId||selectedDifficultyId||selectedChapterId ? '未找到相关题目' : '暂无题目' }}</text>
            <text v-if="!searchKeyword&&!selectedTypeId&&!selectedDifficultyId&&!selectedChapterId" class="text-gray-400 text-xs block mt-1">点击上方按钮添加第一道题目</text>
          </view>
        </template>
      </scroll-pagination>

    </view>

    <!-- 底部安全区域 -->
    <view class="h-8  "></view>
    
    <!-- 弹窗不变，保留原有代码 -->
    <view v-if="showChapterPopup" class="fixed inset-0 z-50">
      <!-- 遮罩层 -->
      <view @click="closeChapterSelector" class="absolute inset-0 bg-black bg-opacity-40"></view>
      <!-- 内容区 -->
      <view class="absolute bottom-0 left-0 right-0 bg-white rounded-t-xl overflow-hidden transform transition-transform duration-300 ease-out">
        <view class="px-6 py-4 border-b border-gray-100">
          <view class="flex items-center justify-between">
            <text class="text-lg font-bold text-gray-800">选择章节</text>
            <view>
              <button @click="closeChapterSelector" class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
              <text class="fas fa-times text-gray-600 text-sm"></text>
            </button></view>
          </view>
        </view>
        
        <view class="px-6 py-3 max-h-[60vh] overflow-y-auto">
          <view class="space-y-1">
          
            
         
            
            <!-- 遍历一级章节 -->
            <view v-for="chapter in chapters" :key="chapter.value" class="mb-3">
              <!-- 一级章节 -->
              <button 
                @click="selectChapter(chapter,chapter.label)"
                class="w-full text-sm py-3 px-4 flex items-center justify-between rounded-lg hover:bg-gray-50"
              >
                <view class="flex items-center">
                  <text class="text-gray-800 font-medium">{{ chapter.label }}</text>
                </view>
                <text v-if="selectedChapterId === chapter.value" class="fas fa-check text-primary-500"></text>
              </button>
              
              <!-- 二级章节 -->
              <view v-if="chapter.children && chapter.children.length > 0" class="pl-4 mt-1">
                <button 
                  v-for="subChapter in chapter.children" 
                  :key="subChapter.value"
                  @click="selectChapter(subChapter,chapter.label+'/'+subChapter.label)"
                  class="w-full text-sm py-2.5 px-4 flex items-center justify-between rounded-lg hover:bg-gray-50"
                >
                  <view class="flex items-center">
                    <text class="fas fa-angle-right text-gray-400 mr-2 text-xs"></text>
                    <text class="text-gray-700">{{ subChapter.label }}</text>
                  </view>
                  <text v-if="selectedChapterId === subChapter.value" class="fas fa-check text-primary-500"></text>
                </button>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
     
  </view>
</template>

<script>
 

export default {
 
  
  data() {
    return {
      bankId: '',
     
      searchKeyword: '',
      
      // 弹出选择相关
      selectedTypeId: '', // 当前选中的题型ID
      selectedChapterId: '', // 当前选中的章节ID
      selectedDifficultyId: '', // 当前选中的难度ID
      
      // picker相关
      typePickerIndex: 0,
      difficultyPickerIndex: 0,
      
      // 题型数据
      questionTypes: [
        { id: 1, name: '单选题' },
        { id: 2, name: '多选题' },
        { id: 3, name: '判断题' },
      ],
      
      // 难度数据
      difficulties: [
        { id: 1, name: '简单' },
        { id: 2, name: '中等' },
        { id: 3, name: '困难' }
      ],
      
      // 章节数据
      chapters: [
        
      ],
      total:-1,
      showChapterPopup: false,
      expandedQuestions: [], // 保存展开的题目ID
      selectedQuestions: [], // 存储已选择的题目ID
      selectedChapterName:'',
      editIndex:-1
    }
  },
  
  computed: {
    selectedTypeName() {
      const type = this.questionTypes.find(t => t.id === this.selectedTypeId);
      return type ? type.name : '';
    },
 
    selectedDifficultyName() {
      const difficulty = this.difficulties.find(d => d.id === this.selectedDifficultyId);
      return difficulty ? difficulty.name : '';
    },
    
    // picker数据源
    typePickerRange() {
      return this.questionTypes.map(item => item.name);
    },
    difficultyPickerRange() {
      return this.difficulties.map(item => item.name);
    },
 
  },
  
  onLoad(options) {
    if (options.bankId) {
      this.bankId = options.bankId;
    }  
    this.loadChapters();
  },
  onShow() {
    if(this.editIndex>=0){
      this.$refs.scrollPagination.refreshPageByIndex(this.editIndex);
      this.editIndex = -1;
    }
  },
  
  methods: {
   
    
    loadChapters() {
      // 模拟加载章节数据
      // 实际项目中这里应该调用API获取章节列表
      this.$reqGet('/front/edu-personal/chapter/enumList/'+this.bankId).then(res => {
        if (res.success) {
          this.chapters = res.data;
        }
      });
    },
    loadData(params,callback) {
      let chapterId = this.selectedChapterId || '';
      // 处理特殊的"不设置章节"选项
      if (chapterId === '-1') {
        chapterId = 'null'; // 后端约定null表示未设置章节
      }
      
      return this.$reqGet('/front/edu-personal/question/page', {...params, bankId: this.bankId,
        searcher:{
          EQ_questionType: this.selectedTypeId || '',
          EQ_difficulty: this.selectedDifficultyId || '',
          EQ_chapterId: chapterId,
          LIKE_questionContent: this.searchKeyword || ''
        }
      }).then(res => {
        callback(res.data);
        this.total = res.data.total;
      });
    },
    
    // 执行搜索
    doSearch() {
      this.$refs.scrollPagination.init();
    },
    
    // 题型picker事件处理
    onTypeChange(e) {
      const index = e.detail.value;
      this.selectedTypeId = this.questionTypes[index].id;
      this.typePickerIndex = index;
      this.doSearch(); // 筛选条件变化，重新加载
    },
    
    // 难度picker事件处理
    onDifficultyChange(e) {
      const index = e.detail.value;
      // 选择具体难度
      this.selectedDifficultyId = this.difficulties[index ].id;
      this.difficultyPickerIndex = index;
      this.doSearch(); // 筛选条件变化，重新加载
    },
    
    showChapterSelector() {
      this.showChapterPopup = true;
    },
    
    closeChapterSelector() {
      this.showChapterPopup = false;
    },
    
 
    
    selectChapter(chapter,fullName) {
      this.selectedChapterId = chapter.value;
      this.selectedChapterName = fullName;
      this.closeChapterSelector();
      this.doSearch(); // 筛选条件变化，重新加载
    },
 
    
    goToAddQuestion() {
      const url = `/pages/question/imp?bankId=${this.bankId}`;
      uni.navigateTo({ url });
    },
    
    editQuestion(question,index) {
      uni.navigateTo({
        url: `/pages/question/edit?id=${question.id}&bankId=${this.bankId}`
      });
      this.editIndex = index;
    },
    
    getQuestionTypeName(type) {
      const typeObj = this.questionTypes.find(t => t.id === type);
      return typeObj ? typeObj.name : type;
    },
    
    getQuestionTypeStyle(type) {
      const styles = {
        1: 'bg-blue-50 text-blue-600',
        2: 'bg-primary-50 text-primary-600',
        3: 'bg-yellow-50 text-yellow-600'
      };
      return styles[type] || 'bg-gray-100 text-gray-600';
    },
    
    getDifficultyName(difficulty) {
      const difficultyObj = this.difficulties.find(d => d.id === difficulty);
      return difficultyObj ? difficultyObj.name : difficulty;
    },
    
    getDifficultyStyle(difficulty) {
      const styles = {
        1: 'bg-green-50 text-green-600',
        2: 'bg-yellow-50 text-yellow-600',
        3: 'bg-red-50 text-red-600'
      };
      return styles[difficulty] || 'bg-gray-100 text-gray-600';
    },

    toggleExpandQuestion(questionId) {
      const index = this.expandedQuestions.indexOf(questionId);
      if (index > -1) {
        this.expandedQuestions.splice(index, 1);
      } else {
        this.expandedQuestions.push(questionId);
      }
    },
    
    clearSearchKeyword() {
      this.searchKeyword = '';
      this.doSearch(); // 筛选条件变化，重新加载
    },
    
    clearTypeFilter() {
      this.selectedTypeId = '';
      this.typePickerIndex = 0;
      this.doSearch(); // 筛选条件变化，重新加载
      return false; // 阻止事件冒泡
    },
    
    clearChapterFilter() {
      this.selectedChapterId = '';
      this.selectedChapterName = '';
      this.doSearch(); // 筛选条件变化，重新加载
      return false; // 阻止事件冒泡
    },
    
    clearDifficultyFilter() {
      this.selectedDifficultyId = '';
      this.difficultyPickerIndex = 0;
      this.doSearch(); // 筛选条件变化，重新加载
      return false; // 阻止事件冒泡
    },
    
 

    // 检查题目是否被选中
    isSelected(questionId) {
      return this.selectedQuestions.includes(questionId);
    },
    
    // 切换选择状态
    toggleSelection(questionId) {
      const index = this.selectedQuestions.indexOf(questionId);
      if (index > -1) {
        this.selectedQuestions.splice(index, 1);
      } else {
        this.selectedQuestions.push(questionId);
      }
    },
    goToEditQuestion(question){
      uni.navigateTo({
        url: `/pages/question/edit?id=${question.id}`
      });
    },
    	// 格式化答案显示
			formatAnswer(question) {
				// 判断题
				if (question.questionType === 3) {
					return question.rightAnswer === '0' ? '正确' : '错误';
				}
				// 选择题
				if (question.questionType === 1 || question.questionType === 2) {
					return question.rightAnswer.split(',')
						.map(index => String.fromCharCode(65 + parseInt(index)))
						.join(', ');
				}
				return question.rightAnswers.join(', ');
			},
    
    // 删除选中的题目
    deleteSelectedQuestions() {
      if (this.selectedQuestions.length === 0) return;
      
      uni.showModal({
        title: '确认删除',
        content: `确定要删除这${this.selectedQuestions.length}道题目吗？`,
        success: (res) => {
          if (res.confirm) {
            this.$reqPost('/front/edu-personal/question/delete/'+this.bankId, {ids: this.selectedQuestions}).then(res => {
              if(res.success){
                uni.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
                this.selectedQuestions = [];
                // this.resetAndReload(); // 使用分页mixin中的方法重置并重新加载
                this.$refs.scrollPagination.init();
              } else {
                uni.showToast({
                  title: '删除失败',
                  icon: 'error'
                });
              }
            });
          }
        }
      });
    }
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;  
  overflow: hidden;
}
</style>