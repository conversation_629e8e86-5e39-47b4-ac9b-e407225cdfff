# 导刷题微信小程序 - 项目架构文档

## 项目概述



**项目名称**: 导刷题  
**项目描述**:导刷题是一个基于微信小程序的题库管理刷题学习APP，提供个人题库管理、题库练习、考试组织、群组管理等功能。
**技术栈**: uniapp (Vue3 Options API) + TailwindCSS 3.4.17 + Font Awesome 6.5.1  
**平台支持**: 微信小程序 + H5  
**设计理念**: 现代化UI设计，优秀的用户体验，跨平台兼容

## 功能模块设计

### 🏠 首页模块
**设计特点**:
- 顶部搜索题库
- 1级快速操作网格（上传题库（弹出窗（不是跳页面）输入题库名称-必填、描述）、考试中心（跳到考试列表）、我的群组）
- 我的题库（我加入的题库列表）
- 底部TabBar导航

**核心功能**:
- 快速跳转到各功能模块
- 我的题库列表可点击右边三个点图标展开弹出操作（置顶/取消置顶-点击直接置顶/取消置顶、(题库拥有者：修改题库基本信息（点击弹出窗修改题库名称、描述）、权限设置-点击跳到题库权限设置页面)、章节管理（点击跳到章节管理页面）、添加题目（跳到添加题目页面）、编辑题目（跳到题目列表页面）、退出题库（点击弹出确认框））


### 登录授权模块
**设计特点**:
- 支持微信授权手机号登录






### 📚 题库模块

#### 题库权限设置
- 允许题库市场搜索
- 密码加入（当是时，显示密码管理）
- 密码管理（显示三个选项卡（全部、一次性密码、 固定密码）） 可点击切换
  - 全部：显示所有密码
  - 一次性密码：显示一次性密码
  - 固定密码：显示固定密码
    密码列表可删除
    可点击生成密码：弹出窗（不是跳页面）
      密码类型（一次性密码、固定密码）
      设置密码：输入6-32位的数字或者字符串，不输入时系统默认生成6位随机密码
      提交：点击提交按钮，将密码添加到密码列表中
    密码列表可点击复制密码




#### 章节管理
**功能特性**:
- 章节和子章节管理
- 添加、编辑、删除章节
- 章节排序（上移、下移）
- 章节重命名

#### 题目列表
**功能特性**:
- 显示当前题库下所有的题目
- 可按题干、章节、题型搜索
- 可对当前题库下的题目进行操作（编辑、删除）
**列表显示**:
- 题干
- 题型（题型 1、单选题 2、多选题 3、判断题  9、组合题）

#### 添加题目
**功能特性**:
- 单题录入（点击跳到单题录入/编辑页面）
- Excel导入（点击跳到一个excel上传界面）
- 文本导入 (点击跳到一个文本录入textarea和上传txt的界面)

#### 添加题目单题录入/编辑
**功能特性**:
- 录入题目信息点击保存提交到服务器
      字段：
        题干
        选项
        答案
        解析
        题型（1、单选题 2、多选题 3、判断题 9、组合题）
        难度（1、简单 2、中等 3、困难）
        所属章节



#### 题库练习
**功能特性**:
- 显示题目名称、描述、创建时间、总题数
- 加入（如权限是密码则需要输入密码）
- 章节练习、随机练习、题型练习、模拟考试、顺序练习
- 我的错题、我的收藏、我的笔记
- 分享


  

