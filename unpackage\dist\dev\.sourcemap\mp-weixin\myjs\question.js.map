{"version": 3, "file": "question.js", "sources": ["myjs/question.js"], "sourcesContent": ["const questionFields = [{\r\n\t\tkey: 'questionContent',\r\n\t\tpattern: /^\\d{1,}[.、:：。]/,\r\n\t},\r\n\r\n\t{\r\n\t\tkey: 'questionContent',\r\n\t\tpattern: /^\\(\\d{1,}.\\d{1,}\\)/,\r\n\t},\r\n\r\n\t{\r\n\t\tkey: 'analysis',\r\n\t\tpattern: /^解析[.、:：。]/,\r\n\t},\r\n\t{\r\n\t\tkey: 'rightAnswer',\r\n\t\tpattern: /^答案[.、:：。]/,\r\n\t},\r\n\r\n\t{\r\n\t\tkey: 'difficulty',\r\n\t\tpattern: /^难度[.、:：。]/,\r\n\t},\r\n\r\n\t{\r\n\t\tkey: 'chapterNames',\r\n\t\tpattern: /^章节[.、:：。]/,\r\n\t},\r\n\r\n\t{\r\n\t\tkey: 'options',\r\n\t\tpattern: /^[A-Z,a-z][.、:：。]/,\r\n\t},\r\n];\r\n\r\nconst yesNoRightAnswers = [\r\n\t['正确', '是', '对', 'T', 't'],\r\n\t['错', '错误', 'F', 'f', '否'],\r\n];\r\n\r\nconst difficulties = [\r\n\t['低', '简单', '易'],\r\n\t['中', '一般', '适中'],\r\n\t['高', '困难', '难'],\r\n];\r\n\r\n\r\nexport const questionTypeEnum = [{\r\n\t\tlabel: '单选题',\r\n\t\tvalue: 1\r\n\t},\r\n\t{\r\n\t\tlabel: '多选题',\r\n\t\tvalue: 2\r\n\t},\r\n\t{\r\n\t\tlabel: '判断题',\r\n\t\tvalue: 3\r\n\t},\r\n\t{\r\n\t\tlabel: '填空题',\r\n\t\tvalue: 4,\r\n\t\tdisabled: true\r\n\t},\r\n\t{\r\n\t\tlabel: '问答题',\r\n\t\tvalue: 5,\r\n\t\tdisabled: true\r\n\t},\r\n\t{\r\n\t\tlabel: '组合题',\r\n\t\tvalue: 9\r\n\t},\r\n];\r\n\r\nconst difficultyEnum = [{\r\n\t\tlabel: '简单',\r\n\t\tvalue: 1\r\n\t},\r\n\t{\r\n\t\tlabel: '一般',\r\n\t\tvalue: 2\r\n\t},\r\n\t{\r\n\t\tlabel: '困难',\r\n\t\tvalue: 3\r\n\t},\r\n];\r\n\r\nconst optionPreEnum = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K'];\r\n\r\n\r\n  \r\n\r\nexport function textToQuestionList(content) {\r\n\tconst now = new Date().getTime();\r\n\r\n\tlet lastContentType = '';\r\n\tlet lastOptionIndex = -1;\r\n\r\n\tlet question = {\r\n\t\tquestionContent: '',\r\n\t\tquestionType: undefined,\r\n\t\toptions: [],\r\n\t\tanalysis: '',\r\n\t\tdifficulty: 2,\r\n\t\trightAnswers: [],\r\n\t\t_error: [],\r\n\t\t_index: 0,\r\n\t\t_rowIndex: 0,\r\n\t\tchapterFullName: '',\r\n\t};\r\n\r\n\tconst questionList = [];\r\n\tlet index = 0;\r\n\tconst lineTexts = content.split('\\n');\r\n\tconsole.log(lineTexts);\r\n\tlet groupQuestion = undefined;\r\n\r\n\tfor1: for (let i = 0; i < lineTexts.length; i++) {\r\n\t\tconst text = lineTexts[i].trim();\r\n\t\tif (!text) {\r\n\t\t\tcontinue;\r\n\t\t}\r\n\r\n\t\tfor (let k = 0; k < questionFields.length; k++) {\r\n\t\t\tconst {\r\n\t\t\t\tkey,\r\n\t\t\t\tpattern\r\n\t\t\t} = questionFields[k];\r\n\r\n\t\t\tif (pattern.test(text)) {\r\n\t\t\t\t//匹配出来的是题干\r\n\t\t\t\tif (k === 0) {\r\n\t\t\t\t\tlastContentType = '';\r\n\t\t\t\t\tquestion = {\r\n\t\t\t\t\t\tquestionContent: '',\r\n\t\t\t\t\t\tquestionType: 1,\r\n\t\t\t\t\t\tquestionTypeText: '单选题',\r\n\t\t\t\t\t\toptions: [],\r\n\t\t\t\t\t\tanalysis: '',\r\n\t\t\t\t\t\tdifficulty: 2,\r\n\t\t\t\t\t\trightAnswers: [],\r\n\t\t\t\t\t\t_error: [],\r\n\t\t\t\t\t\t_index: index++,\r\n\t\t\t\t\t\t_rowIndex: i,\r\n\t\t\t\t\t\tchapterFullName: '',\r\n\t\t\t\t\t};\r\n\t\t\t\t\t// groupQuestion = question;\r\n\t\t\t\t\tgroupQuestion = undefined;\r\n\t\t\t\t\tquestionList.push(question);\r\n\t\t\t\t\t//子题干\r\n\t\t\t\t} else if (k === 1) {\r\n\t\t\t\t\t//匹配出子题目\r\n\t\t\t\t\tif (\r\n\t\t\t\t\t\tquestion &&\r\n\t\t\t\t\t\tquestion.questionContent &&\r\n\t\t\t\t\t\t(!!groupQuestion ||\r\n\t\t\t\t\t\t\t(!question.options.length &&\r\n\t\t\t\t\t\t\t\t!question.rightAnswers.length &&\r\n\t\t\t\t\t\t\t\t!question.analysis))\r\n\t\t\t\t\t) {\r\n\t\t\t\t\t\tif (!groupQuestion) {\r\n\t\t\t\t\t\t\tquestion.children = [];\r\n\t\t\t\t\t\t\tgroupQuestion = question;\r\n\t\t\t\t\t\t\tgroupQuestion.questionType = 9;\r\n\t\t\t\t\t\t\tgroupQuestion.questionTypeText = '组合题';\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tquestion = {\r\n\t\t\t\t\t\t\tquestionContent: '',\r\n\t\t\t\t\t\t\tquestionType: 1,\r\n\t\t\t\t\t\t\tquestionTypeText: '单选题',\r\n\t\t\t\t\t\t\toptions: [],\r\n\t\t\t\t\t\t\tanalysis: '',\r\n\t\t\t\t\t\t\tdifficulty: 2,\r\n\t\t\t\t\t\t\trightAnswers: [],\r\n\t\t\t\t\t\t\t_error: [],\r\n\t\t\t\t\t\t\t_index: groupQuestion.children?.length || 0,\r\n\t\t\t\t\t\t\t_rowIndex: groupQuestion.children?.length || 0,\r\n\t\t\t\t\t\t\tchapterFullName: '',\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tif (groupQuestion.children) {\r\n\t\t\t\t\t\t\tgroupQuestion.children.push(question);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (key === 'options') {\r\n\t\t\t\t\tconst splitOptionTexts = text\r\n\t\t\t\t\t\t.replaceAll(/\\s[A-Z,a-z][.、:：。]/g, function(v) {\r\n\t\t\t\t\t\t\treturn '_||_' + v;\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.split('_||_');\r\n\r\n\t\t\t\t\tfor (let i = 0; i < splitOptionTexts.length; i++) {\r\n\t\t\t\t\t\tconst optionText = splitOptionTexts[i].trim();\r\n\t\t\t\t\t\tif (optionText) {\r\n\t\t\t\t\t\t\tconst optionIndex = optionPreEnum.indexOf(\r\n\t\t\t\t\t\t\t\toptionText.substring(0, 1).toUpperCase(),\r\n\t\t\t\t\t\t\t);\r\n\r\n\t\t\t\t\t\t\tif (optionIndex >= 0) {\r\n\t\t\t\t\t\t\t\tlastOptionIndex = optionIndex;\r\n\t\t\t\t\t\t\t\t//console.log(optionIndex, optionText.substring(2));\r\n\t\t\t\t\t\t\t\tquestion.options[optionIndex] = optionText.substring(2);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// if(question.options.length&&!question.questionType){\r\n\t\t\t\t\t//     question.questionType = 1;\r\n\t\t\t\t\t//     question.questionTypeText = '单选题';\r\n\t\t\t\t\t// }\r\n\r\n\r\n\r\n\t\t\t\t} else if (key === 'rightAnswer') {\r\n\t\t\t\t\tconst newText = text.replace(pattern, '');\r\n\t\t\t\t\tif (!newText) {\r\n\t\t\t\t\t\tcontinue for1;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tlet yesNoIndex = -1;\r\n\t\t\t\t\tyesNoRightAnswers.forEach((yesNoItem, index) => {\r\n\t\t\t\t\t\tif (yesNoItem.indexOf(newText) >= 0) {\r\n\t\t\t\t\t\t\tyesNoIndex = index;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\tif (yesNoIndex >= 0) {\r\n\t\t\t\t\t\tquestion.rightAnswers = [yesNoIndex + ''];\r\n\t\t\t\t\t\tquestion.questionType = 3;\r\n\t\t\t\t\t\tcontinue for1;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tconst rightAnswers = newText\r\n\t\t\t\t\t\t.replaceAll(',', '')\r\n\t\t\t\t\t\t.replaceAll('，', '')\r\n\t\t\t\t\t\t.replaceAll('、', '')\r\n\t\t\t\t\t\t.replaceAll(' ', '')\r\n\t\t\t\t\t\t.split('')\r\n\t\t\t\t\t\t.map((optionItem) => {\r\n\t\t\t\t\t\t\treturn optionPreEnum.indexOf(optionItem.toUpperCase()) + '';\r\n\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\t.sort();\r\n\r\n\t\t\t\t\tquestion.rightAnswers = rightAnswers.filter((element, index) => {\r\n\t\t\t\t\t\treturn rightAnswers.indexOf(element) === index;\r\n\t\t\t\t\t});\r\n\t\t\t\t} else if (key === 'difficulty') {\r\n\t\t\t\t\tlet difficulty = -1;\r\n\t\t\t\t\tconst newText = text.replace(pattern, '');\r\n\t\t\t\t\tdifficulties.forEach((t, index) => {\r\n\t\t\t\t\t\tif (t.indexOf(newText) >= 0) {\r\n\t\t\t\t\t\t\tdifficulty = index;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\tif (difficulty >= 0) {\r\n\t\t\t\t\t\tquestion.difficulty = difficulty + 1;\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (key === 'chapterNames') {\r\n\t\t\t\t\tif (!groupQuestion) {\r\n\t\t\t\t\t\tconst newText = text.replace(pattern, '');\r\n\r\n\t\t\t\t\t\tquestion.chapterFullName = newText\r\n\t\t\t\t\t\t\t.split('/')\r\n\t\t\t\t\t\t\t.map((item) => item.trim())\r\n\t\t\t\t\t\t\t.filter((item) => !!item)\r\n\t\t\t\t\t\t\t.join(' / ');\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconst newText = text.replace(pattern, '');\r\n\t\t\t\t\tquestion[key] = newText;\r\n\t\t\t\t}\r\n\t\t\t\tlastContentType = key;\r\n\t\t\t\tcontinue for1;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (lastContentType) {\r\n\t\t\tif (lastContentType === 'options') {\r\n\t\t\t\tif (lastOptionIndex >= 0) {\r\n\t\t\t\t\tquestion.options[lastOptionIndex] =\r\n\t\t\t\t\t\t(question.options[lastOptionIndex] || '') + '<br>' + text;\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tquestion[lastContentType] = question[lastContentType] + '<br>' + text;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// const errorQuestionIndexes: number[] = [];\r\n\r\n\tfunction checkError(questionList) {\r\n\t\tquestionList.forEach((item) => {\r\n\t\t\titem.difficultyText = difficultyEnum.find(\r\n\t\t\t\t(difficultyEnumItem) => difficultyEnumItem.value === item.difficulty,\r\n\t\t\t)?.label;\r\n\t\t\tif (item.questionType === 9) {\r\n\t\t\t\t//组合题\r\n\t\t\t\tif (item.children && item.children.length) {\r\n\t\t\t\t\tcheckError(item.children);\r\n\t\t\t\t} else {\r\n\t\t\t\t\titem._error.push('子题目不能为空');\r\n\t\t\t\t}\r\n\t\t\t} else if (item.questionType !== 3) {\r\n\t\t\t\t// if (item.difficulty < 0) {\r\n\t\t\t\t//   item._error.push('难度');\r\n\t\t\t\t// }\r\n\r\n\t\t\t\tif (item.options.length < 2) {\r\n\t\t\t\t\titem._error.push('选项不能少于2个');\r\n\t\t\t\t\t// item.questionTypeText = '未知题';\r\n\t\t\t\t\t// errorQuestionIndexes.push(index);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (!item.rightAnswers.length) {\r\n\t\t\t\t\titem._error.push('答案不能为空');\r\n\t\t\t\t\t// item.questionTypeText = '未知题';\r\n\t\t\t\t\t// errorQuestionIndexes.push(index);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\titem.questionType = item.rightAnswers.length === 1 ? 1 : 2;\r\n\r\n\t\t\t\titem.questionTypeText = questionTypeEnum.find(\r\n\t\t\t\t\t(questionType) => questionType.value === item.questionType,\r\n\t\t\t\t)?.label;\r\n\r\n\t\t\t\tconst options = [...item.options];\r\n\r\n\t\t\t\tif (options.filter((option) => option === undefined).length) {\r\n\t\t\t\t\titem._error.push('选项标识必须是连续的（A-J）');\r\n\t\t\t\t\t//errorQuestionIndexes.push(index);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (options.filter((option) => option === '').length) {\r\n\t\t\t\t\titem._error.push('选项内容不允许为空');\r\n\t\t\t\t\t// errorQuestionIndexes.push(index);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t// console.log(item.rightAnswers, item.options);\r\n\t\t\t\t// if (item.rightAnswers.length === 0) {\r\n\t\t\t\t//   item._error.push('答案不允许为空');\r\n\t\t\t\t//   // errorQuestionIndexes.push(index);\r\n\t\t\t\t//   return;\r\n\t\t\t\t// }\r\n\r\n\t\t\t\tif (item.rightAnswers.find((index) => !options[parseInt(index)])) {\r\n\t\t\t\t\titem._error.push('答案与选项不匹配');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (\r\n\t\t\t\t\titem.chapterFullName &&\r\n\t\t\t\t\titem.chapterFullName.split(' / ').length > 2\r\n\t\t\t\t) {\r\n\t\t\t\t\titem._error.push('章节最多只能为2级');\r\n\t\t\t\t\t//item.questionTypeText = '未知题';\r\n\t\t\t\t\t// errorQuestionIndexes.push(index);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\titem.questionTypeText = questionTypeEnum.find(\r\n\t\t\t\t\t(questionType) => questionType.value === item.questionType,\r\n\t\t\t\t)?.label;\r\n\t\t\t}\r\n\t\t});\r\n\t}\r\n\tcheckError(questionList);\r\n\r\n\r\n\r\n\t// setQuestionList(questionList);\r\n\r\n\tconsole.log('end', new Date().getTime() - now);\r\n\r\n\r\n\treturn questionList;\r\n}"], "names": ["uni", "i", "index", "questionList", "_a", "_b"], "mappings": ";;AAAA,MAAM,iBAAiB;AAAA,EAAC;AAAA,IACtB,KAAK;AAAA,IACL,SAAS;AAAA,EACT;AAAA,EAED;AAAA,IACC,KAAK;AAAA,IACL,SAAS;AAAA,EACT;AAAA,EAED;AAAA,IACC,KAAK;AAAA,IACL,SAAS;AAAA,EACT;AAAA,EACD;AAAA,IACC,KAAK;AAAA,IACL,SAAS;AAAA,EACT;AAAA,EAED;AAAA,IACC,KAAK;AAAA,IACL,SAAS;AAAA,EACT;AAAA,EAED;AAAA,IACC,KAAK;AAAA,IACL,SAAS;AAAA,EACT;AAAA,EAED;AAAA,IACC,KAAK;AAAA,IACL,SAAS;AAAA,EACT;AACF;AAEA,MAAM,oBAAoB;AAAA,EACzB,CAAC,MAAM,KAAK,KAAK,KAAK,GAAG;AAAA,EACzB,CAAC,KAAK,MAAM,KAAK,KAAK,GAAG;AAC1B;AAEA,MAAM,eAAe;AAAA,EACpB,CAAC,KAAK,MAAM,GAAG;AAAA,EACf,CAAC,KAAK,MAAM,IAAI;AAAA,EAChB,CAAC,KAAK,MAAM,GAAG;AAChB;AAGY,MAAC,mBAAmB;AAAA,EAAC;AAAA,IAC/B,OAAO;AAAA,IACP,OAAO;AAAA,EACP;AAAA,EACD;AAAA,IACC,OAAO;AAAA,IACP,OAAO;AAAA,EACP;AAAA,EACD;AAAA,IACC,OAAO;AAAA,IACP,OAAO;AAAA,EACP;AAAA,EACD;AAAA,IACC,OAAO;AAAA,IACP,OAAO;AAAA,IACP,UAAU;AAAA,EACV;AAAA,EACD;AAAA,IACC,OAAO;AAAA,IACP,OAAO;AAAA,IACP,UAAU;AAAA,EACV;AAAA,EACD;AAAA,IACC,OAAO;AAAA,IACP,OAAO;AAAA,EACP;AACF;AAEA,MAAM,iBAAiB;AAAA,EAAC;AAAA,IACtB,OAAO;AAAA,IACP,OAAO;AAAA,EACP;AAAA,EACD;AAAA,IACC,OAAO;AAAA,IACP,OAAO;AAAA,EACP;AAAA,EACD;AAAA,IACC,OAAO;AAAA,IACP,OAAO;AAAA,EACP;AACF;AAEA,MAAM,gBAAgB,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAKrE,SAAS,mBAAmB,SAAS;;AAC3C,QAAM,OAAM,oBAAI,KAAM,GAAC,QAAO;AAE9B,MAAI,kBAAkB;AACtB,MAAI,kBAAkB;AAEtB,MAAI,WAAW;AAAA,IACd,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,SAAS,CAAE;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,cAAc,CAAE;AAAA,IAChB,QAAQ,CAAE;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,iBAAiB;AAAA,EACnB;AAEC,QAAM,eAAe,CAAA;AACrB,MAAI,QAAQ;AACZ,QAAM,YAAY,QAAQ,MAAM,IAAI;AACpCA,gBAAAA,MAAY,MAAA,OAAA,2BAAA,SAAS;AACrB,MAAI,gBAAgB;AAEpB;AAAM,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAChD,YAAM,OAAO,UAAU,CAAC,EAAE,KAAI;AAC9B,UAAI,CAAC,MAAM;AACV;AAAA,MACA;AAED,eAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC/C,cAAM;AAAA,UACL;AAAA,UACA;AAAA,QACJ,IAAO,eAAe,CAAC;AAEpB,YAAI,QAAQ,KAAK,IAAI,GAAG;AAEvB,cAAI,MAAM,GAAG;AACZ,8BAAkB;AAClB,uBAAW;AAAA,cACV,iBAAiB;AAAA,cACjB,cAAc;AAAA,cACd,kBAAkB;AAAA,cAClB,SAAS,CAAE;AAAA,cACX,UAAU;AAAA,cACV,YAAY;AAAA,cACZ,cAAc,CAAE;AAAA,cAChB,QAAQ,CAAE;AAAA,cACV,QAAQ;AAAA,cACR,WAAW;AAAA,cACX,iBAAiB;AAAA,YACvB;AAEK,4BAAgB;AAChB,yBAAa,KAAK,QAAQ;AAAA,UAE/B,WAAe,MAAM,GAAG;AAEnB,gBACC,YACA,SAAS,oBACR,CAAC,CAAC,iBACD,CAAC,SAAS,QAAQ,UAClB,CAAC,SAAS,aAAa,UACvB,CAAC,SAAS,WACX;AACD,kBAAI,CAAC,eAAe;AACnB,yBAAS,WAAW;AACpB,gCAAgB;AAChB,8BAAc,eAAe;AAC7B,8BAAc,mBAAmB;AAAA,cACjC;AAED,yBAAW;AAAA,gBACV,iBAAiB;AAAA,gBACjB,cAAc;AAAA,gBACd,kBAAkB;AAAA,gBAClB,SAAS,CAAE;AAAA,gBACX,UAAU;AAAA,gBACV,YAAY;AAAA,gBACZ,cAAc,CAAE;AAAA,gBAChB,QAAQ,CAAE;AAAA,gBACV,UAAQ,mBAAc,aAAd,mBAAwB,WAAU;AAAA,gBAC1C,aAAW,mBAAc,aAAd,mBAAwB,WAAU;AAAA,gBAC7C,iBAAiB;AAAA,cACxB;AACM,kBAAI,cAAc,UAAU;AAC3B,8BAAc,SAAS,KAAK,QAAQ;AAAA,cACpC;AAAA,YACP,OAAY;AACN;AAAA,YACA;AAAA,UACD;AAED,cAAI,QAAQ,WAAW;AACtB,kBAAM,mBAAmB,KACvB,WAAW,uBAAuB,SAAS,GAAG;AAC9C,qBAAO,SAAS;AAAA,YACvB,CAAO,EACA,MAAM,MAAM;AAEd,qBAASC,KAAI,GAAGA,KAAI,iBAAiB,QAAQA,MAAK;AACjD,oBAAM,aAAa,iBAAiBA,EAAC,EAAE,KAAI;AAC3C,kBAAI,YAAY;AACf,sBAAM,cAAc,cAAc;AAAA,kBACjC,WAAW,UAAU,GAAG,CAAC,EAAE,YAAa;AAAA,gBAChD;AAEO,oBAAI,eAAe,GAAG;AACrB,oCAAkB;AAElB,2BAAS,QAAQ,WAAW,IAAI,WAAW,UAAU,CAAC;AAAA,gBACtD;AAAA,cACD;AAAA,YACD;AAAA,UAQN,WAAe,QAAQ,eAAe;AACjC,kBAAM,UAAU,KAAK,QAAQ,SAAS,EAAE;AACxC,gBAAI,CAAC,SAAS;AACb,uBAAS;AAAA,YACT;AAED,gBAAI,aAAa;AACjB,8BAAkB,QAAQ,CAAC,WAAWC,WAAU;AAC/C,kBAAI,UAAU,QAAQ,OAAO,KAAK,GAAG;AACpC,6BAAaA;AAAA,cACb;AAAA,YACP,CAAM;AAED,gBAAI,cAAc,GAAG;AACpB,uBAAS,eAAe,CAAC,aAAa,EAAE;AACxC,uBAAS,eAAe;AACxB,uBAAS;AAAA,YACT;AAED,kBAAM,eAAe,QACnB,WAAW,KAAK,EAAE,EAClB,WAAW,KAAK,EAAE,EAClB,WAAW,KAAK,EAAE,EAClB,WAAW,KAAK,EAAE,EAClB,MAAM,EAAE,EACR,IAAI,CAAC,eAAe;AACpB,qBAAO,cAAc,QAAQ,WAAW,YAAa,CAAA,IAAI;AAAA,YAChE,CAAO,EAEA;AAEF,qBAAS,eAAe,aAAa,OAAO,CAAC,SAASA,WAAU;AAC/D,qBAAO,aAAa,QAAQ,OAAO,MAAMA;AAAA,YAC/C,CAAM;AAAA,UACN,WAAe,QAAQ,cAAc;AAChC,gBAAI,aAAa;AACjB,kBAAM,UAAU,KAAK,QAAQ,SAAS,EAAE;AACxC,yBAAa,QAAQ,CAAC,GAAGA,WAAU;AAClC,kBAAI,EAAE,QAAQ,OAAO,KAAK,GAAG;AAC5B,6BAAaA;AAAA,cACb;AAAA,YACP,CAAM;AACD,gBAAI,cAAc,GAAG;AACpB,uBAAS,aAAa,aAAa;AAAA,YACnC;AAAA,UACN,WAAe,QAAQ,gBAAgB;AAClC,gBAAI,CAAC,eAAe;AACnB,oBAAM,UAAU,KAAK,QAAQ,SAAS,EAAE;AAExC,uBAAS,kBAAkB,QACzB,MAAM,GAAG,EACT,IAAI,CAAC,SAAS,KAAK,KAAI,CAAE,EACzB,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,EACvB,KAAK,KAAK;AAAA,YACZ;AAAA,UACN,OAAW;AACN,kBAAM,UAAU,KAAK,QAAQ,SAAS,EAAE;AACxC,qBAAS,GAAG,IAAI;AAAA,UAChB;AACD,4BAAkB;AAClB,mBAAS;AAAA,QACT;AAAA,MACD;AAED,UAAI,iBAAiB;AACpB,YAAI,oBAAoB,WAAW;AAClC,cAAI,mBAAmB,GAAG;AACzB,qBAAS,QAAQ,eAAe,KAC9B,SAAS,QAAQ,eAAe,KAAK,MAAM,SAAS;AAAA,UACtD;AAAA,QACL,OAAU;AACN,mBAAS,eAAe,IAAI,SAAS,eAAe,IAAI,SAAS;AAAA,QACjE;AAAA,MACD;AAAA,IACD;AAID,WAAS,WAAWC,eAAc;AACjC,IAAAA,cAAa,QAAQ,CAAC,SAAS;;AAC9B,WAAK,kBAAiBC,MAAA,eAAe;AAAA,QACpC,CAAC,uBAAuB,mBAAmB,UAAU,KAAK;AAAA,MAC1D,MAFqB,gBAAAA,IAEnB;AACH,UAAI,KAAK,iBAAiB,GAAG;AAE5B,YAAI,KAAK,YAAY,KAAK,SAAS,QAAQ;AAC1C,qBAAW,KAAK,QAAQ;AAAA,QAC7B,OAAW;AACN,eAAK,OAAO,KAAK,SAAS;AAAA,QAC1B;AAAA,MACL,WAAc,KAAK,iBAAiB,GAAG;AAKnC,YAAI,KAAK,QAAQ,SAAS,GAAG;AAC5B,eAAK,OAAO,KAAK,UAAU;AAG3B;AAAA,QACA;AACD,YAAI,CAAC,KAAK,aAAa,QAAQ;AAC9B,eAAK,OAAO,KAAK,QAAQ;AAGzB;AAAA,QACA;AAED,aAAK,eAAe,KAAK,aAAa,WAAW,IAAI,IAAI;AAEzD,aAAK,oBAAmBC,MAAA,iBAAiB;AAAA,UACxC,CAAC,iBAAiB,aAAa,UAAU,KAAK;AAAA,QAC9C,MAFuB,gBAAAA,IAErB;AAEH,cAAM,UAAU,CAAC,GAAG,KAAK,OAAO;AAEhC,YAAI,QAAQ,OAAO,CAAC,WAAW,WAAW,MAAS,EAAE,QAAQ;AAC5D,eAAK,OAAO,KAAK,iBAAiB;AAElC;AAAA,QACA;AAED,YAAI,QAAQ,OAAO,CAAC,WAAW,WAAW,EAAE,EAAE,QAAQ;AACrD,eAAK,OAAO,KAAK,WAAW;AAE5B;AAAA,QACA;AAQD,YAAI,KAAK,aAAa,KAAK,CAACH,WAAU,CAAC,QAAQ,SAASA,MAAK,CAAC,CAAC,GAAG;AACjE,eAAK,OAAO,KAAK,UAAU;AAC3B;AAAA,QACA;AAED,YACC,KAAK,mBACL,KAAK,gBAAgB,MAAM,KAAK,EAAE,SAAS,GAC1C;AACD,eAAK,OAAO,KAAK,WAAW;AAG5B;AAAA,QACA;AAAA,MACL,OAAU;AACN,aAAK,oBAAmB,sBAAiB;AAAA,UACxC,CAAC,iBAAiB,aAAa,UAAU,KAAK;AAAA,QAC9C,MAFuB,mBAErB;AAAA,MACH;AAAA,IACJ,CAAG;AAAA,EACD;AACD,aAAW,YAAY;AAMvBF,8DAAY,QAAO,oBAAI,QAAO,YAAY,GAAG;AAG7C,SAAO;AACR;;;"}