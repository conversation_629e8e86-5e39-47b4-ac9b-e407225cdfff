<template>
  <view class="h-screen bg-gray-50">
    <!-- 自定义导航栏 -->

    <view class="pt-4 pb-4">
      <view class="mx-4 bg-white rounded-xl shadow-sm p-4">
        <view
          v-for="item in typeList"
          :key="item.value"
          class="mb-3 bg-gray-50 rounded-lg p-3 flex items-center justify-between"
          @click="startPractice(item)"
        >
          <view class="flex items-center">
            <view
              class="w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center mr-3"
            >
              <i class="fas fa-clipboard-list text-primary-500 text-base"></i>
            </view>
            <text class="text-base font-medium text-gray-800">{{
              item.label
            }}</text>
          </view>
          <text class="text-sm text-gray-500"
            >共{{ item.questionCount }}题</text
          >
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { questionTypeEnum } from "@/myjs/question.js";

export default {
  data() {
    return {
      typeList: [],
      bankId: "",
    };
  },
  onLoad(options) {
    this.bankId = options.bankId || "";
    this.loadTypeList();
  },
  methods: {
    loadTypeList() {
      if (!this.bankId) return;

      this.$reqGet(`/front/edu/qbank/questionTypeList/${this.bankId}`).then(
        (res) => {
          if (res.success) {
            this.typeList = (res.data || []).map((item) => {
              const match = questionTypeEnum.find((q) => q.value === item.type);
              return {
                ...item,
                label: match ? match.label : item.label,
              };
            });
          }
        }
      );
    },
    startPractice(item) {
      uni.navigateTo({
        url: `/pages/practice/do?mode=type&bankId=${this.bankId}&type=${item.type}&typeName=${item.label}`,
      });
    },
  },
};
</script>

<style scoped>
/* 可根据需要补充样式 */
</style>
