<template>
  <view class="min-h-screen bg-gray-50">
    <!-- 练习模式选择 -->
    <view class="bg-white px-4 py-4 border-b border-gray-100">
      <text class="text-sm font-medium text-gray-800 block mb-3">练习模式</text>
      <view class="flex flex-wrap gap-2">
        <button 
          v-for="mode in practiceModes" 
          :key="mode.id"
          @click="selectPracticeMode(mode)"
          :class="[
            'px-3 py-2 rounded-lg text-sm',
            selectedMode === mode.id 
              ? 'bg-primary-500 text-white' 
              : 'bg-gray-100 text-gray-600'
          ]"
        >
          {{ mode.name }}
        </button>
      </view>
    </view>

    <!-- 练习设置 -->
    <view class="px-4 py-4">
      <view class="bg-white rounded-xl p-4 shadow-sm mb-4">
        <text class="text-lg font-bold text-gray-800 mb-4 block">练习设置</text>
        
        <!-- 章节选择 -->
        <view v-if="selectedMode === 'chapter'" class="mb-4">
          <text class="text-sm font-medium text-gray-800 block mb-3">选择章节</text>
          <view class="space-y-2">
            <view 
              v-for="chapter in chapters" 
              :key="chapter.id"
              @click="toggleChapter(chapter.id)"
              class="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
              :class="{ 'border-primary-500 bg-primary-50': selectedChapters.includes(chapter.id) }"
            >
              <view>
                <text class="text-sm font-medium text-gray-800 block">{{ chapter.name }}</text>
                <text class="text-xs text-gray-500">{{ chapter.questionCount }}题</text>
              </view>
              <view :class="[
                'w-5 h-5 rounded-full border-2 flex items-center justify-center',
                selectedChapters.includes(chapter.id) 
                  ? 'border-primary-500 bg-primary-500' 
                  : 'border-gray-300'
              ]">
                <text v-if="selectedChapters.includes(chapter.id)" class="fas fa-check text-white text-xs"></text>
              </view>
            </view>
          </view>
        </view>

        <!-- 题型选择 -->
        <view v-if="selectedMode === 'type'" class="mb-4">
          <text class="text-sm font-medium text-gray-800 block mb-3">选择题型</text>
          <view class="space-y-2">
            <view 
              v-for="type in questionTypes" 
              :key="type.id"
              @click="toggleQuestionType(type.id)"
              class="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
              :class="{ 'border-primary-500 bg-primary-50': selectedTypes.includes(type.id) }"
            >
              <view>
                <text class="text-sm font-medium text-gray-800 block">{{ type.name }}</text>
                <text class="text-xs text-gray-500">{{ type.count }}题</text>
              </view>
              <view :class="[
                'w-5 h-5 rounded-full border-2 flex items-center justify-center',
                selectedTypes.includes(type.id) 
                  ? 'border-primary-500 bg-primary-500' 
                  : 'border-gray-300'
              ]">
                <text v-if="selectedTypes.includes(type.id)" class="fas fa-check text-white text-xs"></text>
              </view>
            </view>
          </view>
        </view>

        <!-- 难度选择 -->
        <view class="mb-4">
          <text class="text-sm font-medium text-gray-800 block mb-3">难度等级</text>
          <view class="flex space-x-2">
            <button 
              v-for="difficulty in difficulties" 
              :key="difficulty.id"
              @click="toggleDifficulty(difficulty.id)"
              :class="[
                'flex-1 py-2 rounded-lg text-sm',
                selectedDifficulties.includes(difficulty.id) 
                  ? 'bg-primary-500 text-white' 
                  : 'bg-gray-100 text-gray-600'
              ]"
            >
              {{ difficulty.name }}
            </button>
          </view>
        </view>

        <!-- 题目数量 -->
        <view class="mb-4">
          <text class="text-sm font-medium text-gray-800 block mb-3">题目数量</text>
          <view class="flex items-center space-x-3">
            <button @click="decreaseQuestionCount" class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
              <text class="fas fa-minus text-gray-600 text-sm"></text>
            </button>
            <input 
              v-model.number="questionCount"
              type="number"
              class="flex-1 text-center py-2 border border-gray-200 rounded-lg text-sm"
              min="1"
              :max="maxQuestionCount"
            />
            <button @click="increaseQuestionCount" class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
              <text class="fas fa-plus text-gray-600 text-sm"></text>
            </button>
          </view>
          <text class="text-xs text-gray-500 mt-1">最多可选择 {{ maxQuestionCount }} 题</text>
        </view>

        <!-- 答题模式 -->
        <view class="mb-4">
          <text class="text-sm font-medium text-gray-800 block mb-3">答题模式</text>
          <view class="space-y-2">
            <view 
              v-for="answerMode in answerModes" 
              :key="answerMode.id"
              @click="selectAnswerMode(answerMode.id)"
              class="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
              :class="{ 'border-primary-500 bg-primary-50': selectedAnswerMode === answerMode.id }"
            >
              <view>
                <text class="text-sm font-medium text-gray-800 block">{{ answerMode.name }}</text>
                <text class="text-xs text-gray-500">{{ answerMode.description }}</text>
              </view>
              <view :class="[
                'w-5 h-5 rounded-full border-2 flex items-center justify-center',
                selectedAnswerMode === answerMode.id 
                  ? 'border-primary-500 bg-primary-500' 
                  : 'border-gray-300'
              ]">
                <text v-if="selectedAnswerMode === answerMode.id" class="fas fa-check text-white text-xs"></text>
              </view>
            </view>
          </view>
        </view>

        <!-- 其他设置 -->
        <view>
          <text class="text-sm font-medium text-gray-800 block mb-3">其他设置</text>
          <view class="space-y-3">
            <view class="flex items-center justify-between">
              <view>
                <text class="text-sm text-gray-800 block">随机打乱题目顺序</text>
                <text class="text-xs text-gray-500">每次练习题目顺序都不同</text>
              </view>
              <switch 
                :checked="settings.randomOrder" 
                @change="toggleRandomOrder"
                color="#007AFF"
              />
            </view>
            
            <view class="flex items-center justify-between">
              <view>
                <text class="text-sm text-gray-800 block">随机打乱选项顺序</text>
                <text class="text-xs text-gray-500">选择题选项顺序随机</text>
              </view>
              <switch 
                :checked="settings.randomOptions" 
                @change="toggleRandomOptions"
                color="#007AFF"
              />
            </view>
          </view>
        </view>
      </view>

      <!-- 开始练习按钮 -->
      <view class="mt-6">
        <button @click="startPractice" class="w-full py-3 bg-primary-500 text-white rounded-lg text-sm font-medium">
          开始练习（{{ availableQuestionCount }}题）
        </button>
      </view>
    </view>

    <!-- 底部安全区域 -->
    <view class="h-20"></view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      questionBankId: '',
      selectedMode: 'random',
      practiceModes: [
        { id: 'random', name: '随机练习' },
        { id: 'chapter', name: '章节练习' },
        { id: 'type', name: '题型练习' },
        { id: 'sequence', name: '顺序练习' }
      ],
      chapters: [
        { id: '1', name: '第一章 基础知识', questionCount: 25 },
        { id: '2', name: '第二章 进阶内容', questionCount: 18 },
        { id: '3', name: '第三章 实践应用', questionCount: 32 }
      ],
      questionTypes: [
        { id: 'single', name: '单选题', count: 35 },
        { id: 'multiple', name: '多选题', count: 20 },
        { id: 'judge', name: '判断题', count: 15 },
        { id: 'fill', name: '填空题', count: 5 }
      ],
      difficulties: [
        { id: 'easy', name: '简单' },
        { id: 'medium', name: '中等' },
        { id: 'hard', name: '困难' }
      ],
      answerModes: [
        { id: 'immediate', name: '即时反馈', description: '答题后立即显示结果' },
        { id: 'final', name: '最终反馈', description: '全部答完后显示结果' }
      ],
      selectedChapters: [],
      selectedTypes: [],
      selectedDifficulties: ['easy', 'medium', 'hard'],
      selectedAnswerMode: 'immediate',
      questionCount: 20,
      maxQuestionCount: 75,
      settings: {
        randomOrder: true,
        randomOptions: false
      }
    }
  },
  computed: {
    availableQuestionCount() {
      // 根据选择的条件计算可用题目数量
      let count = this.maxQuestionCount;
      
      if (this.selectedMode === 'chapter' && this.selectedChapters.length > 0) {
        count = this.chapters
          .filter(c => this.selectedChapters.includes(c.id))
          .reduce((sum, c) => sum + c.questionCount, 0);
      } else if (this.selectedMode === 'type' && this.selectedTypes.length > 0) {
        count = this.questionTypes
          .filter(t => this.selectedTypes.includes(t.id))
          .reduce((sum, t) => sum + t.count, 0);
      }
      
      return Math.min(count, this.questionCount);
    }
  },
  onLoad(options) {
    if (options.id) {
      this.questionBankId = options.id;
      this.loadQuestionBankInfo();
    }
  },
  methods: {
    loadQuestionBankInfo() {
      // 模拟加载题库信息
      // 实际项目中这里应该调用API
    },
    
    selectPracticeMode(mode) {
      this.selectedMode = mode.id;
      
      // 重置相关选择
      if (mode.id !== 'chapter') {
        this.selectedChapters = [];
      }
      if (mode.id !== 'type') {
        this.selectedTypes = [];
      }
    },
    
    toggleChapter(chapterId) {
      const index = this.selectedChapters.indexOf(chapterId);
      if (index > -1) {
        this.selectedChapters.splice(index, 1);
      } else {
        this.selectedChapters.push(chapterId);
      }
    },
    
    toggleQuestionType(typeId) {
      const index = this.selectedTypes.indexOf(typeId);
      if (index > -1) {
        this.selectedTypes.splice(index, 1);
      } else {
        this.selectedTypes.push(typeId);
      }
    },
    
    toggleDifficulty(difficultyId) {
      const index = this.selectedDifficulties.indexOf(difficultyId);
      if (index > -1) {
        this.selectedDifficulties.splice(index, 1);
      } else {
        this.selectedDifficulties.push(difficultyId);
      }
    },
    
    selectAnswerMode(modeId) {
      this.selectedAnswerMode = modeId;
    },
    
    decreaseQuestionCount() {
      if (this.questionCount > 1) {
        this.questionCount--;
      }
    },
    
    increaseQuestionCount() {
      if (this.questionCount < this.maxQuestionCount) {
        this.questionCount++;
      }
    },
    
    toggleRandomOrder(e) {
      this.settings.randomOrder = e.detail.value;
    },
    
    toggleRandomOptions(e) {
      this.settings.randomOptions = e.detail.value;
    },
    
    validateSettings() {
      if (this.selectedMode === 'chapter' && this.selectedChapters.length === 0) {
        uni.showToast({
          title: '请选择至少一个章节',
          icon: 'none'
        });
        return false;
      }
      
      if (this.selectedMode === 'type' && this.selectedTypes.length === 0) {
        uni.showToast({
          title: '请选择至少一种题型',
          icon: 'none'
        });
        return false;
      }
      
      if (this.selectedDifficulties.length === 0) {
        uni.showToast({
          title: '请选择至少一个难度等级',
          icon: 'none'
        });
        return false;
      }
      
      if (this.availableQuestionCount === 0) {
        uni.showToast({
          title: '没有符合条件的题目',
          icon: 'none'
        });
        return false;
      }
      
      return true;
    },
    
    startPractice() {
      if (!this.validateSettings()) {
        return;
      }
      
      // 构建练习参数
      const practiceParams = {
        bankId: this.questionBankId,
        mode: this.selectedMode,
        chapters: this.selectedChapters,
        types: this.selectedTypes,
        difficulties: this.selectedDifficulties,
        count: this.availableQuestionCount,
        answerMode: this.selectedAnswerMode,
        randomOrder: this.settings.randomOrder,
        randomOptions: this.settings.randomOptions
      };
      
      // 跳转到练习页面
      const paramsStr = encodeURIComponent(JSON.stringify(practiceParams));
      uni.navigateTo({
        url: `/pages/practice/index?params=${paramsStr}`
      });
    }
  }
}
</script>

<style scoped>
/* 微信小程序兼容样式 */
.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.space-x-3 > * + * {
  margin-left: 0.75rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}

.gap-2 {
  gap: 0.5rem;
}
</style>