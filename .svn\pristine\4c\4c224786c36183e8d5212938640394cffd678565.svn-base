<template>
  <view class="min-h-screen bg-gray-50">
    
    <!-- 加载中 -->
    
      <loading-view :show="loading" />
    
    
    <!-- 表单内容 -->
    <view class="px-4 pt-2 pb-10">
      <question-form v-if="!loading" :bankId="bankId"  ref="questionForm" :isEdit="true" />  
    </view>

    <view class="h-11"></view>
    <!-- 底部保存按钮 -->
    <view class="fixed bottom-0 left-0 right-0 bg-white p-4 border-t border-gray-200">
      <button @click="saveQuestion" class="w-full h-auto py-3 text-sm bg-primary-500 text-white rounded-lg shadow-sm font-medium  ">
          保存题目
        </button>
    </view>
      <!--安全距离-->
  </view>
</template>

<script>


export default {
  data() {
    return {
      loading:false,
      bankId:null,
     
    }
  },
  
 
  
  onLoad(options) {
    this.id = options.id;
    this.loadQuestion();
  },

  methods: {
    loadQuestion(){
      this.loading = true;
      this.$reqGet('/front/edu-personal/question/detail',{id:this.id}).then(res=>{
        if(res.success){
          this.loading = false;
          this.bankId = res.data.bankId; 
          this.$nextTick(()=>{
            this.$refs.questionForm.updateFormValue(res.data);
          })
        }else{
          uni.showToast({
            title: res.errorMessage,
            icon: 'none'
          });
        }

      })
    },
      // 保存题目
    saveQuestion() {

this.$refs.questionForm.validateForm((formValues)=>{
  formValues.id = this.id;
  this.$reqPost('/front/edu-personal/question/edit',formValues,true,'保存中...').then(res=>{
  if(res.success){
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    });
    setTimeout(()=>{
      uni.navigateBack();
    },1000);
  }else{
    uni.showToast({
      title: res.errorMessage,
      icon: 'none'
    });
  }
})
})






},
    
 
  }
}
</script>

<style scoped>
</style>