<template>
  <view class="min-h-screen bg-gray-50">
    <!-- 搜索栏 -->
    <view class="bg-white px-4 py-3 border-b border-gray-100">
      <view class="relative">
        <text class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></text>
        <input 
          v-model="searchKeyword"
          placeholder="搜索题库名称"
          class="w-full pl-10 pr-4 py-3 bg-gray-50 rounded-lg text-sm"
          @confirm="handleSearch"
        />
      </view>
    </view>

    <!-- 筛选标签 -->
    <view class="bg-white px-4 py-3 border-b border-gray-100">
      <scroll-view scroll-x="true" class="whitespace-nowrap">
        <view class="flex space-x-2">
          <button 
            v-for="filter in filters" 
            :key="filter.id"
            @click="selectFilter(filter)"
            :class="[
              'px-3 py-1 rounded-full text-sm whitespace-nowrap',
              selectedFilter === filter.id 
                ? 'bg-primary-500 text-white' 
                : 'bg-gray-100 text-gray-600'
            ]"
          >
            {{ filter.name }}
          </button>
        </view>
      </scroll-view>
    </view>

    <!-- 题库列表 -->
    <view class="px-4 py-4">
      <view v-if="loading" class="text-center py-8">
        <text class="text-gray-500">加载中...</text>
      </view>
      
      <view v-else-if="questionBanks.length === 0" class="text-center py-12">
        <view class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <text class="fas fa-search text-gray-300 text-2xl"></text>
        </view>
        <text class="text-gray-500 text-sm block mb-2">暂无题库</text>
        <text class="text-gray-400 text-xs">试试调整搜索条件</text>
      </view>
      
      <view v-else class="space-y-3">
        <view 
          v-for="item in questionBanks" 
          :key="item.id"
          @click="goToQuestionBankDetail(item)"
          class="bg-white rounded-xl p-4 shadow-sm border border-gray-100"
        >
          <view class="flex items-start">
            <view class="w-12 h-12 rounded-lg mr-3 flex items-center justify-center bg-primary-100">
              <text class="fas fa-book text-primary-500 text-lg"></text>
            </view>
            <view class="flex-1">
              <view class="flex items-center justify-between mb-2">
                <text class="text-base font-medium text-gray-800">{{ item.name }}</text>
                <view v-if="item.needPassword" class="px-2 py-1 bg-yellow-100 rounded-full">
                  <text class="fas fa-lock text-yellow-600 text-xs"></text>
                </view>
              </view>
              
              <text class="text-sm text-gray-600 block mb-3">{{ item.description || '暂无描述' }}</text>
              
              <view class="flex items-center justify-between">
                <view class="flex items-center space-x-4">
                  <view class="flex items-center">
                    <text class="fas fa-question-circle text-gray-400 text-xs mr-1"></text>
                    <text class="text-xs text-gray-500">{{ item.questionCount }}题</text>
                  </view>
                  <view class="flex items-center">
                    <text class="fas fa-users text-gray-400 text-xs mr-1"></text>
                    <text class="text-xs text-gray-500">{{ item.memberCount }}人</text>
                  </view>
                  <view class="flex items-center">
                    <text class="fas fa-star text-gray-400 text-xs mr-1"></text>
                    <text class="text-xs text-gray-500">{{ item.rating || '暂无评分' }}</text>
                  </view>
                </view>
                
                <view class="flex items-center">
                  <text class="text-xs text-gray-400 mr-2">{{ item.updateTime }}</text>
                  <button 
                    v-if="!item.isJoined"
                    @click.stop="joinQuestionBank(item)"
                    class="px-3 py-1 bg-primary-500 text-white text-xs rounded-full"
                  >
                    加入
                  </button>
                  <view v-else class="px-3 py-1 bg-primary-100 text-primary-600 text-xs rounded-full">
                    已加入
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加入题库密码弹窗 -->
    <view v-if="showPasswordModal" class="fixed inset-0 z-50 flex items-center justify-center" @click="closePasswordModal">
      <view class="absolute inset-0 bg-black bg-opacity-50"></view>
      <view class="bg-white rounded-xl p-6 mx-4 relative z-10 w-80" @click.stop>
        <text class="text-lg font-bold text-gray-800 block mb-4">输入密码</text>
        <view class="mb-4">
          <text class="text-sm text-gray-600 block mb-2">题库"{{ selectedQuestionBank?.name }}"需要密码才能加入</text>
          <input 
            v-model="joinPassword"
            placeholder="请输入密码"
            class="w-full px-3 py-2 border border-gray-200 rounded-lg text-sm"
            password
          />
        </view>
        <view class="flex space-x-3">
          <button @click="closePasswordModal" class="flex-1 py-2 border border-gray-200 text-gray-600 rounded-lg text-sm">
            取消
          </button>
          <button @click="confirmJoinWithPassword" class="flex-1 py-2 bg-primary-500 text-white rounded-lg text-sm">
            确认
          </button>
        </view>
      </view>
    </view>

    <!-- 底部安全区域 -->
    <view class="h-20"></view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      searchKeyword: '',
      selectedFilter: 'all',
      loading: false,
      showPasswordModal: false,
      filters: [
        { id: 'all', name: '全部' },
        { id: 'my', name: '我的题库' },
        { id: 'joined', name: '已加入' },
        { id: 'public', name: '公开题库' },
        { id: 'hot', name: '热门推荐' }
      ],
      questionBanks: [
        {
          id: '1',
          name: '计算机基础知识',
          description: '涵盖计算机组成原理、操作系统、数据结构等基础知识',
          questionCount: 156,
          memberCount: 89,
          rating: '4.8',
          updateTime: '2天前',
          needPassword: false,
          isJoined: true,
          isOwner: true
        },
        {
          id: '2',
          name: 'JavaScript进阶',
          description: 'ES6+语法、异步编程、模块化等进阶内容',
          questionCount: 203,
          memberCount: 156,
          rating: '4.9',
          updateTime: '1周前',
          needPassword: true,
          isJoined: false,
          isOwner: false
        },
        {
          id: '3',
          name: 'Vue.js实战',
          description: 'Vue3组合式API、状态管理、路由等实战项目',
          questionCount: 178,
          memberCount: 234,
          rating: '4.7',
          updateTime: '3天前',
          needPassword: false,
          isJoined: true,
          isOwner: false
        },
        {
          id: '4',
          name: 'React核心原理',
          description: 'Hooks、虚拟DOM、状态管理等核心概念深度解析',
          questionCount: 145,
          memberCount: 198,
          rating: '4.6',
          updateTime: '5天前',
          needPassword: true,
          isJoined: false,
          isOwner: false
        }
      ],
      selectedQuestionBank: null,
      joinPassword: ''
    }
  },
  onLoad(options) {
    if (options.search) {
      this.searchKeyword = decodeURIComponent(options.search);
      this.handleSearch();
    } else {
      this.loadQuestionBanks();
    }
  },
  methods: {
    loadQuestionBanks() {
      this.loading = true;
      
      // 模拟API调用
      setTimeout(() => {
        this.loading = false;
        // 根据筛选条件过滤数据
        this.filterQuestionBanks();
      }, 500);
    },
    
    filterQuestionBanks() {
      // 这里应该根据selectedFilter和searchKeyword过滤数据
      // 实际项目中应该调用API
    },
    
    selectFilter(filter) {
      this.selectedFilter = filter.id;
      this.loadQuestionBanks();
    },
    
    handleSearch() {
      this.loadQuestionBanks();
    },
    
    goToQuestionBankDetail(item) {
      uni.navigateTo({
        url: `/pages/questionbank/detail?bankId=${item.id}`
      });
    },
    
    joinQuestionBank(item) {
      if (item.needPassword) {
        this.selectedQuestionBank = item;
        this.joinPassword = '';
        this.showPasswordModal = true;
      } else {
        this.confirmJoin(item);
      }
    },
    
    closePasswordModal() {
      this.showPasswordModal = false;
      this.selectedQuestionBank = null;
      this.joinPassword = '';
    },
    
    confirmJoinWithPassword() {
      if (!this.joinPassword.trim()) {
        uni.showToast({
          title: '请输入密码',
          icon: 'none'
        });
        return;
      }
      
      // 验证密码
      uni.showLoading({
        title: '验证中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        
        // 模拟密码验证
        if (this.joinPassword === '123456') {
          this.confirmJoin(this.selectedQuestionBank);
          this.closePasswordModal();
        } else {
          uni.showToast({
            title: '密码错误',
            icon: 'none'
          });
        }
      }, 1000);
    },
    
    confirmJoin(item) {
      uni.showLoading({
        title: '加入中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        
        // 更新题库状态
        const index = this.questionBanks.findIndex(bank => bank.id === item.id);
        if (index !== -1) {
          this.questionBanks[index].isJoined = true;
          this.questionBanks[index].memberCount++;
        }
        
        uni.showToast({
          title: '加入成功',
          icon: 'success'
        });
      }, 1000);
    }
  }
}
</script>

<style scoped>
/* 微信小程序兼容样式 */
.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.space-x-3 > * + * {
  margin-left: 0.75rem;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}

.fixed {
  position: fixed;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.z-50 {
  z-index: 50;
}

.z-10 {
  z-index: 10;
}

.bg-opacity-50 {
  background-color: rgba(0, 0, 0, 0.5);
}

.whitespace-nowrap {
  white-space: nowrap;
}
</style>