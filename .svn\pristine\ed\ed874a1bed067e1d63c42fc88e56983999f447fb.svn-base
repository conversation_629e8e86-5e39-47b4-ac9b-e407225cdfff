<template>
  <view class="min-h-screen bg-gray-100">
    <!-- 空状态 -->
    <view
      v-if="dataList && !dataList.length"
      class="flex flex-col items-center justify-center py-20"
    >
      <view
        class="w-24 h-24 rounded-full bg-white shadow-sm flex items-center justify-center mb-4"
      >
        <text class="fas fa-folder-open text-gray-400 text-3xl"></text>
      </view>
      <text class="text-gray-600 text-lg font-medium mb-2">无章节</text>
      <text class="text-gray-500 text-sm">当前题库暂无章节</text>
    </view>

    <!-- 章节列表 -->
    <view v-else class="bg-white">
      <view v-for="(item, index) in dataList" :key="item.id">
        <!-- 父章节 -->
        <view
          class="px-4 py-3 border-b border-gray-100 hover:bg-gray-50"
          @click="toAnswer(item)"
        >
          <view class="flex items-center justify-between">
            <view class="flex-1">
              <text
                :class="[
                  'text-base font-medium',
                  !item.unlock ? 'text-gray-400' : 'text-gray-800',
                ]"
              >
                {{ item.name }}
              </text>
            </view>
            <view class="flex items-center">
              <text class="text-xs text-gray-500 mr-3">
                {{ item.totalQuestionNum || 0 }} 道题
              </text>
              <view class="w-6 flex justify-center">
                <template v-if="item.children && item.children.length">
                  <text
                    :class="[
                      'text-gray-400 text-lg',
                      openIds.includes(item.id)
                        ? 'fas fa-chevron-down'
                        : 'fas fa-chevron-right',
                    ]"
                    @click="toggleOpen(item.id)"
                  ></text>
                </template>
                <template v-else>
                  <text
                    v-if="!item.unlock"
                    class="fas fa-lock text-gray-400 text-lg"
                  ></text>
                  <text
                    v-else-if="item.leafNode"
                    class="fas fa-edit text-primary-500 text-lg"
                  ></text>
                </template>
              </view>
            </view>
          </view>
        </view>

        <!-- 子章节 -->
        <template v-if="openIds.includes(item.id)">
          <view
            v-for="childItem in item.children"
            :key="childItem.id"
            class="bg-gray-100"
          >
            <view
              class="pl-8 pr-4 py-3 border-b border-gray-100 hover:bg-gray-200"
              @click="toAnswer(childItem)"
            >
              <view class="flex items-center justify-between">
                <view class="flex-1">
                  <text
                    :class="[
                      'text-base font-medium',
                      !childItem.unlock ? 'text-gray-400' : 'text-gray-700',
                    ]"
                  >
                    {{ childItem.name }}
                  </text>
                </view>
                <view class="flex items-center">
                  <text class="text-xs text-gray-500 mr-3">
                    {{ childItem.totalQuestionNum || 0 }} 道题
                  </text>
                  <view class="w-6 flex justify-center">
                    <text
                      v-if="!childItem.unlock"
                      class="fas fa-lock text-gray-400 text-lg"
                    ></text>
                    <text
                      v-else
                      class="fas fa-edit text-primary-500 text-lg"
                    ></text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </template>
      </view>
    </view>

    <!-- 密码输入弹窗 -->
    <view
      v-if="showPasswordModal"
      class="fixed inset-0 z-50 flex items-center justify-center"
      @click="closePasswordModal"
    >
      <view class="absolute inset-0 bg-black bg-opacity-50"></view>
      <view
        class="bg-white rounded-3xl p-6 mx-6 relative z-10 w-full max-w-sm shadow-lg"
        @click.stop
      >
        <view class="flex items-center justify-between mb-6">
          <view class="flex items-center">
            <view
              class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center mr-3"
            >
              <text class="fas fa-lock text-primary-500"></text>
            </view>
            <text class="text-xl font-bold text-gray-800">输入密码</text>
          </view>
          <view>
            <button
              @click="closePasswordModal"
              class="w-8 h-8 rounded-full hover:bg-gray-100 flex items-center justify-center"
            >
              <text class="fas fa-times text-gray-400"></text>
            </button>
          </view>
        </view>

        <view class="space-y-4">
          <view>
            <text class="text-sm font-medium text-gray-700 block mb-2"
              >题库密码 <text class="text-red-500">*</text></text
            >
            <input
              v-model="passwordInput"
              type="password"
              placeholder="请输入题库密码"
              maxlength="20"
              class="h-auto p-3 border border-solid border-gray-300 rounded-xl text-sm bg-white focus:border-primary-500 hover:border-primary-500"
              @confirm="confirmPassword"
            />
          </view>
        </view>

        <button
          @click="confirmPassword"
          class="w-full mt-6 py-3.5 bg-primary-500 text-white rounded-xl text-sm font-medium"
        >
          <text class="fas fa-check mr-2"></text>
          确认加入
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      dataList: undefined,
      openIds: [],
      showPasswordModal: false,
      passwordInput: "",
    };
  },
  onLoad(options) {
    this.bankId = options.bankId;
  },
  onShow() {
    this.loadData();
  },
  methods: {
    loadData() {
      const bankId = this.bankId;
      this.$reqGet(`/front/edu/qbank/chapterList/${bankId}`).then((res) => {
        const { data, success } = res || {};
        if (success) {
          this.dataList = data.map((item) => ({
            ...item,
            open: false,
            cr: item.totalQuestionNum
              ? parseInt((item.doneNum / item.totalQuestionNum) * 1000 + "") /
                10
              : 0,
            accuracy: item.answerNum
              ? parseInt((item.answerCorrectNum / item.answerNum) * 1000 + "") /
                10
              : 0,
            children: item.children?.map((childItem) => ({
              ...childItem,
              cr: childItem.totalQuestionNum
                ? parseInt(
                    (childItem.doneNum / childItem.totalQuestionNum) * 1000 + ""
                  ) / 10
                : 0,
              accuracy: childItem.answerNum
                ? parseInt(
                    (childItem.answerCorrectNum / childItem.answerNum) * 1000 +
                      ""
                  ) / 10
                : 0,
            })),
          }));
        }
      });
    },
    toggleOpen(id) {
      if (this.openIds.includes(id)) {
        this.openIds = this.openIds.filter((item) => item !== id);
      } else {
        this.openIds.push(id);
      }
    },
    requestJoinBank(password = "") {
      // 请求加入题库接口
      const params = {
        id: this.bankId,
      };

      if (password) {
        params.pwd = password;
      }

      this.$reqPost("/front/edu-personal/joinBank", params)
        .then((res) => {
          if (res.success) {
            // 更新题库状态
            this.loadData();
            uni.showToast({
              title: "加入成功",
              icon: "success",
            });
          } else {
            uni.showToast({
              title: res.errorMessage || "加入失败",
              icon: "error",
            });
          }
        })
        .catch((err) => {
          uni.showToast({
            title: "网络错误，请重试",
            icon: "error",
          });
        });
    },
    toAnswer(item) {
      if (!item.leafNode) {
        this.toggleOpen(item.id);
        return;
      }

      if (!item.totalQuestionNum) {
        uni.showToast({
          title: "该章节题目准备中~",
          icon: "none",
        });
        return;
      }

      if (!item.unlock) {
        this.$reqGet("/front/edu-personal/bankPermission", {
          id: this.bankId,
        }).then((res) => {
          if (res.success) {
            if (res.data.isMember) {
              this.toAnswer(item);
              return;
            }
            if (res.data.needPassword) {
              this.showPasswordModal = true;
              this.passwordInput = "";
            } else {
              uni.showModal({
                title: "提示",
                content: "需加入可使用，确认将此题库加入我的题库吗？",
                success: (res) => {
                  if (res.confirm) {
                    this.requestJoinBank();
                  }
                },
              });
            }
          }
        });
        return;
      }

      uni.navigateTo({
        url: "./do?mode=chapter&chapterId=" + item.id,
      });
    },
  },
};
</script>

<style scoped></style>
