<template>
	<view class="min-h-screen bg-gray-50 px-4 py-6">
		<view class="mb-4">
			<text class="text-xl font-bold text-gray-800 block mb-3">题目导入方式</text>
			<text class="text-sm text-gray-500 mb-6">请选择一种导入方式，开始添加题目</text>
		</view>

		<!-- 导入方式卡片 -->
		<view class="space-y-4">
			<!-- 单题录入 -->
			<view class="bg-white rounded-xl shadow-sm overflow-hidden" @click="navigateToSingleAdd">
				<view class="flex items-center p-5">
					<view class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mr-4">
						<text class="fas fa-edit text-primary-500 text-xl"></text>
					</view>
					<view class="flex-1">
						<text class="text-lg font-medium text-gray-800 block">单题录入</text>
						<text class="text-sm text-gray-500">题目单个录入</text>
					</view>
					<text class="fas fa-chevron-right text-gray-300"></text>
				</view>
			</view>

			<!-- 批量导入 -->
			<view class="bg-white rounded-xl shadow-sm overflow-hidden" @click="navigateToBatchImport">
				<view class="flex items-center p-5">
					<view class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
						<text class="fas fa-paste text-blue-500 text-xl"></text>
					</view>
					<view class="flex-1">
						<text class="text-lg font-medium text-gray-800 block">批量导入</text>
						<text class="text-sm text-gray-500">将内容从文档复制粘贴到文本框，调整导入</text>
					</view>
					<text class="fas fa-chevron-right text-gray-300"></text>
				</view>
			</view>

			<!-- Excel导入 -->
			<view class="bg-white rounded-xl shadow-sm overflow-hidden" @click="navigateToExcelImport">
				<view class="flex items-center p-5">
					<view class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mr-4">
						<text class="fas fa-file-excel text-green-500 text-xl"></text>
					</view>
					<view class="flex-1">
						<text class="text-lg font-medium text-gray-800 block">Excel导入</text>
						<text class="text-sm text-gray-500">上传Excel文件，调整导入</text>
					</view>
					<text class="fas fa-chevron-right text-gray-300"></text>
				</view>
			</view>

			<!-- 人工导题 -->
			
			<view class="bg-white rounded-xl shadow-sm overflow-hidden"  >
				<button open-type="contact" class="text-sm share-button">
				<view class="flex items-center p-5">
					<view class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mr-4">
						<text class="fas fa-headset text-purple-500 text-xl"></text>
					</view>
					<view class="flex-1 text-left">
						<text class="text-lg font-medium text-gray-800 block text-left">人工导题</text>
						<text class="text-sm text-gray-500 text-left">联系客服专业团队录入，省时省力</text>
					</view>
					<text class="fas fa-chevron-right text-gray-300"></text>
				</view>
			</button>
			</view>
		
		 
		</view>

		<!-- 导入说明 -->
		<view class="mt-8 bg-white rounded-xl p-5 shadow-sm">
			<view class="flex items-center mb-3">
				<text class="fas fa-info-circle text-blue-500 mr-2"></text>
				<text class="text-base font-medium text-gray-800">导入说明</text>
			</view>
			<view class="space-y-2">
				<view class="flex">
					<text class="fas fa-check-circle text-green-500 mr-2"></text>
					<text class="text-sm text-gray-600">单题录入适合小量题目的精细添加，效率低</text>
				</view>
				<view class="flex">
					<text class="fas fa-check-circle text-green-500 mr-2"></text>
					<text class="text-sm text-gray-600">批量导入支持格式化文本，一次添加多道题，效率高</text>
				</view>
				<view class="flex">
					<text class="fas fa-check-circle text-green-500 mr-2"></text>
					<text class="text-sm text-gray-600">Excel导入需要按模板格式准备文件，效率高</text>
				</view>
				<view class="flex">
					<text class="fas fa-check-circle text-green-500 mr-2"></text>
					<text class="text-sm text-gray-600">人工导题需要联系客服，专业团队录入，省时省力</text>
				</view>
			 
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {

			}
		},
		onLoad(options) {
			this.bankId = options.bankId;
		},
		methods: {
			navigateToSingleAdd() {
				uni.navigateTo({
					url: `/pages/question/add?bankId=${this.bankId}`
				});
			},
			navigateToBatchImport() {
				uni.navigateTo({
					url: `/pages/question/textImp?bankId=${this.bankId}`
				});
			},
			navigateToExcelImport() {
				uni.navigateTo({
					url: `/pages/question/fileImp?bankId=${this.bankId}`
				});
			},
			contactCustomerService() {
				// 显示联系客服选项
				uni.showActionSheet({
					itemList: ['拨打客服电话', '复制微信号', '在线客服'],
					success: (res) => {
						switch(res.tapIndex) {
							case 0: // 拨打电话
								uni.makePhoneCall({
									phoneNumber: '************',
									fail: (err) => {
										uni.showToast({
											title: '拨号失败',
											icon: 'none'
										});
									}
								});
								break;
							case 1: // 复制微信号
								uni.setClipboardData({
									data: 'eduservice2023',
									success: () => {
										uni.showToast({
											title: '微信号已复制',
											icon: 'success'
										});
									}
								});
								break;
							case 2: // 在线客服
								// 这里可以跳转到在线客服页面
								uni.showToast({
									title: '正在连接客服系统',
									icon: 'none'
								});
								break;
						}
					}
				});
			}
		}

	}
</script>

<style scoped>
</style>