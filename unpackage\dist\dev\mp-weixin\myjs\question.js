"use strict";
const common_vendor = require("../common/vendor.js");
const questionFields = [
  {
    key: "questionContent",
    pattern: /^\d{1,}[.、:：。]/
  },
  {
    key: "questionContent",
    pattern: /^\(\d{1,}.\d{1,}\)/
  },
  {
    key: "analysis",
    pattern: /^解析[.、:：。]/
  },
  {
    key: "rightAnswer",
    pattern: /^答案[.、:：。]/
  },
  {
    key: "difficulty",
    pattern: /^难度[.、:：。]/
  },
  {
    key: "chapterNames",
    pattern: /^章节[.、:：。]/
  },
  {
    key: "options",
    pattern: /^[A-Z,a-z][.、:：。]/
  }
];
const yesNoRightAnswers = [
  ["正确", "是", "对", "T", "t"],
  ["错", "错误", "F", "f", "否"]
];
const difficulties = [
  ["低", "简单", "易"],
  ["中", "一般", "适中"],
  ["高", "困难", "难"]
];
const questionTypeEnum = [
  {
    label: "单选题",
    value: 1
  },
  {
    label: "多选题",
    value: 2
  },
  {
    label: "判断题",
    value: 3
  },
  {
    label: "填空题",
    value: 4,
    disabled: true
  },
  {
    label: "问答题",
    value: 5,
    disabled: true
  },
  {
    label: "组合题",
    value: 9
  }
];
const difficultyEnum = [
  {
    label: "简单",
    value: 1
  },
  {
    label: "一般",
    value: 2
  },
  {
    label: "困难",
    value: 3
  }
];
const optionPreEnum = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K"];
function textToQuestionList(content) {
  var _a, _b;
  const now = (/* @__PURE__ */ new Date()).getTime();
  let lastContentType = "";
  let lastOptionIndex = -1;
  let question = {
    questionContent: "",
    questionType: void 0,
    options: [],
    analysis: "",
    difficulty: 2,
    rightAnswers: [],
    _error: [],
    _index: 0,
    _rowIndex: 0,
    chapterFullName: ""
  };
  const questionList = [];
  let index = 0;
  const lineTexts = content.split("\n");
  common_vendor.index.__f__("log", "at myjs/question.js:117", lineTexts);
  let groupQuestion = void 0;
  for1:
    for (let i = 0; i < lineTexts.length; i++) {
      const text = lineTexts[i].trim();
      if (!text) {
        continue;
      }
      for (let k = 0; k < questionFields.length; k++) {
        const {
          key,
          pattern
        } = questionFields[k];
        if (pattern.test(text)) {
          if (k === 0) {
            lastContentType = "";
            question = {
              questionContent: "",
              questionType: 1,
              questionTypeText: "单选题",
              options: [],
              analysis: "",
              difficulty: 2,
              rightAnswers: [],
              _error: [],
              _index: index++,
              _rowIndex: i,
              chapterFullName: ""
            };
            groupQuestion = void 0;
            questionList.push(question);
          } else if (k === 1) {
            if (question && question.questionContent && (!!groupQuestion || !question.options.length && !question.rightAnswers.length && !question.analysis)) {
              if (!groupQuestion) {
                question.children = [];
                groupQuestion = question;
                groupQuestion.questionType = 9;
                groupQuestion.questionTypeText = "组合题";
              }
              question = {
                questionContent: "",
                questionType: 1,
                questionTypeText: "单选题",
                options: [],
                analysis: "",
                difficulty: 2,
                rightAnswers: [],
                _error: [],
                _index: ((_a = groupQuestion.children) == null ? void 0 : _a.length) || 0,
                _rowIndex: ((_b = groupQuestion.children) == null ? void 0 : _b.length) || 0,
                chapterFullName: ""
              };
              if (groupQuestion.children) {
                groupQuestion.children.push(question);
              }
            } else {
              continue;
            }
          }
          if (key === "options") {
            const splitOptionTexts = text.replaceAll(/\s[A-Z,a-z][.、:：。]/g, function(v) {
              return "_||_" + v;
            }).split("_||_");
            for (let i2 = 0; i2 < splitOptionTexts.length; i2++) {
              const optionText = splitOptionTexts[i2].trim();
              if (optionText) {
                const optionIndex = optionPreEnum.indexOf(
                  optionText.substring(0, 1).toUpperCase()
                );
                if (optionIndex >= 0) {
                  lastOptionIndex = optionIndex;
                  question.options[optionIndex] = optionText.substring(2);
                }
              }
            }
          } else if (key === "rightAnswer") {
            const newText = text.replace(pattern, "");
            if (!newText) {
              continue for1;
            }
            let yesNoIndex = -1;
            yesNoRightAnswers.forEach((yesNoItem, index2) => {
              if (yesNoItem.indexOf(newText) >= 0) {
                yesNoIndex = index2;
              }
            });
            if (yesNoIndex >= 0) {
              question.rightAnswers = [yesNoIndex + ""];
              question.questionType = 3;
              continue for1;
            }
            const rightAnswers = newText.replaceAll(",", "").replaceAll("，", "").replaceAll("、", "").replaceAll(" ", "").split("").map((optionItem) => {
              return optionPreEnum.indexOf(optionItem.toUpperCase()) + "";
            }).sort();
            question.rightAnswers = rightAnswers.filter((element, index2) => {
              return rightAnswers.indexOf(element) === index2;
            });
          } else if (key === "difficulty") {
            let difficulty = -1;
            const newText = text.replace(pattern, "");
            difficulties.forEach((t, index2) => {
              if (t.indexOf(newText) >= 0) {
                difficulty = index2;
              }
            });
            if (difficulty >= 0) {
              question.difficulty = difficulty + 1;
            }
          } else if (key === "chapterNames") {
            if (!groupQuestion) {
              const newText = text.replace(pattern, "");
              question.chapterFullName = newText.split("/").map((item) => item.trim()).filter((item) => !!item).join(" / ");
            }
          } else {
            const newText = text.replace(pattern, "");
            question[key] = newText;
          }
          lastContentType = key;
          continue for1;
        }
      }
      if (lastContentType) {
        if (lastContentType === "options") {
          if (lastOptionIndex >= 0) {
            question.options[lastOptionIndex] = (question.options[lastOptionIndex] || "") + "<br>" + text;
          }
        } else {
          question[lastContentType] = question[lastContentType] + "<br>" + text;
        }
      }
    }
  function checkError(questionList2) {
    questionList2.forEach((item) => {
      var _a2, _b2, _c;
      item.difficultyText = (_a2 = difficultyEnum.find(
        (difficultyEnumItem) => difficultyEnumItem.value === item.difficulty
      )) == null ? void 0 : _a2.label;
      if (item.questionType === 9) {
        if (item.children && item.children.length) {
          checkError(item.children);
        } else {
          item._error.push("子题目不能为空");
        }
      } else if (item.questionType !== 3) {
        if (item.options.length < 2) {
          item._error.push("选项不能少于2个");
          return;
        }
        if (!item.rightAnswers.length) {
          item._error.push("答案不能为空");
          return;
        }
        item.questionType = item.rightAnswers.length === 1 ? 1 : 2;
        item.questionTypeText = (_b2 = questionTypeEnum.find(
          (questionType) => questionType.value === item.questionType
        )) == null ? void 0 : _b2.label;
        const options = [...item.options];
        if (options.filter((option) => option === void 0).length) {
          item._error.push("选项标识必须是连续的（A-J）");
          return;
        }
        if (options.filter((option) => option === "").length) {
          item._error.push("选项内容不允许为空");
          return;
        }
        if (item.rightAnswers.find((index2) => !options[parseInt(index2)])) {
          item._error.push("答案与选项不匹配");
          return;
        }
        if (item.chapterFullName && item.chapterFullName.split(" / ").length > 2) {
          item._error.push("章节最多只能为2级");
          return;
        }
      } else {
        item.questionTypeText = (_c = questionTypeEnum.find(
          (questionType) => questionType.value === item.questionType
        )) == null ? void 0 : _c.label;
      }
    });
  }
  checkError(questionList);
  common_vendor.index.__f__("log", "at myjs/question.js:380", "end", (/* @__PURE__ */ new Date()).getTime() - now);
  return questionList;
}
exports.questionTypeEnum = questionTypeEnum;
exports.textToQuestionList = textToQuestionList;
//# sourceMappingURL=../../.sourcemap/mp-weixin/myjs/question.js.map
