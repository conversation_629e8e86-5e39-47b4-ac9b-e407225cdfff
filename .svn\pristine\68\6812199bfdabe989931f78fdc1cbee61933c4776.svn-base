# 智慧题库微信小程序 - 项目架构文档

## 项目概述

**项目名称**: 导刷题  
**技术栈**: uniapp (Vue3 Options API) + TailwindCSS 3.4.17 + Font Awesome 6.5.1  
**平台支持**: 微信小程序 + H5  
**设计理念**: 现代化UI设计，优秀的用户体验，跨平台兼容

## 功能模块设计

### 🏠 首页模块
**设计特点**:
- 顶部搜索题库
- 1级快速操作网格（上传题库（弹出窗（不是跳页面）输入题库名称-必填、描述）、考试中心（跳到考试列表）、我的群组）
- 我的题库（我加入的题库列表）
- 底部TabBar导航

**核心功能**:
- 快速跳转到各功能模块
- 我的题库列表可点击右边三个点图标展开弹出操作（置顶/取消置顶-点击直接置顶/取消置顶、(题库拥有者：修改题库基本信息（点击弹出窗修改题库名称、描述）、权限设置-点击跳到题库权限设置页面)、章节管理（点击跳到章节管理页面）、添加题目（跳到添加题目页面）、编辑题目（跳到题目列表页面）、退出题库（点击弹出确认框））






### 📚 题库模块

#### 题库权限设置
- 允许题库市场搜索
- 密码加入（当是时，显示密码管理）
- 密码管理（显示三个选项卡（全部、一次性密码、 固定密码）） 可点击切换
  - 全部：显示所有密码
  - 一次性密码：显示一次性密码
  - 固定密码：显示固定密码
    密码列表可删除
    可点击生成密码：弹出窗（不是跳页面）
      密码类型（一次性密码、固定密码）
      设置密码：输入6-32位的数字或者字符串，不输入时系统默认生成6位随机密码
      提交：点击提交按钮，将密码添加到密码列表中
    密码列表可点击复制密码




#### 章节管理
**功能特性**:
- 章节和子章节管理
- 添加、编辑、删除章节
- 章节排序（上移、下移）
- 章节重命名

#### 题目列表
**功能特性**:
- 显示当前题库下所有的题目
- 可按题干、章节、题型搜索
- 可对当前题库下的题目进行操作（编辑、删除）
**列表显示**:
- 题干
- 题型（题型 1、单选题 2、多选题 3、判断题  9、组合题）

#### 添加题目
**功能特性**:
- 单题录入（点击跳到单题录入/编辑页面）
- Excel导入（点击跳到一个excel上传界面）
- 文本导入 (点击跳到一个文本录入textarea和上传txt的界面)

#### 添加题目
**功能特性**:
- 单题录入（点击跳到单题录入/编辑页面）
- Excel导入（点击跳到一个excel上传界面）
- 文本导入 (点击跳到一个文本录入textarea和上传txt的界面)




### 📝 考试管理模块

#### 考试列表 (pages/exam/index.vue)
**功能特性**:
- 我的考试/考试记录标签页
- 考试状态管理（未开始、进行中、已结束）
- 考试信息展示（名称、时间、参与人数）
- 编辑、删除、分享操作

#### 考试创建 (pages/exam/create.vue)
**表单字段**:
- 考试名称
- 考试说明
- 考生设置：
  - 指定考生（姓名+准考证、姓名+手机号、姓名+手机号+验证码、姓名+身份证号、姓名+工号、姓名+学号）
  - 指定群组
  - 所有人都可考试
- 及格分数
- 开始时间-结束时间
- 考试时长
- 设置考题：
  - 选择题库
  - 随机选题（按题型或难度）
  - 章节选题
  - 顺序选题
  - 手动选题
- 考试频率（不限、单次、每日一次）
- 考试封面
- 答题后显示答案
- 选项乱序
- 试题乱序
- 交卷后显示分数
- 成绩查询设置
- 多选题得分规则
- 切屏限制
- 交卷次数

#### 学生考试 (pages/exam/student.vue)
**功能特性**:
- 考试码输入
- 考生信息验证
- 考试须知展示
- 最近考试记录
- 考试详情弹窗

### 🎯 练习模块 (pages/practice/index.vue)
**练习模式**:
- 顺序练习（按顺序依次练习）
- 随机练习（随机抽取题目）
- 题型练习（按题型练习）
- 模拟考试（设置出题规则）
- 章节练习（按章节练习）

**功能特性**:
- 题库选择
- 练习设置（随机出题、显示答案、计时练习、错题重做）
- 练习历史记录
- 练习统计

### 👥 群组模块

#### 群组列表 (pages/group/index.vue)
**功能特性**:
- 我的群组/群组市场标签页
- 群组搜索
- 群组信息展示（名称、描述、成员数、题库数）
- 加入群组功能

#### 群组创建 (pages/group/create.vue)
**表单字段**:
- 群名称
- 群头像
- 群介绍
- 群公告
- 加入方式：
  - 不限制
  - 审核后加入
  - 口令（6位数数字口令）
- 关联题库

**功能特性**:
- 创建后生成群号
- 成员审核功能
- 解散群组
- 分享群组

### 👤 个人中心 (pages/profile/index.vue)
**功能特性**:
- 用户信息展示
- 学习统计（学习时长、正确率、完成题目数）
- 功能菜单（设置、帮助、关于）
- 退出登录

## 页面路由设计

```
pages/
├── login/login.vue              # 登录页面
├── index/index.vue              # 首页（TabBar）
├── questionbank/
│   ├── index.vue               # 题库列表
│   ├── create.vue              # 题库创建/编辑
│   ├── chapters.vue            # 章节管理
│   └── questions.vue           # 试题管理
├── exam/
│   ├── index.vue               # 考试列表
│   ├── create.vue              # 考试创建
│   └── student.vue             # 学生考试
├── practice/
│   ├── index.vue               # 练习首页
│   ├── take.vue                # 练习答题
│   └── result.vue              # 练习结果
├── group/
│   ├── index.vue               # 群组列表
│   ├── create.vue              # 群组创建
│   └── detail.vue              # 群组详情
└── profile/index.vue           # 个人中心
```

## TabBar设计

```javascript
// pages.json 配置
{
  "tabBar": {
    "color": "#999999",
    "selectedColor": "#3b82f6",
    "backgroundColor": "#ffffff",
    "borderStyle": "black",
    "list": [
      {
        "pagePath": "pages/index/index",
        "text": "首页",
        "iconPath": "static/tabbar/home.png",
        "selectedIconPath": "static/tabbar/home-active.png"
      },
      {
        "pagePath": "pages/questionbank/index",
        "text": "题库",
        "iconPath": "static/tabbar/questionbank.png",
        "selectedIconPath": "static/tabbar/questionbank-active.png"
      },
      {
        "pagePath": "pages/exam/index",
        "text": "考试",
        "iconPath": "static/tabbar/exam.png",
        "selectedIconPath": "static/tabbar/exam-active.png"
      },
      {
        "pagePath": "pages/practice/index",
        "text": "练习",
        "iconPath": "static/tabbar/practice.png",
        "selectedIconPath": "static/tabbar/practice-active.png"
      },
      {
        "pagePath": "pages/group/index",
        "text": "群组",
        "iconPath": "static/tabbar/group.png",
        "selectedIconPath": "static/tabbar/group-active.png"
      }
    ]
  }
}
```

## 设计系统

### 颜色规范
```css
/* 主色调 */
--primary-blue: #3b82f6;
--primary-purple: #8b5cf6;
--primary-green: #10b981;
--primary-orange: #f59e0b;
--primary-red: #ef4444;

/* 中性色 */
--gray-50: #f9fafb;
--gray-100: #f3f4f6;
--gray-200: #e5e7eb;
--gray-300: #d1d5db;
--gray-400: #9ca3af;
--gray-500: #6b7280;
--gray-600: #4b5563;
--gray-700: #374151;
--gray-800: #1f2937;
--gray-900: #111827;
```

### 字体规范
```css
/* 标题 */
--text-xl: 1.25rem; /* 20px */
--text-lg: 1.125rem; /* 18px */
--text-base: 1rem; /* 16px */
--text-sm: 0.875rem; /* 14px */
--text-xs: 0.75rem; /* 12px */

/* 字重 */
--font-bold: 700;
--font-semibold: 600;
--font-medium: 500;
--font-normal: 400;
```

### 间距规范
```css
/* 间距 */
--spacing-1: 0.25rem; /* 4px */
--spacing-2: 0.5rem; /* 8px */
--spacing-3: 0.75rem; /* 12px */
--spacing-4: 1rem; /* 16px */
--spacing-5: 1.25rem; /* 20px */
--spacing-6: 1.5rem; /* 24px */
--spacing-8: 2rem; /* 32px */
```

### 圆角规范
```css
/* 圆角 */
--rounded-sm: 0.125rem; /* 2px */
--rounded: 0.25rem; /* 4px */
--rounded-md: 0.375rem; /* 6px */
--rounded-lg: 0.5rem; /* 8px */
--rounded-xl: 0.75rem; /* 12px */
--rounded-2xl: 1rem; /* 16px */
--rounded-3xl: 1.5rem; /* 24px */
--rounded-full: 9999px;
```

## 组件设计

### 通用组件
- **Modal组件**: 自定义模态框，支持中心弹出和底部滑出
- **Button组件**: 统一按钮样式，支持多种状态
- **Input组件**: 统一输入框样式，支持验证
- **Card组件**: 统一卡片样式
- **Tag组件**: 统一标签样式

### 业务组件
- **QuestionCard**: 题目卡片组件
- **ExamCard**: 考试卡片组件
- **GroupCard**: 群组卡片组件
- **ProgressBar**: 进度条组件
- **StatisticsCard**: 统计卡片组件

## 状态管理

### 用户状态
```javascript
// 用户信息
userInfo: {
  id: string,
  name: string,
  phone: string,
  avatar: string,
  role: 'teacher' | 'student' | 'admin'
}

// 用户权限
permissions: {
  canCreateQuestionBank: boolean,
  canCreateExam: boolean,
  canManageGroup: boolean
}
```

### 应用状态
```javascript
// 全局状态
appState: {
  isLoading: boolean,
  networkStatus: 'online' | 'offline',
  currentTab: string
}
```

## 数据存储

### 本地存储
```javascript
// 用户信息
uni.setStorageSync('userInfo', userInfo);

// 登录状态
uni.setStorageSync('isLoggedIn', true);

// 用户设置
uni.setStorageSync('userSettings', settings);

// 缓存数据
uni.setStorageSync('cacheData', data);
```

### 云端数据
- 用户信息管理
- 题库数据管理
- 考试数据管理
- 群组数据管理
- 练习记录管理

## 性能优化

### 图片优化
- 使用WebP格式
- 图片懒加载
- 图片压缩

### 代码优化
- 组件按需加载
- 路由懒加载
- 数据缓存

### 用户体验优化
- 骨架屏加载
- 下拉刷新
- 上拉加载更多
- 页面切换动画

## 安全考虑

### 数据安全
- 敏感数据加密存储
- 网络请求HTTPS
- 数据验证和过滤

### 权限控制
- 角色权限管理
- 功能权限控制
- 数据访问权限

## 测试策略

### 功能测试
- 各模块功能完整性测试
- 用户交互流程测试
- 数据操作测试

### 兼容性测试
- 微信小程序兼容性
- H5浏览器兼容性
- 不同设备适配测试

### 性能测试
- 页面加载速度测试
- 内存使用测试
- 网络请求性能测试

## 部署方案

### 开发环境
- 本地开发服务器
- 热重载
- 调试工具

### 生产环境
- 微信小程序发布
- H5部署
- CDN加速

## 维护计划

### 版本管理
- 语义化版本号
- 更新日志维护
- 向后兼容性

### 监控告警
- 错误监控
- 性能监控
- 用户行为分析

### 定期维护
- 代码重构
- 性能优化
- 安全更新

---

这个架构文档为智慧题库微信小程序提供了完整的设计指导，确保项目的可维护性、可扩展性和用户体验的卓越性。 