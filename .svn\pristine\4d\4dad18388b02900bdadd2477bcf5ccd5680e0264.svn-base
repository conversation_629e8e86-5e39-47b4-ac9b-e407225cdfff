<template>
	<view class="h-screen bg-gray-50 flex flex-col">
		<question-answer 
			class="flex-1" 
			v-if="!loading" 
			:hideFooter="hideFooter" 
			:title="name" 
			:questionList="questionList" 
			:readonly="true" 
			:defaultIndex="index" 
		/>
		
		<!-- 加载状态 -->
		<loading-view :show="loading"></loading-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				name: '',
				questionList: [],
				isExam: false,
				id: 0,
				index: 0,
				loading: true,
				hideFooter: false
			}
		},
		onLoad(options) {
			const {
				answerId,
				questionId,
				collected
			} = options;

			if (answerId) {
				this.$reqGet(`/front/edu/user-answer/result`, {
						
							id: answerId
						
					})
					.then((res) => {
						const {
							data,
							success
						} = res || {};
						if (success) {
							this.questionList = data.questionList;
							this.name = data.name;
							this.index = 0;
							this.loading = false;
						} else {
							uni.showToast({
								title: '加载失败',
								icon: 'none'
							});
						}
					})
					.catch(() => {
						uni.showToast({
							title: '网络错误',
							icon: 'none'
						});
					});
			} else if (questionId) {
				this
					.$reqGet(`/front/edu/user-answer/questionInfo`, {
						
							questionId
						
					})
					.then((res) => {
						const {
							data,
							success
						} = res || {};
						if (success) {
							if (collected) {
								data.collected = true;
							}
							this.questionList = [data];
							this.hideFooter = true;
							this.loading = false;
						} else {
							uni.showToast({
								title: '加载失败',
								icon: 'none'
							});
						}
					})
					.catch(() => {
						uni.showToast({
							title: '网络错误',
							icon: 'none'
						});
					});
			} else {
				const {
					questionList,
					index,
					name
				} = getApp().globalData.analysisPageData;
			
				this.questionList = questionList;
				this.name = name;
				this.index = index;
 
				this.loading = false;
			}
		},
		methods: {

		}
	}
</script>

<style scoped>
</style>