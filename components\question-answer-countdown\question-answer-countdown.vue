<template>
  <view class="flex items-center justify-center">
    <view class="bg-white rounded-lg px-3 py-1.5 shadow-sm flex items-center">
      <!-- 暂停/继续按钮（非真实考试才显示） -->
      <view v-if="!isRealExam" class="mr-2" @click="stop">
        <view
          class="w-5 h-5 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200 transition-colors"
        >
          <i
            :class="[
              isStop ? 'fas fa-play' : 'fas fa-pause',
              'text-gray-600',
              'text-xs',
            ]"
          ></i>
        </view>
      </view>
      <!-- 倒计时 -->
      <text class="font-mono text-sm font-semibold text-gray-800 select-none">
        {{ m }}:{{ s }}
      </text>
    </view>
  </view>
</template>

<script>
export default {
  name: "countdown",
  props: {
    remainingTime: Number,
    isRealExam: <PERSON>olean,
    stopContent: String,
    baseSpentDuration: Number,
    answerId: String,
  },
  watch: {
    remainingTime: {
      handler(nv) {
        if (nv) {
          this.endTime = new Date().getTime() + nv * 1000;
          this.handleCountDown();
          this.startUpdateSpentTimer();
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      //剩余秒数
      leftSecond: 0,
      //结束时间
      endTime: 0,
      m: "00",
      s: "00",
      isStop: false,
    };
  },
  unmounted() {
    clearTimeout(this.outTimer);
    this.clearUpdateSpentTimer();
    this.updateSpentTime();
  },
  methods: {
    stop() {
      if (this.isRealExam) {
        return;
      }
      this.isStop = true;

      clearTimeout(this.outTimer);
      this.clearUpdateSpentTimer();
      this.updateSpentTime();
      uni.showModal({
        showCancel: false,
        title: "暂停中",
        confirmText: "继续做题",
        content: this.stopContent,
        success: (res) => {
          this.isStop = false;
          this.endTime = this.leftSecond * 1000 + new Date().getTime();
          this.handleCountDown();
          this.startUpdateSpentTimer();
        },
      });
    },
    handleCountDown() {
      const leftTime = this.endTime - new Date();
      this.leftSecond = parseInt(leftTime / 1000);
      if (leftTime > 0) {
        var minutes = parseInt(leftTime / 1000 / 60);
        var seconds = parseInt((leftTime % (1000 * 60)) / 1000);
        if (minutes < 10) {
          minutes = "0" + minutes;
        }
        if (seconds < 10) {
          seconds = "0" + seconds;
        }
        this.m = minutes;
        this.s = seconds;
        this.outTimer = setTimeout(() => {
          this.handleCountDown();
        }, 1000);
      } else {
        clearTimeout(this.outTimer);
        this.m = "00";
        this.s = "00";
        this.$emit("end");
      }
    },
    clearUpdateSpentTimer() {
      //清除更新已用时间定时器
      this.updateSpentTimer && clearInterval(this.updateSpentTimer);
    },
    startUpdateSpentTimer() {
      if (this.isRealExam) {
        return;
      }

      //开启更新已用时间定时器
      this.clearUpdateSpentTimer();
      this.updateSpentTimer = setInterval(() => {
        this.updateSpentTime();
      }, 3000);
    },
    updateSpentTime() {
      const { answerId, baseSpentDuration, remainingTime, leftSecond } = this;
      if (answerId && remainingTime >= 0 && baseSpentDuration >= 0) {
        const useDuration = remainingTime - leftSecond;

        this.$reqPost("/front/edu/user-answer/updateTime", {
          id: this.answerId,
          spentDuration:
            baseSpentDuration + (useDuration < 0 ? 0 : useDuration),
        }).then((res) => {
          const { success } = res || {};
          if (!success) {
            // console.log("更新时间失败");
          }
        });
      }
    },
  },
};
</script>

<style scoped>
/* 保持极简风格，无需额外样式 */
</style>
