<template>
  <view class="min-h-screen bg-gray-50 flex flex-col">
   
    <view class="flex-1 p-4 pb-20">
      <!-- 大型文本输入区 - 美化版 -->
      <view class="mb-4">
        <view class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
          <view class="px-4 py-3.5 bg-gradient-to-r from-primary-50 to-blue-50 flex items-center justify-between">
            <text class="text-lg font-bold text-gray-700">请粘贴题目文本</text>
            <view class="bg-white h-8 w-8 rounded-full flex items-center justify-center shadow-sm">
              <text class="fas fa-paste text-primary-500"></text>
            </view>
          </view>
          <textarea
            v-model="importText"
            class="w-full p-4 min-h-[400rpx] text-sm text-gray-800 border-0"
            placeholder="请将题目文本粘贴到此处"
            maxlength="20000"
          ></textarea>
        </view>
      </view>
      
      <!-- 导入说明 - 美化版 -->
      <view class="bg-white rounded-lg shadow-sm p-4 mb-4 border border-gray-200">
        <view class="flex items-center mb-3">
          <view class="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center mr-2">
            <text class="fas fa-info-circle text-primary-500"></text>
          </view>
          <text class="text-base font-medium text-gray-800">文本导入说明</text>
        </view>
        
        <view class="space-y-2.5 text-sm text-gray-600 pl-2">
          <view class="flex items-start bg-gray-50 p-2.5 rounded-lg">
            <text class="fas fa-check text-primary-500 mr-2 mt-1"></text>
            <text>系统支持自动解析常见题型，包括单选题、多选题、判断题等</text>
          </view>
          <view class="flex items-start bg-gray-50 p-2.5 rounded-lg">
            <text class="fas fa-check text-primary-500 mr-2 mt-1"></text>
            <text>每道题应以序号（如"1."、"1、"）开头</text>
          </view>
          <view class="flex items-start bg-gray-50 p-2.5 rounded-lg">
            <text class="fas fa-check text-primary-500 mr-2 mt-1"></text>
            <text>选项应以字母（如"A."、"B、"）开头</text>
          </view>
          <view class="flex items-start bg-gray-50 p-2.5 rounded-lg">
            <text class="fas fa-check text-primary-500 mr-2 mt-1"></text>
            <text>答案格式示例："答案：A"、"答案：A,B,C"或"答案：正确/错误"</text>
          </view>
          <view class="flex items-start bg-gray-50 p-2.5 rounded-lg">
            <text class="fas fa-check text-primary-500 mr-2 mt-1"></text>
            <text>解析格式示例："解析：..."</text>
          </view>
          <view class="flex items-start bg-gray-50 p-2.5 rounded-lg">
            <text class="fas fa-check text-primary-500 mr-2 mt-1"></text>
            <text>章节格式示例："章节：第一章/第一节"，最多支持2级，2级请以"/"分割</text>
          </view>
          <view class="flex items-start bg-gray-50 p-2.5 rounded-lg">
            <text class="fas fa-check text-primary-500 mr-2 mt-1"></text>
            <text>难度格式示例："难度：简单/一般/困难"或"难度：低/中/高"或"难度：易/适中/难"</text>
          </view>
        </view>
        
        <!-- 示例展开/收起 - 美化版 -->
        <view class="mt-4 border-t border-gray-100 pt-3">
          <view @click="toggleExample" class="flex items-center text-primary-500 py-1">
            <text class="text-sm mr-1.5">{{ showExample ? '收起示例' : '查看示例' }}</text>
            <view class="h-5 w-5 rounded-full bg-primary-100 flex items-center justify-center transition-transform duration-300" :class="{'transform rotate-180': showExample}">
              <text :class="['fas fa-chevron-down text-xs text-primary-500']"></text>
            </view>
          </view>
          
          <view v-if="showExample" class="mt-3 bg-gray-50 p-4 rounded-lg text-xs text-gray-700 whitespace-pre-line border border-gray-200 shadow-inner">
{{ exampleText }}
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="fixed bottom-0 left-0 right-0 bg-white border-t px-4 py-3 border-gray-100   ">
      
      <button @click="previewQuestions" :class="[
        ' text-sm py-3 rounded-lg ',
        'bg-primary-500 text-white'
      ]">
        <text class="fas fa-eye mr-2"></text>
        <text>预览题目</text>
      </button>
    </view>
  </view>
</template>

<script>


const questionFields = [
  {
    key: 'questionContent',
    pattern: /^\d{1,}[.、:：。]/,
  },

  {
    key: 'questionContent',
    pattern: /^\(\d{1,}.\d{1,}\)/,
  },

  {
    key: 'analysis',
    pattern: /^解析[.、:：。]/,
  },
  {
    key: 'rightAnswer',
    pattern: /^答案[.、:：。]/,
  },

  {
    key: 'difficulty',
    pattern: /^难度[.、:：。]/,
  },

  {
    key: 'chapterNames',
    pattern: /^章节[.、:：。]/,
  },

  {
    key: 'options',
    pattern: /^[A-Z,a-z][.、:：。]/,
  },
];

const yesNoRightAnswers = [
  ['正确', '是', '对', 'T', 't'],
  ['错', '错误', 'F', 'f', '否'],
];

const difficulties = [
  ['低', '简单', '易'],
  ['中', '一般', '适中'],
  ['高', '困难', '难'],
];


const questionTypeEnum = [
  { label: '单选题', value: 1 },
  { label: '多选题', value: 2 },
  { label: '判断题', value: 3 },
  { label: '填空题', value: 4, disabled: true },
  { label: '问答题', value: 5, disabled: true },
  { label: '组合题', value: 9 },
];

const difficultyEnum = [
  { label: '简单', value: 1 },
  { label: '一般', value: 2 },
  { label: '困难', value: 3 },
];

const optionPreEnum = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K'];

 import { textToQuestionList } from '@/myjs/question';


export default {
  data() {
    return {
      importText: '',
      showExample: false,
      parsedQuestions: [],
      exampleText: `1.（  ）是我国最早的诗歌总集，又称作"诗三百"。
A.《左传》    
B.《离骚》
C.《坛经》
D.《诗经》
答案：D  
解析：诗经是我国最早的诗歌总集。
难度：中
章节：第一章


2.中华人民共和国的成立，标志着（　）。
A.中国新民主主义革命取得了基本胜利
B.中国现代史的开始
C.半殖民地半封建社会的结束
D.中国进入社会主义社会
答案：ABC
解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。从中华人民共和国成立到社会主义改造基本完成，是我国从新民主主义到社会主义过渡的时期。这一时期，我国社会的性质是新民主主义社会。
章节：第二章/第一节


3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。
答案：错误 
解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。《赵氏孤儿》非常典型地反映了中国悲剧那种前赴后继、不屈不饶地同邪恶势力斗争到底的抗争精神。`
    };
  },
  onLoad(options) {
    this.bankId = options.bankId;
  },
  methods: {
    toggleExample() {
      this.showExample = !this.showExample;
    },
    
    parseText() {
      // 这里是解析文本的逻辑，实际项目中应该有更复杂的实现
      if (!this.importText.trim()) {
        uni.showToast({
          title: '请输入题目文本',
          icon: 'none'
        });
        return;
      }
      
      return textToQuestionList(this.importText)
  
    },
    
    clearParsed() {
      this.parsedQuestions = [];
    },
    
    previewQuestions() {

        this.importText = this.importText.trim();
        if(!this.importText){
          uni.showToast({
            title: '请在上方粘贴题目文本',
            icon: 'none'
          });
          return;
        }

        const questionList = this.parseText();
      if (questionList.length === 0) {
        uni.showToast({
          title: '未解析出题目，请检查文本格式',
          icon: 'none'
        });
        return;
      }


      
      // 将解析结果存储到全局或缓存中
      getApp().globalData = getApp().globalData || {};
      getApp().globalData.parsedQuestions = questionList;
      
      // 跳转到预览页面
      uni.navigateTo({
        url: '/pages/question/impPreview?bankId='+this.bankId
      });
    },
    

  }
};
</script>

<style>
/* 添加一些过渡效果 */
.transition-transform {
  transition: transform 0.3s ease;
}
</style>
