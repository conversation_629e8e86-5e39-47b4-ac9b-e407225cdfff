<template>
	<view class="agreement-container">
		<view class="header">
			<text class="title">用户服务协议</text>
		</view>
		
		<scroll-view class="content" scroll-y="true">
			<view class="section">
				<text class="section-title">1. 服务条款的确认和接纳</text>
				<text class="section-content">
					欢迎使用导刷题！本协议是您与导刷题之间关于您使用导刷题服务所订立的协议。
					请您仔细阅读本协议，您点击"同意"、"下一步"或您的注册、使用等行为或者以其他任何明示或者默示方式表示接受本协议的，
					即视为您已阅读并同意本协议的约束。
				</text>
			</view>
			
			<view class="section">
				<text class="section-title">2. 服务内容</text>
				<text class="section-content">
					导刷题为用户提供在线题库练习、学习资料、考试模拟等教育服务。
					具体服务内容以导刷题实际提供的为准。
				</text>
			</view>
			
			<view class="section">
				<text class="section-title">3. 用户账号</text>
				<text class="section-content">
					用户有义务保证密码和账号的安全，用户利用该密码和账号所进行的一切活动引起的任何损失或损害，
					由用户自行承担全部责任，导刷题不承担任何责任。
				</text>
			</view>
			
			<view class="section">
				<text class="section-title">4. 使用规则</text>
				<text class="section-content">
					用户在使用导刷题服务时，必须遵守中华人民共和国相关法律法规，不得利用导刷题服务进行任何违法或不当行为。
				</text>
			</view>
			
			<view class="section">
				<text class="section-title">5. 隐私保护</text>
				<text class="section-content">
					导刷题非常重视用户个人信息的保护。详细的隐私保护政策请参见《隐私政策》。
				</text>
			</view>
			
			<view class="section">
				<text class="section-title">6. 免责声明</text>
				<text class="section-content">
					导刷题对服务内容的准确性、完整性、可靠性不作任何明示或暗示的保证。
					用户理解并同意，任何通过导刷题服务取得的信息资料的准确性、完整性和可靠性需要您独立判断。
				</text>
			</view>
			
			<view class="section">
				<text class="section-title">7. 协议修改</text>
				<text class="section-content">
					导刷题有权随时修改本协议的任何条款，一旦本协议的内容发生变动，
					导刷题将会通过适当方式向用户提示修改内容。
				</text>
			</view>
			
			<view class="section">
				<text class="section-title">8. 联系我们</text>
				<text class="section-content">
					如果您对本协议有任何疑问，请通过应用内客服功能联系我们。
				</text>
			</view>
		</scroll-view>
		
		<view class="footer">
			<button class="confirm-btn" @click="goBack">我已阅读并同意</button>
		</view>
	</view>
</template>

<script>
export default {
	name: 'UserAgreement',
	methods: {
		goBack() {
			uni.navigateBack();
		}
	}
}
</script>

<style scoped>
.agreement-container {
	height: 100vh;
	background-color: #f8f9fa;
	display: flex;
	flex-direction: column;
}

.header {
	background-color: #007AFF;
	padding: 44px 20px 20px;
	text-align: center;
}

.title {
	color: white;
	font-size: 18px;
	font-weight: bold;
}

.content {
	flex: 1;
	padding: 20px;
}

.section {
	background-color: white;
	margin-bottom: 15px;
	padding: 20px;
	border-radius: 8px;
}

.section-title {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10px;
}

.section-content {
	font-size: 14px;
	color: #666;
	line-height: 1.6;
	display: block;
}

.footer {
	padding: 20px;
	background-color: white;
	border-top: 1px solid #eee;
}

.confirm-btn {
	width: 100%;
	height: 44px;
	background-color: #007AFF;
	color: white;
	border: none;
	border-radius: 22px;
	font-size: 16px;
	font-weight: bold;
}
</style>