<template>
	<view class="min-h-screen bg-gray-50">
		<view class="divide-y divide-gray-100 bg-white rounded-lg shadow mx-2 mt-4">
			<view :class="['flex items-center justify-between px-4 py-3 cursor-pointer hover:bg-gray-50 transition', item.enum ? '' : '']" @click="openPicker(item)"
				v-for="(item,index) in settingCells" :key="index">
				<view class="flex-1">
					<view class="text-base font-medium">
						{{item.title}}
					</view>
					<view class="text-gray-400 text-sm mt-1 flex items-center" v-if="item.label">
						<text class="mr-2"><i class="fas fa-info-circle"></i></text>{{item.label}}
					</view>
				</view>
				<view class="ml-4 flex items-center">
					<switch :checked="!!value[item.key]" class="accent-blue-500" v-if="!item.enum"
						@change="updateValue(item.key,!value[item.key])"></switch>
					<text class="text-gray-400 text-base" v-else>{{item.enum
            ? item.enum.find((enumItem) => enumItem.value == value[item.key])
                ?.label
            : undefined}}</text>
					<template v-if="item.enum">
						<i class="fas fa-chevron-right text-gray-300 ml-2"></i>
					</template>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const settingCells = [{
			title: "题目字体大小",
			key: "fontSize",
			enum: [{
					label: "小",
					value: 0
				},
				{
					label: "中",
					value: 1
				},
				{
					label: "大",
					value: 2
				},
				{
					label: "超大",
					value: 3
				},
			],
		},

		{
			title: "自动跳转",
			key: "autoJumpNext",
			label: "完成答题后自动跳转下一题",
		},

		{
			title: "自动显示答案解析",
			key: "autoShowResult",
			label: "练习模式下完成答题后显示答案解析",
		},

		{
			title: "随机练习题数",
			label: "如存在未交卷的随机练习，需交卷后下次练习生效",
			key: "randomQuestionNum",
			enum: [{
					label: "30",
					value: 30
				},
				{
					label: "60",
					value: 60
				},
				{
					label: "90",
					value: 90
				},
			],
		},
	];

 
	export default {
	 

		data() {
			return {
				settingCells,

			}
		},
		computed: {
			value() {
				return this.$store.state.qbankSetting.settingValue
			}
		},
		methods: {
			openPicker(item) {
				if (item.enum) {
					uni.showActionSheet({
						itemList: item.enum.map((i) => i.label),
						success: (res) => {
							// console.log(res);
							const {
								value
							} = item.enum[res.tapIndex];
							this.updateValue(item.key, value);
						},
					});
				}
			},
			updateValue(k, v) {

				const newValue = {
					...this.value
				};
				newValue[k] = v;
				this.$store.commit('qbankSetting/update', newValue)
			}
		}
	}
</script>

<style>

</style>