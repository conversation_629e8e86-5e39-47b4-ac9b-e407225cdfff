const questionFields = [{
		key: 'questionContent',
		pattern: /^\d{1,}[.、:：。]/,
	},

	{
		key: 'questionContent',
		pattern: /^\(\d{1,}.\d{1,}\)/,
	},

	{
		key: 'analysis',
		pattern: /^解析[.、:：。]/,
	},
	{
		key: 'rightAnswer',
		pattern: /^答案[.、:：。]/,
	},

	{
		key: 'difficulty',
		pattern: /^难度[.、:：。]/,
	},

	{
		key: 'chapterNames',
		pattern: /^章节[.、:：。]/,
	},

	{
		key: 'options',
		pattern: /^[A-Z,a-z][.、:：。]/,
	},
];

const yesNoRightAnswers = [
	['正确', '是', '对', 'T', 't'],
	['错', '错误', 'F', 'f', '否'],
];

const difficulties = [
	['低', '简单', '易'],
	['中', '一般', '适中'],
	['高', '困难', '难'],
];


export const questionTypeEnum = [{
		label: '单选题',
		value: 1
	},
	{
		label: '多选题',
		value: 2
	},
	{
		label: '判断题',
		value: 3
	},
	{
		label: '填空题',
		value: 4,
		disabled: true
	},
	{
		label: '问答题',
		value: 5,
		disabled: true
	},
	{
		label: '组合题',
		value: 9
	},
];

const difficultyEnum = [{
		label: '简单',
		value: 1
	},
	{
		label: '一般',
		value: 2
	},
	{
		label: '困难',
		value: 3
	},
];

const optionPreEnum = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K'];


  

export function textToQuestionList(content) {
	const now = new Date().getTime();

	let lastContentType = '';
	let lastOptionIndex = -1;

	let question = {
		questionContent: '',
		questionType: undefined,
		options: [],
		analysis: '',
		difficulty: 2,
		rightAnswers: [],
		_error: [],
		_index: 0,
		_rowIndex: 0,
		chapterFullName: '',
	};

	const questionList = [];
	let index = 0;
	const lineTexts = content.split('\n');
	console.log(lineTexts);
	let groupQuestion = undefined;

	for1: for (let i = 0; i < lineTexts.length; i++) {
		const text = lineTexts[i].trim();
		if (!text) {
			continue;
		}

		for (let k = 0; k < questionFields.length; k++) {
			const {
				key,
				pattern
			} = questionFields[k];

			if (pattern.test(text)) {
				//匹配出来的是题干
				if (k === 0) {
					lastContentType = '';
					question = {
						questionContent: '',
						questionType: 1,
						questionTypeText: '单选题',
						options: [],
						analysis: '',
						difficulty: 2,
						rightAnswers: [],
						_error: [],
						_index: index++,
						_rowIndex: i,
						chapterFullName: '',
					};
					// groupQuestion = question;
					groupQuestion = undefined;
					questionList.push(question);
					//子题干
				} else if (k === 1) {
					//匹配出子题目
					if (
						question &&
						question.questionContent &&
						(!!groupQuestion ||
							(!question.options.length &&
								!question.rightAnswers.length &&
								!question.analysis))
					) {
						if (!groupQuestion) {
							question.children = [];
							groupQuestion = question;
							groupQuestion.questionType = 9;
							groupQuestion.questionTypeText = '组合题';
						}

						question = {
							questionContent: '',
							questionType: 1,
							questionTypeText: '单选题',
							options: [],
							analysis: '',
							difficulty: 2,
							rightAnswers: [],
							_error: [],
							_index: groupQuestion.children?.length || 0,
							_rowIndex: groupQuestion.children?.length || 0,
							chapterFullName: '',
						};
						if (groupQuestion.children) {
							groupQuestion.children.push(question);
						}
					} else {
						continue;
					}
				}

				if (key === 'options') {
					const splitOptionTexts = text
						.replaceAll(/\s[A-Z,a-z][.、:：。]/g, function(v) {
							return '_||_' + v;
						})
						.split('_||_');

					for (let i = 0; i < splitOptionTexts.length; i++) {
						const optionText = splitOptionTexts[i].trim();
						if (optionText) {
							const optionIndex = optionPreEnum.indexOf(
								optionText.substring(0, 1).toUpperCase(),
							);

							if (optionIndex >= 0) {
								lastOptionIndex = optionIndex;
								//console.log(optionIndex, optionText.substring(2));
								question.options[optionIndex] = optionText.substring(2);
							}
						}
					}
					// if(question.options.length&&!question.questionType){
					//     question.questionType = 1;
					//     question.questionTypeText = '单选题';
					// }



				} else if (key === 'rightAnswer') {
					const newText = text.replace(pattern, '');
					if (!newText) {
						continue for1;
					}

					let yesNoIndex = -1;
					yesNoRightAnswers.forEach((yesNoItem, index) => {
						if (yesNoItem.indexOf(newText) >= 0) {
							yesNoIndex = index;
						}
					});

					if (yesNoIndex >= 0) {
						question.rightAnswers = [yesNoIndex + ''];
						question.questionType = 3;
						continue for1;
					}

					const rightAnswers = newText
						.replaceAll(',', '')
						.replaceAll('，', '')
						.replaceAll('、', '')
						.replaceAll(' ', '')
						.split('')
						.map((optionItem) => {
							return optionPreEnum.indexOf(optionItem.toUpperCase()) + '';
						})

						.sort();

					question.rightAnswers = rightAnswers.filter((element, index) => {
						return rightAnswers.indexOf(element) === index;
					});
				} else if (key === 'difficulty') {
					let difficulty = -1;
					const newText = text.replace(pattern, '');
					difficulties.forEach((t, index) => {
						if (t.indexOf(newText) >= 0) {
							difficulty = index;
						}
					});
					if (difficulty >= 0) {
						question.difficulty = difficulty + 1;
					}
				} else if (key === 'chapterNames') {
					if (!groupQuestion) {
						const newText = text.replace(pattern, '');

						question.chapterFullName = newText
							.split('/')
							.map((item) => item.trim())
							.filter((item) => !!item)
							.join(' / ');
					}
				} else {
					const newText = text.replace(pattern, '');
					question[key] = newText;
				}
				lastContentType = key;
				continue for1;
			}
		}

		if (lastContentType) {
			if (lastContentType === 'options') {
				if (lastOptionIndex >= 0) {
					question.options[lastOptionIndex] =
						(question.options[lastOptionIndex] || '') + '<br>' + text;
				}
			} else {
				question[lastContentType] = question[lastContentType] + '<br>' + text;
			}
		}
	}

	// const errorQuestionIndexes: number[] = [];

	function checkError(questionList) {
		questionList.forEach((item) => {
			item.difficultyText = difficultyEnum.find(
				(difficultyEnumItem) => difficultyEnumItem.value === item.difficulty,
			)?.label;
			if (item.questionType === 9) {
				//组合题
				if (item.children && item.children.length) {
					checkError(item.children);
				} else {
					item._error.push('子题目不能为空');
				}
			} else if (item.questionType !== 3) {
				// if (item.difficulty < 0) {
				//   item._error.push('难度');
				// }

				if (item.options.length < 2) {
					item._error.push('选项不能少于2个');
					// item.questionTypeText = '未知题';
					// errorQuestionIndexes.push(index);
					return;
				}
				if (!item.rightAnswers.length) {
					item._error.push('答案不能为空');
					// item.questionTypeText = '未知题';
					// errorQuestionIndexes.push(index);
					return;
				}

				item.questionType = item.rightAnswers.length === 1 ? 1 : 2;

				item.questionTypeText = questionTypeEnum.find(
					(questionType) => questionType.value === item.questionType,
				)?.label;

				const options = [...item.options];

				if (options.filter((option) => option === undefined).length) {
					item._error.push('选项标识必须是连续的（A-J）');
					//errorQuestionIndexes.push(index);
					return;
				}

				if (options.filter((option) => option === '').length) {
					item._error.push('选项内容不允许为空');
					// errorQuestionIndexes.push(index);
					return;
				}
				// console.log(item.rightAnswers, item.options);
				// if (item.rightAnswers.length === 0) {
				//   item._error.push('答案不允许为空');
				//   // errorQuestionIndexes.push(index);
				//   return;
				// }

				if (item.rightAnswers.find((index) => !options[parseInt(index)])) {
					item._error.push('答案与选项不匹配');
					return;
				}

				if (
					item.chapterFullName &&
					item.chapterFullName.split(' / ').length > 2
				) {
					item._error.push('章节最多只能为2级');
					//item.questionTypeText = '未知题';
					// errorQuestionIndexes.push(index);
					return;
				}
			} else {
				item.questionTypeText = questionTypeEnum.find(
					(questionType) => questionType.value === item.questionType,
				)?.label;
			}
		});
	}
	checkError(questionList);



	// setQuestionList(questionList);

	console.log('end', new Date().getTime() - now);


	return questionList;
}