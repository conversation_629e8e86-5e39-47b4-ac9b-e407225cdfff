<template>
  <view class="min-h-screen bg-gray-50">
    <!-- 头部信息 -->
    <view class="bg-white px-4 py-4 shadow-sm">
      <view class="flex justify-between items-center">
        <view class="flex-1">
          <view class="text-lg text-gray-800 font-semibold mb-1">{{
            modelData.name
          }}</view>
          <view class="text-gray-500 text-sm"
            >提交时间：{{ modelData.closeTime }}</view
          >
        </view>

        <view>
          <button
            class="border border-primary-500 text-primary-500 bg-white px-4 py-2 rounded-lg shadow-sm hover:bg-primary-50 transition text-sm font-medium"
            @click="againAnswer"
            v-if="modelData.type != 3 && modelData.type != 4"
          >
            <i class="fas fa-redo mr-1"></i>再次{{
              modelData.isExam ? "考试" : "练习"
            }}
          </button>
        </view>
      </view>
    </view>

    <!-- 正确率/得分区块 -->
    <view class="px-4 py-6">
      <view class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <template v-if="!modelData.isExam">
          <view class="flex justify-center mb-4">
            <progress-circle :value="percent"></progress-circle>
          </view>
          <view class="text-center text-gray-500 text-sm"
            >正确率=做对题目量/已做题目量</view
          >
        </template>

        <view v-else class="flex justify-between items-end mb-6">
          <view class="flex-1">
            <view class="text-base text-gray-700 mb-2">得分</view>
            <view class="flex items-baseline">
              <text class="text-4xl font-bold text-primary-500">{{
                modelData.score
              }}</text>
              <text class="text-base ml-1 text-gray-600">分</text>
            </view>
            <view class="text-gray-500 text-sm mt-1"
              >总分：{{ modelData.totalScore }}分</view
            >
          </view>

          <view class="flex-1 text-right">
            <view class="text-base text-gray-700 mb-2">用时</view>
            <view class="text-4xl font-bold text-primary-500">{{
              formatGap
            }}</view>
            <view class="text-gray-500 text-sm mt-1"
              >总时间：{{ modelData.limitDuration / 60 }}分钟</view
            >
          </view>
        </view>

        <!-- 统计区块 -->
        <view class="grid grid-cols-4 gap-4">
          <view
            class="bg-primary-50 rounded-lg flex flex-col items-center py-4 border border-primary-100"
          >
            <text class="text-primary-500 text-sm font-medium mb-1">答对</text>
            <view class="text-primary-500 text-xl font-bold">{{
              modelData.correctNum
            }}</view>
          </view>
          <view
            class="bg-gray-50 rounded-lg flex flex-col items-center py-4 border border-gray-100"
          >
            <text class="text-gray-600 text-sm font-medium mb-1">不全对</text>
            <view class="text-gray-600 text-xl font-bold">0</view>
          </view>
          <view
            class="bg-gray-50 rounded-lg flex flex-col items-center py-4 border border-gray-100"
          >
            <text class="text-gray-600 text-sm font-medium mb-1">已做题</text>
            <view class="text-gray-700 text-xl font-bold">{{
              modelData.correctNum + modelData.wrongNum
            }}</view>
          </view>
          <view
            class="bg-gray-50 rounded-lg flex flex-col items-center py-4 border border-gray-100"
          >
            <text class="text-gray-600 text-sm font-medium mb-1">总题数</text>
            <view class="text-gray-700 text-xl font-bold">{{
              modelData.totalNum
            }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 答题卡区块 -->
    <view class="bg-white mx-4 rounded-xl shadow-sm border border-gray-100">
      <view class="flex items-center border-b border-gray-100 px-4 py-4">
        <view class="flex items-center text-primary-500 mr-3">
          <i class="fas fa-th-list text-lg"></i>
        </view>
        <text class="text-lg font-bold text-gray-800">答题卡</text>
        <text class="text-sm text-gray-500 ml-auto"
          >点击题目序号可跳转至该题解析</text
        >
      </view>
      <answer-card
        :showResult="true"
        :questionList="modelData.questionList"
        @indexClick="toAllAnalysis"
      ></answer-card>
    </view>

    <!-- 底部操作区 -->
    <view class="h-20"></view>
    <view
      class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 flex items-center justify-center h-20 shadow-lg z-10"
    >
      <view class="flex-1 flex items-center justify-center">
        <view class="flex w-full px-4 space-x-4">
          <view class="flex-1">
            <button
              class="w-full bg-gradient-to-r from-gray-50 to-gray-100 text-gray-700 py-3.5 rounded-xl text-base font-semibold shadow-sm hover:shadow-md transition-all duration-200 border border-gray-200 hover:border-gray-300 flex items-center justify-center"
              @click="toWrongAnalysis"
            >
              <i class="fas fa-exclamation-triangle mr-2 text-yellow-500"></i>
              <span>错题解析</span>
            </button>
          </view>
          <view class="flex-1">
            <button
              class="w-full bg-gradient-to-r from-primary-500 to-primary-600 text-white py-3.5 rounded-xl text-base font-semibold shadow-md hover:shadow-lg transition-all duration-200 flex items-center justify-center"
              @click="toAllAnalysis(0)"
            >
              <i class="fas fa-chart-line mr-2"></i>
              <span>全部解析</span>
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      modelData: {},
      title: "",
    };
  },
  computed: {
    percent() {
      const { correctNum, wrongNum } = this.modelData || {
        correctNum: 0,
        wrongNum: 0,
      };
      const answeredNum = wrongNum + correctNum;
      if (answeredNum) {
        return parseInt((correctNum / answeredNum) * 10000 + "") / 100;
      }
      return 0;
    },
    formatGap() {
      const spentDuration = this.modelData.spentDuration || 0;
      let staytimeGap = spentDuration * 1000;
      let stayHour = Math.floor(staytimeGap / (3600 * 1000));
      let leave1 = staytimeGap % (3600 * 1000);
      let stayMin = Math.floor(leave1 / (60 * 1000));
      let leave2 = leave1 % (60 * 1000);
      let staySec = Math.floor(leave2 / 1000);
      stayHour = stayHour < 10 ? "0" + stayHour : stayHour;
      stayMin = stayMin < 10 ? "0" + stayMin : stayMin;
      staySec = staySec < 10 ? "0" + staySec : staySec;
      return stayHour + ":" + stayMin + ":" + staySec;
    },
  },
  onLoad(options) {
    this.answerId = options.answerId;
    this.loadData();
  },
  methods: {
    loadData() {
      this.$reqGet(
        `/front/edu/user-answer/result`,
        {
          id: this.answerId,
        },
        true,
        "加载中"
      ).then((res) => {
        const { data, success, errorMessage } = res || {};
        if (success) {
          this.modelData = data;
          const title = `${data.isExam ? "考试" : "练习"}结果`;
          uni.setNavigationBarTitle({
            title,
          });
          this.title = title;
        } else {
          uni.showModal({
            title: "提示",
            content: errorMessage || "系统繁忙",
            showCancel: false,
          });
        }
      });
    },

    toAllAnalysis(index) {
      const { name, questionList } = this.modelData;
      getApp().globalData.analysisPageData = {
        name,
        questionList,
        index,
      };
      uni.navigateTo({
        url: "./analysis",
      });
    },
    toWrongAnalysis() {
      const { name, questionList, isExam } = this.modelData;
      const wrongQuestionList = questionList.filter((item) => item.state == 1);
      if (!wrongQuestionList.length) {
        uni.showToast({
          icon: "none",
          title: `本次${!isExam ? "练习" : "考试"}没有错题`,
        });
        return;
      }

      getApp().globalData.analysisPageData = {
        name,
        questionList: wrongQuestionList,
      };
      uni.navigateTo({
        url: "./analysis",
      });
    },
    againAnswer() {
      const { type, mainId, bankId } = this.modelData;
      let url;
      switch (type) {
        case 1:
          url = "/pages/practice/do?mode=chapter&chapterId=" + mainId;
          break;
        case 2:
          url = "/pages/practice/do?mode=random&bankId=" + bankId;
          break;

        case 5 || 6:
          url =
            "/pages/practice/do?mode=paper&paperId=" +
            mainId +
            "&isExam=" +
            this.modelData.isExam;
          break;

        default:
          break;
      }

      if (url) {
        uni.redirectTo({
          url,
        });
      }
    },
  },
};
</script>

<style></style>
