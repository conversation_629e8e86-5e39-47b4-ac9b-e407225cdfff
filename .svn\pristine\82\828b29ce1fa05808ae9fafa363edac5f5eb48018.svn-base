<template>
  <view class="h-screen bg-gray-50 flex flex-col">
    <!-- 搜索和筛选布局 -->
    <view class="bg-gray-50 shadow-sm sticky top-0 z-20">
      <!-- 优雅椭圆形搜索框 -->
      <view class="px-4 py-3.5 bg-gray-50 border-b border-gray-100">
        <view class="relative flex items-center">
          <!-- 搜索图标（左侧） -->
          <text class="fas fa-search text-gray-500 text-sm absolute left-4 top-1/2 transform -translate-y-1/2 z-10"></text>
          
          <!-- 搜索输入框 -->
          <input 
            v-model="searchKeyword"
            placeholder="搜索密码..."
            class="h-10 w-full pl-10 pr-14 bg-white border border-gray-200 rounded-full text-sm shadow-sm"
            @confirm="doSearch"
          />
          
          <!-- 清除按钮 -->
          <view v-if="searchKeyword" class="absolute right-12 top-1/2 -translate-y-1/2 z-30 flex items-center justify-center">
            <view @click.stop="clearSearch" class="h-6 w-6 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200 transition-colors cursor-pointer">
              <text class="fas fa-times text-gray-500 text-xs"></text>
            </view>
          </view>
          
          <!-- 搜索按钮 -->
          <view @click="doSearch" class="absolute right-2 transform -translate-y-0">
            <view class="h-7 w-7 rounded-full bg-gray-600 shadow-sm flex items-center justify-center hover:bg-gray-700 transition-colors">
              <text class="fas fa-arrow-right text-white text-xs"></text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 筛选条件 - 同一行 -->
      <view class="flex items-center px-4 py-3 bg-white border-b border-gray-100">
        <!-- 类型选择(picker) -->
        <view class="flex-1 relative mr-2">
          <picker 
            @change="onTypeChange" 
            :value="typePickerIndex" 
            :range="typePickerRange"
          >
            <view class="flex items-center justify-between h-8 px-3 bg-gray-50 border border-gray-200 rounded-full">
              <view class="flex items-center">
                <text class="fas fa-layer-group text-gray-400 mr-1.5 text-xs"></text>
                <text class="text-xs truncate" :class="selectedPasswordTab ? 'text-gray-800' : 'text-gray-400'">
                  {{ !selectedPasswordTab ? '选择类型' : getTabNameById(selectedPasswordTab) }}
                </text>
              </view>
              <view class="flex items-center">
                <text 
                  v-if="selectedPasswordTab" 
                  @click.stop="clearTypeFilter" 
                  class="fas fa-times-circle text-gray-400 text-xs mr-1 hover:text-primary-400"
                ></text>
                <text class="fas fa-chevron-down text-gray-400 text-xs"></text>
              </view>
            </view>
          </picker>
        </view>
        
        <!-- 状态选择(picker) -->
        <view class="flex-1 relative">
          <picker 
            @change="onStatusPickerChange" 
            :value="statusPickerIndex" 
            :range="statusPickerRange"
          >
            <view class="flex items-center justify-between h-8 px-3 bg-gray-50 border border-gray-200 rounded-full">
              <view class="flex items-center">
                <text class="fas fa-filter text-gray-400 mr-1.5 text-xs"></text>
                <text class="text-xs truncate" :class="selectedStatus>=0  ? 'text-gray-800' : 'text-gray-400'">
                  {{ selectedStatus<0  ? '选择状态' : getStatusNameById(selectedStatus) }}
                </text>
              </view>
              <view class="flex items-center">
                <text 
                  v-if="selectedStatus>=0" 
                  @click.stop="clearStatusFilter" 
                  class="fas fa-times-circle text-gray-400 text-xs mr-1 hover:text-primary-400"
                ></text>
                <text class="fas fa-chevron-down text-gray-400 text-xs"></text>
              </view>
            </view>
          </picker>
        </view>
      </view>
    </view>

    <!-- 密码列表 -->
    <view class="flex-1 flex flex-col overflow-hidden">
      <!-- 添加密码按钮和操作栏 -->
      <view class="flex items-center justify-between px-4 py-3 mb-1">
        <view class="flex items-center">
          <text class="text-sm text-gray-600">共 {{ total }} 条密码</text>
        </view>
        <view class="flex items-center space-x-2">
          <view>
            <button 
            @click="openAddPasswordModal" 
            class="py-1.5 px-3 bg-primary-50 text-primary-500 rounded-full text-xs font-medium flex items-center hover:bg-primary-100 transition-colors"
          >
            <text class="fas fa-plus mr-1.5"></text>添加密码
          </button>
          </view>
          <view>
            <button 
            @click="openBatchGenerateModal" 
            class="py-1.5 px-3 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-full text-xs font-medium shadow-sm flex items-center hover:shadow-md transition-all"
          >
            <text class="fas fa-magic mr-1.5"></text>批量生成
          </button>
          </view>
        </view>
      </view>

      <!-- 列表区域使用scroll-pagination -->
      <scroll-pagination
        class="flex-1 overflow-hidden"
        :page-size="10"
        :auto-load="true"
        ref="scrollPagination"
        :enable-refresh="true"
        @load="loadData"
      >
        <template v-slot="{list}">
          <!-- 密码列表 -->
          <view class="px-3 space-y-3 ">
            <view 
              v-for="(password, index) in list" 
              :key="password.id"
              class="bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow overflow-hidden"
            >
              <!-- 密码卡片头部 -->
              <view class="px-3 py-2 border-b border-gray-100 bg-white flex items-center justify-between">
                <view class="flex items-center space-x-2">
                  <!-- 密码类型标签 -->
                  <view :class="[
                    'px-2 py-0.5 rounded-full text-xs',
                    password.type === 2
                      ? 'bg-blue-50 text-blue-600 border border-blue-200' 
                      : 'bg-primary-50 text-primary-600 border border-primary-200'
                  ]">
                    <text :class="[password.type === 2? 'fas fa-stopwatch' : 'fas fa-infinity', 'mr-1']"></text>
                    {{ password.type === 2 ? '一次性' : '固定' }}
                  </view>

                  <!-- 状态标签 -->
                  <view :class="[
                    'px-2 py-0.5 rounded-full text-xs',
                    password.status==2 
                      ? 'bg-gray-100 text-gray-500' 
                      : (password.status==1 
                        ? 'bg-red-50 text-red-600' 
                        : 'bg-green-50 text-green-600')
                  ]">
                    {{ password.status==2 ? '已禁用' : (password.status==1 ? '已使用' : '可用') }}
                  </view>
                  
                  <!-- 使用次数 - 简化显示 -->
                  <view class="text-xs text-gray-500">
                    使用 {{ password.usedCount || 0 }} 次
                  </view>
                </view>

                <view class="flex items-center">
                  <!-- 创建时间 -->
                  <text class="text-xs text-gray-400 mr-2">{{ password.createTime }}</text>
                </view>
              </view>

              <!-- 密码卡片内容 -->
              <view class="px-3 py-2">
                <!-- 密码内容 -->
                <view class="flex items-center justify-between">
                  <view class="flex-1 bg-gray-50 rounded-lg py-2 px-3 border border-gray-200 mr-2">
                    <text class="font-mono text-base font-medium tracking-wider">{{ password.code }}</text>
                  </view>
                  <button 
                    @click="copyPassword(password)" 
                    class="w-8 h-8 bg-primary-50 rounded-lg flex items-center justify-center hover:bg-primary-100 transition-colors"
                  >
                    <text class="fas fa-copy text-primary-500 text-sm"></text>
                  </button>
                </view>

                <!-- 底部操作按钮 -->
                <view class="flex justify-end mt-3 space-x-4">
                    <view>
                      <button 
                    v-if="password.status==0"
                    @click="togglePasswordStatus(password)" 
                    class="w-8 h-8 bg-yellow-50 rounded-lg flex items-center justify-center hover:bg-yellow-100 transition-colors"
                  >
                    <text class="fas fa-ban text-yellow-600 text-sm"></text>
                  </button>
                    </view>
                    <view>
                      <button 
                    @click="deletePassword(password)" 
                    class="w-8 h-8 bg-red-50 rounded-lg flex items-center justify-center hover:bg-red-100 transition-colors"
                  >
                    <text class="fas fa-trash text-red-600 text-sm"></text>
                  </button>
                    </view>
                </view>
              </view>
            </view>
          </view>
        </template>
        
        <!-- 空状态模板 -->
        <template #empty>
          <view class="py-12 px-4 bg-white rounded-xl shadow-sm flex flex-col items-center justify-center">
            <view class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <text class="fas fa-key text-gray-300 text-2xl"></text>
            </view>
            <text class="text-gray-500 text-sm">{{ searchKeyword || selectedStatus>=0  || selectedPasswordTab? '未找到相关密码' : '暂无密码' }}</text>
            <text v-if="!searchKeyword && selectedStatus<0 && selectedPasswordTab" class="text-gray-400 text-xs block mt-1">点击右上角按钮添加或生成密码</text>
            
          </view>
        </template>
      </scroll-pagination>
    </view>

    <!-- 底部安全区域 -->
    <view class="h-8"></view>
    
    <!-- 添加密码弹窗 -->
    <view v-if="showAddPasswordModal" class="fixed inset-0 z-50 flex items-center justify-center">
      <view class="fixed inset-0 bg-black bg-opacity-50 z-10" @click="closeAddPasswordModal"></view>
      <view class="bg-white rounded-3xl p-6 mx-6 relative z-50 w-full max-w-sm shadow-lg">
        <view class="flex items-center justify-between mb-6">
          <view class="flex items-center">
            <view class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center mr-3">
              <text class="fas fa-plus text-primary-500"></text>
            </view>
            <text class="text-xl font-bold text-gray-800">添加密码</text>
          </view>
          <view>
            <button @click="closeAddPasswordModal" class="w-8 h-8 rounded-full hover:bg-gray-100 flex items-center justify-center">
              <text class="fas fa-times text-gray-400"></text>
            </button>
          </view>
        </view>
        
        <view class="space-y-4">
          <view>
            <text class="text-sm font-medium text-gray-700 block mb-2">密码类型</text>
            <radio-group @change="onPasswordTypeChange" class="px-1">
              <label 
                v-for="type in passwordTypes" 
                :key="type.id" 
                class="flex items-center mb-2"
              >
                <radio :value="type.id+''" :checked="newPassword.type === type.id"    />
                <view class="flex items-center ml-1">
                  <text :class="[type.id === 2 ? 'fas fa-stopwatch' : 'fas fa-infinity', 'mr-2 text-gray-500']"></text>
                  <text class="text-sm text-gray-800">{{ type.name }}</text>
                </view>
              </label>
            </radio-group>
          </view>

          <view>
            <text class="text-sm font-medium text-gray-700 block mb-2">设置密码 <text class="text-red-500">*</text></text>
            <input 
              v-model="newPassword.password"
              placeholder="请输入6-16位数字或字母组合"
              class="h-auto p-3 border border-solid border-gray-300 rounded-xl text-sm bg-white focus:border-primary-500 hover:border-primary-500"
              maxlength="18"
            />
            <text class="text-xs text-gray-500 mt-1.5 block">密码必须手动输入，长度为6-16位</text>
          </view>
        </view>
        
        <button 
          @click="addPassword" 
          class="w-full mt-6 py-3.5 bg-primary-500 text-white rounded-xl text-sm font-medium"
        >
          <text class="fas fa-plus mr-2"></text>
          添加密码
        </button>
      </view>
    </view>

    <!-- 批量生成密码弹窗 -->
    <view v-if="showBatchGenerateModal" class="fixed inset-0 z-50 flex items-center justify-center">
      <view class="fixed inset-0 bg-black bg-opacity-50 z-10" @click="closeBatchGenerateModal"></view>
      <view class="bg-white rounded-3xl p-6 mx-6 relative z-50 w-full max-w-sm shadow-lg">
        <view class="flex items-center justify-between mb-6">
          <view class="flex items-center">
            <view class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center mr-3">
              <text class="fas fa-magic text-primary-500"></text>
            </view>
            <text class="text-xl font-bold text-gray-800">批量生成密码</text>
          </view>
          <view>
            <button @click="closeBatchGenerateModal" class="w-8 h-8 rounded-full hover:bg-gray-100 flex items-center justify-center">
              <text class="fas fa-times text-gray-400"></text>
            </button>
          </view>
        </view>
        
        <view class="space-y-5">
          <view>
            <text class="text-sm font-medium text-gray-700 block mb-2">密码类型</text>
            <radio-group @change="onBatchTypeChange" class="px-1">
              <label 
                v-for="type in passwordTypes" 
                :key="type.id" 
                class="flex items-center mb-2"
              >
                <radio :value="type.id+''" :checked="batchGenerate.type === type.id"/>
                <view class="flex items-center ml-1">
                  <text :class="[type.id === 2 ? 'fas fa-stopwatch' : 'fas fa-infinity', 'mr-2 text-gray-500']"></text>
                  <text class="text-sm text-gray-800">{{ type.name }}</text>
                </view>
              </label>
            </radio-group>
          </view>

          <view>
            <text class="text-sm font-medium text-gray-700 block mb-2">生成数量</text>
            <view class="flex items-center justify-center">
              <view class="flex bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                <button 
                  @click="decreaseCount" 
                  class="w-10 h-10 flex items-center justify-center bg-gray-50 hover:bg-gray-100 transition-colors"
                >
                  <text class="fas fa-minus text-primary-500"></text>
                </button>
                <view class="px-3 py-2 flex items-center justify-center border-l border-r border-gray-200 bg-white">
                  <input 
                    v-model="batchGenerate.count"
                    type="number" 
                    class="h-full w-16 text-center text-base font-medium text-gray-700"
                    min="1"
                    max="100"
                  />
                </view>
                <button 
                  @click="increaseCount" 
                  class="w-10 h-10 flex items-center justify-center bg-gray-50 hover:bg-gray-100 transition-colors"
                >
                  <text class="fas fa-plus text-primary-500"></text>
                </button>
              </view>
            </view>
            <text class="text-xs text-gray-500 mt-1.5 block">单次最多可生成100个密码</text>
          </view>

          <view>
            <text class="text-sm font-medium text-gray-700 block mb-2">密码长度: {{ batchGenerate.length }} 位</text>
            <view class="flex items-center space-x-2">
              <text class="text-xs font-medium text-gray-600 w-6 text-center">6</text>
              <slider 
                class="flex-1" 
                min="6" 
                max="16" 
                :value="batchGenerate.length" 
                activeColor="#4F46E5"
                backgroundColor="#E5E7EB"
                blockColor="#4F46E5"
                blockSize="20"
                @change="onSliderChange"
              />
              <text class="text-xs font-medium text-gray-600 w-6 text-center">16</text>
            </view>
            <view class="w-full bg-gray-50 rounded-lg p-3 mt-2 border border-gray-100">
              <text class="text-xs text-gray-600">更长的密码安全性更高，但不便于记忆和输入</text>
            </view>
          </view>
        </view>
        
        <button 
          @click="generateBatchPasswords" 
          class="w-full mt-6 py-3.5 bg-primary-500 text-white rounded-xl text-sm font-medium"
        >
          <text class="fas fa-magic mr-2"></text>
          批量生成
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      bankId: '',
      showAddPasswordModal: false,
      showBatchGenerateModal: false,
      selectedStatus: -1,
      selectedPasswordTab: null,
      passwordTabs: [
        { id: 1, name: '固定密码' },
        { id: 2, name: '一次性密码' },
      ],
      typePickerIndex: 0, // 类型选择器当前选中项
      statusOptions: [
        { id: 0, name: '可用' },
        { id: 1, name: '已使用' },
        { id:2, name: '已禁用' }
      ],
      total: 0, // 总记录数
      newPassword: {
        type: 1,
        password: ''
      },
      batchGenerate: {
        type: 1,
        count: 10,
        length: 6
      },
      passwordTypes: [
        { id: 1, name: '固定密码' },
        { id: 2, name: '一次性密码' }
      ],
      searchKeyword: '', // 搜索关键词
      statusPickerIndex: 0, // 状态选择器当前选中项
    }
  },
  computed: {
    statusPickerRange() {
      return this.statusOptions.map(item => item.name);
    },
    typePickerRange() {
      return this.passwordTypes.map(item => item.name);
    }
  },
  onLoad(options) {
    if (options.bankId) {
      this.bankId = options.bankId;
    }
  },
  methods: {
    // 根据ID获取类型名称
    getTabNameById(id) {
      const tab = this.passwordTypes.find(tab => tab.id === id);
      return tab ? tab.name : '选择类型';
    },
    
    // 根据ID获取状态名称
    getStatusNameById(id) {
      const status = this.statusOptions.find(status => status.id === id);
      return status ? status.name : '选择状态';
    },
    
    loadData(params, callback) {
      // 根据筛选条件构建查询参数
      const queryParams = {
        ...params, 
        bankId: this.bankId,
        searcher:{
          EQ_type: !!this.selectedPasswordTab ? this.selectedPasswordTab : '',
        EQ_status: this.selectedStatus>=0  ? this.selectedStatus : '',
        LIKE_code: this.searchKeyword || '',
        }
      };
      
      // 调用API获取分页数据
      this.$reqGet('/front/edu-personal/qbank-password/page', queryParams).then(res => {
        if (res.success) {
          callback(res.data);
          this.total = res.data.total || 0;
        } else {
          uni.showToast({
            title: res.errorMessage || '加载密码失败',
            icon: 'none'
          });
          // 即使失败也要回调空数据，以便组件正确处理
          callback({
            list: [],
            total: 0,
            pageNo: params.pageNo,
            pageSize: params.pageSize
          });
        }
      }).catch(err => {
        console.error('加载密码失败:', err);
        // 发生错误时同样回调空数据
        callback({
          list: [],
          total: 0,
          pageNo: params.pageNo,
          pageSize: params.pageSize
        });
      });
    },
    
    onTypeChange(e) {
      const index = parseInt(e.detail.value);
      this.selectedPasswordTab = this.passwordTypes[index].id;
      this.typePickerIndex = index;
      this.doSearch(); // 筛选条件变化，重新加载
    },
    
    clearTypeFilter() {
      this.selectedPasswordTab = null;
      this.typePickerIndex = 0;
      this.doSearch(); // 筛选条件变化，重新加载
      return false; // 阻止事件冒泡
    },
    
    onStatusPickerChange(e) {
      this.statusPickerIndex = parseInt(e.detail.value);
      this.selectedStatus = this.statusOptions[this.statusPickerIndex].id;
      this.doSearch(); // 筛选条件变化，重新加载
    },

    clearStatusFilter() {
      this.selectedStatus = -1;
      this.statusPickerIndex = 0;
      this.doSearch(); //筛选条件变化，重新加载
      return false; //阻止事件冒泡
    },
    
    doSearch() {
      if (this.$refs.scrollPagination) {
        this.$refs.scrollPagination.init(); // 重置分页并重新加载
      }
    },

    clearSearch() {
      this.searchKeyword = '';
      this.doSearch(); // 筛选条件变化，重新加载
    },
    
    openAddPasswordModal() {
      this.newPassword = {
        type: 1,
        password: ''
      };
      this.showAddPasswordModal = true;
    },
    
    closeAddPasswordModal() {
      this.showAddPasswordModal = false;
    },
    
    openBatchGenerateModal() {
      this.batchGenerate = {
        type: 1,
        count: 10,
        length: 6
      };
      this.showBatchGenerateModal = true;
    },
    
    closeBatchGenerateModal() {
      this.showBatchGenerateModal = false;
    },
    
    onPasswordTypeChange(e) {
      this.newPassword.type = e.detail.value;
    },
    
    onBatchTypeChange(e) {
      this.batchGenerate.type = e.detail.value;
    },
    
    addPassword() {
      let password = this.newPassword.password.trim();
      
      // 检查密码是否为空
      if (!password) {
        uni.showToast({
          title: '请输入密码',
          icon: 'none'
        });
        return;
      }
      
      // 检查密码长度
      if (password.length < 6 || password.length > 16) {
        uni.showToast({
          title: '密码长度应为6-16位',
          icon: 'none'
        });
        return;
      }
      //检查密码是否为数字或字母
      if (!/^[a-zA-Z0-9]+$/.test(password)) {
        uni.showToast({
          title: '密码只能为数字或字母',
          icon: 'none'
        });
        return;
      }
      
 
      
      // 在实际应用中，这里应该调用API保存密码
      this.$reqPost('/front/edu-personal/qbank-password/create', {
        bankId: this.bankId,
        code: password,
        type: this.newPassword.type
      }).then(res => {
        if (res.success) {
          this.closeAddPasswordModal();
          this.doSearch(); // 添加成功后刷新列表
          
          uni.showToast({
            title: '密码添加成功',
            icon: 'success'
          });
        } else {
          uni.showToast({
            title: res.errorMessage || '添加密码失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error('添加密码失败:', err);
        uni.showToast({
          title: '添加密码失败',
          icon: 'none'
        });
      });
    },
        
    decreaseCount() {
      if (this.batchGenerate.count > 1) {
        this.batchGenerate.count--;
      }
    },
    
    increaseCount() {
      if (this.batchGenerate.count < 100) {
        this.batchGenerate.count++;
      }
    },
    
    onSliderChange(e) {
      this.batchGenerate.length = e.detail.value;
    },
    
    generateBatchPasswords() {
      if (this.batchGenerate.count < 1 || this.batchGenerate.count > 100) {
        uni.showToast({
          title: '生成数量应在1-100之间',
          icon: 'none'
        });
        return;
      }
      
      // 在实际应用中，这里应该调用API批量生成密码
      this.$reqPost('/front/edu-personal/qbank-password/batchGen', {
        bankId: this.bankId,
        type: this.batchGenerate.type,
        count: this.batchGenerate.count,
        length: this.batchGenerate.length
      }).then(res => {
        if (res.success) {
          this.closeBatchGenerateModal();
          this.doSearch(); // 生成成功后刷新列表
          
          uni.showToast({
            title: `成功生成${this.batchGenerate.count}个密码`,
            icon: 'success'
          });
        } else {
          uni.showToast({
            title: res.errorMessage || '生成密码失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error('生成密码失败:', err);
        
        // 示例：模拟生成成功（仅用于演示）
        this.closeBatchGenerateModal();
        this.doSearch(); // 刷新列表
        
        uni.showToast({
          title: `成功生成${this.batchGenerate.count}个密码`,
          icon: 'success'
        });
      });
    },
    
    copyPassword(password) {
      uni.setClipboardData({
        data: password.code,
        success: () => {
          uni.showToast({
            title: '密码已复制',
            icon: 'success'
          });
        }
      });
    },
    
    togglePasswordStatus(password) {
      // if (password.used) return; // 已使用的密码不能切换状态
      uni.showModal({
        title: '确认禁用',
        content: `确定要禁用密码"${password.code}"吗？禁用后将无法使用该密码，已加入的用户不受影响`,
        success: (res) => {
          if (res.confirm) {
             // 在实际应用中，这里应该调用API更新密码状态
      this.$reqGet('/front/edu-personal/qbank-password/disable', {
        id: password.id,
    
      }).then(res => {
        if (res.success) {
          // 更新本地状态
      
          password.status=2
          uni.showToast({
            title: '禁用成功',
            icon: 'success'
          });
        } else {
          uni.showToast({
            title: res.errorMessage || '操作失败',
            icon: 'none'
          });
        }
      })
          }
        }
      });
    
     
    },
    
    deletePassword(password) {
      uni.showModal({
        title: '确认删除',
        content: password.usedCount?`确定要删除密码"${password.code}"吗？删除后该密码加入的用户将无法使用需再次加入`:`确定要删除密码"${password.code}"吗？`,
        success: (res) => {
          if (res.confirm) {
            // 在实际应用中，这里应该调用API删除密码
            this.$reqPost('/front/edu-personal/qbank-password/delete', {
              id: password.id
            }).then(res => {
              if (res.success) {
                this.doSearch(); // 删除成功后刷新列表
                
                uni.showToast({
                  title: '密码已删除',
                  icon: 'success'
                });
              } else {
                uni.showToast({
                  title: res.errorMessage || '删除失败',
                  icon: 'none'
                });
              }
            }).catch(err => {
              console.error('删除密码失败:', err);
              
              // 示例：模拟删除成功（仅用于演示）
              this.doSearch(); // 刷新列表
              
              uni.showToast({
                title: '密码已删除',
                icon: 'success'
              });
            });
          }
        }
      });
    }
  }
}
</script> 