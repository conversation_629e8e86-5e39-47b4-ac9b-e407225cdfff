<template>
  <view class="min-h-screen bg-gray-50">
    <!-- 页面标题 -->
    <view class="px-5 pt-6 pb-4">
      <view class="flex items-center mb-2">
        <view class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mr-3 shadow-sm">
          <text class="fas fa-shield-alt text-primary-500 text-xl"></text>
        </view>
        <view>
          <text class="text-xl font-bold text-gray-800">权限设置</text>
          <text class="text-sm text-gray-500 mt-1 block">管理题库的访问权限与安全选项</text>
        </view>
      </view>
    </view>
    
    <!-- 权限设置列表 -->
    <view class="px-4 py-2">
      <view class="bg-white rounded-2xl p-5 shadow-sm mb-4 border border-gray-100">
        <!-- 允许题库市场搜索 -->
        <view class="py-4 border-b border-gray-100">
          <view class="flex items-center justify-between">
            <view class="flex items-start">
              <view class="w-10 h-10 rounded-xl bg-blue-50 flex items-center justify-center mr-3 border border-blue-100">
                <text class="fas fa-search text-blue-500 text-lg"></text>
              </view>
              <view class="flex-1">
                <text class="text-base font-medium text-gray-800 block">允许题库市场搜索</text>
                <text class="text-xs text-gray-500 mt-1.5">开启后其他用户可以在题库市场中搜索到此题库</text>
              </view>
            </view>
            <switch 
              :checked="settings.allowSearch" 
              @change="toggleAllowSearch"
              color="#007AFF"
              class="transform scale-85"
            />
          </view>
        </view>
        
        <!-- 密码加入 -->
        <view class="py-4" :class="{'border-b border-gray-100': settings.needPassword}">
          <view class="flex items-center justify-between">
            <view class="flex items-start">
              <view class="w-10 h-10 rounded-xl bg-green-50 flex items-center justify-center mr-3 border border-green-100">
                <text class="fas fa-lock text-green-500 text-lg"></text>
              </view>
              <view class="flex-1">
                <text class="text-base font-medium text-gray-800 block">密码加入</text>
                <text class="text-xs text-gray-500 mt-1.5">开启后其他用户需输入密码才能加入题库</text>
              </view>
            </view>
            <switch 
              :checked="settings.needPassword" 
              @change="togglePasswordRequired"
              color="#007AFF"
              class="transform scale-85"
            />
          </view>
        </view>

        <!-- 密码管理菜单项 -->
        <view 
          v-if="settings.needPassword" 
          class="py-4"
          @click="navigateToPasswordList"
        >
          <view class="p-3 bg-primary-50 rounded-xl border border-primary-100 flex items-center justify-between hover:bg-primary-100 transition-colors cursor-pointer">
            <view class="flex items-center">
              <view class="w-10 h-10 rounded-xl bg-primary-100 flex items-center justify-center mr-3">
                <text class="fas fa-key text-primary-500 text-lg"></text>
              </view>
              <view>
                <text class="text-base font-medium text-gray-800 block">密码管理中心</text>
                <text class="text-xs text-gray-500 mt-1.5">创建和管理题库密码、批量生成和查看使用记录</text>
              </view>
            </view>
            <view class="w-8 h-8 rounded-full bg-white flex items-center justify-center shadow-sm ml-3">
              <text class="fas fa-chevron-right text-primary-400"></text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 温馨提示 -->
      <view class="flex items-center p-4 bg-yellow-50 rounded-xl border border-yellow-100 mb-4">
        <text class="fas fa-lightbulb text-yellow-500 mr-3"></text>
        <text class="text-xs text-gray-600">开启密码设置后，您可以在密码管理中心创建和管理多个不同类型的密码</text>
      </view>
    </view>

   

    <!-- 底部安全区域 -->
    <view class="h-20"></view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      bankId: '',
      showCreatePasswordModal: false,
      settings: {
        allowSearch: true,
        needPassword: false
      },
      selectedPasswordTab: 'all',
  
      passwordTypes: [
        { id: 'once', name: '一次性密码' },
        { id: 'fixed', name: '固定密码' }
      ]
    }
  },
 
  onLoad(options) {
    if (options.bankId) {
      this.bankId = options.bankId;
      this.loadPermissionSettings();
    }
  },
  methods: {
    loadPermissionSettings() {
      this.$reqGet('/front/edu-personal/qbank/permission',{id:this.bankId}).then(res => {
        this.settings = res.data;
      });
    },
    
    toggleAllowSearch(e) {
      this.settings.allowSearch = e.detail.value;
      this.saveSettings();
    },
    
    togglePasswordRequired(e) {
      this.settings.needPassword = e.detail.value;
      this.saveSettings();
    },
    
    saveSettings() {
       this.$reqPost('/front/edu-personal/qbank/savePermission',{
        ...this.settings,
        id:this.bankId
       }).then(res => {
          if(res.success){
            uni.showToast({
              title: '设置已保存',
              icon: 'success'
            });
          }else{
            uni.showToast({ 
              title: res.errorMessage||'保存失败',
              icon: 'none'
            });
          }
       });
    },
    
    

    navigateToPasswordList() {
      uni.navigateTo({
        url: `/pages/questionbank/passwordList?bankId=${this.bankId}`
      });
    }
  }
}
</script>

<style scoped>
 
</style>