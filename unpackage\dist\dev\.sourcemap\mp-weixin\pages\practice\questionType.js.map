{"version": 3, "file": "questionType.js", "sources": ["pages/practice/questionType.vue", "D:/Program Files/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHJhY3RpY2UvcXVlc3Rpb25UeXBlLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"h-screen bg-gray-50\">\r\n    <!-- 自定义导航栏 -->\r\n\r\n    <view class=\"pt-4 pb-4\">\r\n      <view class=\"mx-4 bg-white rounded-xl shadow-sm p-4\">\r\n        <view\r\n          v-for=\"item in typeList\"\r\n          :key=\"item.value\"\r\n          class=\"mb-3 bg-gray-50 rounded-lg p-3 flex items-center justify-between\"\r\n        >\r\n          <view class=\"flex items-center\">\r\n            <view\r\n              class=\"w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center mr-3\"\r\n            >\r\n              <i class=\"fas fa-clipboard-list text-primary-500 text-base\"></i>\r\n            </view>\r\n            <text class=\"text-base font-medium text-gray-800\">{{\r\n              item.label\r\n            }}</text>\r\n          </view>\r\n          <text class=\"text-sm text-gray-500\"\r\n            >共{{ item.questionCount }}题</text\r\n          >\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { questionTypeEnum } from \"@/myjs/question.js\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      typeList: [],\r\n      bankId: \"\",\r\n    };\r\n  },\r\n  onLoad(options) {\r\n    this.bankId = options.bankId || \"\";\r\n    this.loadTypeList();\r\n  },\r\n  methods: {\r\n    loadTypeList() {\r\n      if (!this.bankId) return;\r\n\r\n      this.$reqGet(`/front/edu/qbank/questionTypeList/${this.bankId}`).then(\r\n        (res) => {\r\n          if (res.success) {\r\n            this.typeList = (res.data || []).map((item) => {\r\n              const match = questionTypeEnum.find(\r\n                (q) => q.value === item.value\r\n              );\r\n              return {\r\n                ...item,\r\n                label: match ? match.label : item.value,\r\n              };\r\n            });\r\n          }\r\n        }\r\n      );\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 可根据需要补充样式 */\r\n</style>\r\n", "import MiniProgramPage from 'E:/work/code/edu/edu-personal-uniapp/pages/practice/questionType.vue'\nwx.createPage(MiniProgramPage)"], "names": ["questionTypeEnum"], "mappings": ";;;AAiCA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,UAAU,CAAE;AAAA,MACZ,QAAQ;AAAA;EAEX;AAAA,EACD,OAAO,SAAS;AACd,SAAK,SAAS,QAAQ,UAAU;AAChC,SAAK,aAAY;AAAA,EAClB;AAAA,EACD,SAAS;AAAA,IACP,eAAe;AACb,UAAI,CAAC,KAAK;AAAQ;AAElB,WAAK,QAAQ,qCAAqC,KAAK,MAAM,EAAE,EAAE;AAAA,QAC/D,CAAC,QAAQ;AACP,cAAI,IAAI,SAAS;AACf,iBAAK,YAAY,IAAI,QAAQ,IAAI,IAAI,CAAC,SAAS;AAC7C,oBAAM,QAAQA,cAAAA,iBAAiB;AAAA,gBAC7B,CAAC,MAAM,EAAE,UAAU,KAAK;AAAA;AAE1B,qBAAO;AAAA,gBACL,GAAG;AAAA,gBACH,OAAO,QAAQ,MAAM,QAAQ,KAAK;AAAA;YAEtC,CAAC;AAAA,UACH;AAAA,QACF;AAAA;IAEH;AAAA,EACF;AACH;;;;;;;;;;;;;AChEA,GAAG,WAAW,eAAe;"}