<template>
  <view class="h-screen bg-gray-50 flex flex-col" v-if="!loading">
    <!-- 自定义导航栏 - 固定在顶部 -->
    <view class="fixed top-0 left-0 right-0 z-50">
      <!-- 导航栏主体 -->
      <view
        class="w-full bg-gradient-to-r from-primary-500 to-primary-600 relative overflow-hidden"
        :style="navBarStyle"
      >
        <!-- 装饰背景 -->
        <view class="absolute inset-0 opacity-10">
          <view
            class="absolute top-6 right-6 w-8 h-8 rounded-full bg-white"
          ></view>
          <view
            class="absolute bottom-4 left-12 w-4 h-4 rounded-full bg-white"
          ></view>
          <view
            class="absolute top-10 left-1/2 w-2 h-2 rounded-full bg-white"
          ></view>
        </view>

        <!-- 返回按钮 -->
        <view
          :style="navBarButtonStyle"
          @click="goBack"
          class="flex items-center justify-center"
        >
          <view
            :style="backButtonStyle"
            class="bg-white bg-opacity-20 flex items-center justify-center backdrop-blur-sm rounded-full"
          >
            <i class="fas fa-arrow-left text-white text-base"></i>
          </view>
        </view>

        <!-- 导航栏标题 -->
        <view class="px-4 py-2 relative z-10">
          <view class="text-center">
            <view class="flex items-center justify-center">
              <view
                class="w-6 h-6 rounded-full bg-white bg-opacity-20 flex items-center justify-center mr-2 backdrop-blur-sm"
              >
                <i class="fas fa-clipboard-list text-white text-xs"></i>
              </view>
              <text class="text-white text-base font-bold">模拟考试</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 主体内容 -->
    <view class="flex-1 pb-16" :style="mainContentStyle">
      <!-- 装饰区域 - 和内容一起滚动，紧贴导航栏 -->
      <view
        class="bg-gradient-to-r from-primary-500 to-primary-600 mb-4 relative overflow-hidden -mt-4"
      >
        <!-- 装饰背景 -->
        <view class="absolute inset-0 opacity-10">
          <view
            class="absolute top-8 right-12 w-10 h-10 rounded-full bg-white"
          ></view>
          <view
            class="absolute bottom-8 left-20 w-8 h-8 rounded-full bg-white"
          ></view>
          <view
            class="absolute top-16 left-1/4 w-4 h-4 rounded-full bg-white"
          ></view>
        </view>

        <!-- 装饰内容 -->
        <view class="px-4 py-6 relative z-10">
          <view class="text-center mb-4">
            <text class="text-white text-sm opacity-90"
              >自定义题型和规则，模拟真实考试环境</text
            >
          </view>

          <!-- 功能介绍 -->
          <view class="bg-white bg-opacity-15 rounded-xl p-4 backdrop-blur-sm">
            <view class="flex items-center justify-center">
              <view class="flex items-center">
                <view
                  class="w-8 h-8 rounded-full bg-white bg-opacity-30 flex items-center justify-center mr-3"
                >
                  <i class="fas fa-cogs text-white text-sm"></i>
                </view>
                <view>
                  <text class="text-white text-sm font-medium block"
                    >自定义考试设置</text
                  >
                  <text class="text-white text-xs opacity-80"
                    >选择题型 · 设置分值 · 控制时长</text
                  >
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 存在未完成考试的情况 -->
      <view
        v-if="hasUnfinishedExam"
        class="mx-4 bg-white rounded-lg shadow p-4 mb-3"
      >
        <view class="text-center">
          <view class="mb-4">
            <i class="fas fa-clock text-orange-500 text-4xl"></i>
          </view>
          <text class="text-xl font-bold text-gray-800 mb-2 block"
            >未完成考试</text
          >
          <text class="text-gray-500 text-sm mb-6 block"
            >您有一次模拟考试尚未完成，可以选择继续作答或重新开始</text
          >

          <view class="space-y-3">
            <view>
              <button
                class="w-full bg-primary-500 text-white py-3 rounded-lg text-sm font-medium flex items-center justify-center"
                @click="continueExam"
              >
                <i class="fas fa-play mr-2"></i>继续考试
              </button>
            </view>
            <view>
              <button
                class="w-full border border-solid border-gray-300 bg-white text-gray-600 py-3 rounded-lg text-sm font-medium flex items-center justify-center"
                @click="resetExam"
              >
                <i class="fas fa-redo mr-2"></i>重新开始
              </button>
            </view>
          </view>
        </view>
      </view>
      <!-- 不存在未完成考试，显示规则设置 -->
      <view v-else class="mx-4 bg-white rounded-xl shadow-sm p-4">
        <!-- 题型配置 -->
        <view class="mb-5">
          <!-- 题型配置标题 -->
          <view class="flex items-center mb-3">
            <i class="fas fa-list-alt text-primary-500 mr-2"></i>
            <text class="text-base font-semibold text-gray-800">题型配置</text>
          </view>

          <!-- 题型列表 -->
          <block v-for="(rule, index) in questionTypeRules" :key="rule.type">
            <view class="mb-3 bg-gray-50 rounded-lg p-3">
              <!-- 题型标题 -->
              <view class="flex items-center justify-between mb-2">
                <text class="text-base font-medium text-gray-800">{{
                  rule.label
                }}</text>
                <text class="text-sm text-gray-500"
                  >共{{ rule.questionCount }}题</text
                >
              </view>

              <!-- 设置项 -->
              <view class="flex items-center space-x-3">
                <!-- 出题数量 -->
                <view class="flex-1">
                  <text class="text-sm text-gray-600 mb-1 block">出题数量</text>
                  <view
                    class="flex items-center bg-white rounded-lg border border-solid border-gray-200 p-1"
                  >
                    <button
                      @click="decreaseCount(index)"
                      class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500"
                    >
                      <i class="fas fa-minus text-sm"></i>
                    </button>
                    <input
                      type="number"
                      v-model.number="rule.count"
                      min="0"
                      :max="rule.questionCount"
                      class="flex-1 h-auto px-2 py-1 text-center text-base border-0 bg-transparent"
                      readonly
                    />
                    <button
                      @click="increaseCount(index)"
                      class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500"
                    >
                      <i class="fas fa-plus text-sm"></i>
                    </button>
                  </view>
                </view>

                <!-- 每题分值 -->
                <view class="flex-1">
                  <text class="text-sm text-gray-600 mb-1 block">每题分值</text>
                  <view
                    class="flex items-center bg-white rounded-lg border border-solid border-gray-200 p-1"
                  >
                    <button
                      @click="decreaseScore(index)"
                      class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500"
                    >
                      <i class="fas fa-minus text-sm"></i>
                    </button>
                    <input
                      type="text"
                      v-model="rule.score"
                      @blur="onScoreBlur(index, $event)"
                      class="w-12 h-auto px-2 py-1 text-center text-base border-0 bg-transparent"
                      placeholder="1.0"
                    />
                    <button
                      @click="increaseScore(index)"
                      class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500"
                    >
                      <i class="fas fa-plus text-sm"></i>
                    </button>
                  </view>
                </view>
              </view>
            </view>
          </block>
        </view>

        <!-- 及格分数 -->
        <view class="mb-4">
          <view class="flex items-center justify-between">
            <view class="flex items-center">
              <text class="text-base font-semibold text-gray-800"
                >及格分数</text
              >
              <text class="text-sm text-gray-500 ml-2"
                >（总分{{ totalScore }}分）</text
              >
            </view>
            <view
              class="flex items-center bg-gray-50 rounded-lg border border-solid border-gray-200 p-1"
            >
              <button
                @click="decreasePassScore()"
                class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500"
              >
                <i class="fas fa-minus text-sm"></i>
              </button>
              <input
                type="text"
                v-model="passScore"
                @blur="onPassScoreBlur($event)"
                class="w-16 h-auto px-2 py-1 text-center text-base border-0 bg-transparent"
                placeholder="60"
              />
              <button
                @click="increasePassScore()"
                class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500"
              >
                <i class="fas fa-plus text-sm"></i>
              </button>
            </view>
          </view>
        </view>

        <!-- 考试时长 -->
        <view class="mb-4">
          <view class="flex items-center justify-between">
            <text class="text-base font-semibold text-gray-800">考试时长</text>
            <view
              class="flex items-center bg-gray-50 rounded-lg border border-solid border-gray-200 p-1"
            >
              <button
                @click="decreaseDuration()"
                class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500"
              >
                <i class="fas fa-minus text-sm"></i>
              </button>
              <input
                type="number"
                v-model.number="duration"
                min="1"
                max="480"
                step="1"
                @input="validateDuration"
                class="w-16 h-auto px-2 py-1 text-center text-base border-0 bg-transparent"
              />
              <text class="text-sm text-gray-500 mr-2">分钟</text>
              <button
                @click="increaseDuration()"
                class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500"
              >
                <i class="fas fa-plus text-sm"></i>
              </button>
            </view>
          </view>
        </view>

        <!-- 选项乱序 -->
        <view class="mb-4">
          <view class="flex items-center justify-between">
            <text class="text-base font-semibold text-gray-800">选项乱序</text>
            <view class="flex items-center">
              <switch
                :checked="optionDisorder"
                @change="(e) => (optionDisorder = e.detail.value)"
                color="#3b82f6"
                class="mr-3"
              />
              <text class="text-sm text-gray-500">{{
                optionDisorder ? "开启" : "关闭"
              }}</text>
            </view>
          </view>
        </view>

        <!-- 开始考试按钮 -->
        <button
          class="w-full bg-primary-500 text-white py-3 rounded-lg text-sm font-medium flex items-center justify-center"
          @click="startExam"
        >
          <i class="fas fa-play mr-2"></i>开始考试
        </button>
      </view>
    </view>

    <!-- 右下角固定历史记录按钮 -->
    <view class="fixed bottom-6 right-6 z-50">
      <button
        class="w-12 h-12 rounded-full bg-primary-500 text-white shadow-lg flex items-center justify-center"
        @click="goHistory"
      >
        <i class="fas fa-history text-xl"></i>
      </button>
      <!-- 小红点提示（可选） -->
      <view
        v-if="hasNewHistory"
        class="absolute -top-1 -right-1 w-5 h-5 rounded-full bg-red-500 flex items-center justify-center"
      >
        <text class="text-white text-xs font-bold">!</text>
      </view>
    </view>
  </view>
</template>

<script>
import { questionTypeEnum } from "@/myjs/question.js";

export default {
  data() {
    return {
      navBarButtonStyle: {},
      navBarStyle: {}, // 新增导航栏样式
      backButtonStyle: {}, // 返回按钮样式
      mainContentStyle: {}, // 主体内容样式
      hasUnfinishedExam: true,
      hasNewHistory: false, // 是否有新的历史记录提示
      questionTypeRules: [],
      passScore: 60,
      duration: 90,
      optionDisorder: false,
      loading: true,
    };
  },
  computed: {
    totalScore() {
      return this.questionTypeRules.reduce(
        (sum, r) => sum + r.count * parseFloat(r.score || 0),
        0
      );
    },
  },
  methods: {
    setNavBarButtonStyle() {
      let style = {};
      // #ifdef MP-WEIXIN
      const menuButton = uni.getMenuButtonBoundingClientRect();
      style = {
        position: "absolute",
        left: "16px",
        top: menuButton.top + "px",
        width: menuButton.height + "px",
        height: menuButton.height + "px",
        "z-index": 20,
      };
      // #endif
      // #ifdef H5
      style = {
        position: "absolute",
        left: "16px",
        top: "16px",
        width: "40px",
        height: "40px",
        "z-index": 20,
      };
      // #endif
      this.navBarButtonStyle = style;
    },
    setNavBarStyle() {
      let style = {};
      // #ifdef MP-WEIXIN
      const sys = uni.getSystemInfoSync();

      const navTop = sys.statusBarHeight;
      style = {
        "padding-top": navTop + "px",
      };
      // #endif
      // #ifdef H5
      style = {
        "padding-top": "16px",
      };
      // #endif
      this.navBarStyle = style;
    },
    setBackButtonStyle() {
      let style = {};
      // #ifdef MP-WEIXIN
      const menuButton = uni.getMenuButtonBoundingClientRect();
      const buttonSize = menuButton.height;
      style = {
        width: buttonSize + "px",
        height: buttonSize + "px",
      };
      // #endif
      // #ifdef H5
      style = {
        width: "36px",
        height: "36px",
      };
      // #endif
      this.backButtonStyle = style;
    },
    setMainContentStyle() {
      let style = {};
      // #ifdef MP-WEIXIN
      const sys = uni.getSystemInfoSync();
      const menuButton = uni.getMenuButtonBoundingClientRect();
      // 只计算导航栏高度，不加多余间距
      const navHeight = menuButton.top + menuButton.height;
      style = {
        "padding-top": navHeight + "px",
      };
      // #endif
      // #ifdef H5
      const statusBarHeight = 20;
      style = {
        "padding-top": statusBarHeight + "px",
      };
      // #endif
      this.mainContentStyle = style;
    },
    goBack() {
      uni.navigateBack();
    },

    continueExam() {
      this._r = true;
      uni.navigateTo({
        url: "/pages/practice/do?mode=mock&mockId=" + this.mockExamId,
      });
    },
    resetExam() {
      this.hasUnfinishedExam = false;
      this.mockExamId = "";
      this.loadQuestionTypeRules();
    },
    startExam() {
      const questionTypeRule = this.questionTypeRules.find(
        (item) => item.count > 0
      );
      if (!questionTypeRule) {
        uni.showToast({ title: "请至少设置一种题型的出题数量", icon: "none" });
        return;
      }
      console.log(this.parseScore(this.passScore), this.totalScore);
      if (this.passScore > this.totalScore) {
        uni.showToast({ title: "及格分不能超过总分", icon: "none" });
        return;
      }
      if (this.duration < 1 || !Number.isInteger(this.duration)) {
        uni.showToast({ title: "考试时长需为正整数", icon: "none" });
        return;
      }
      if (this.duration > 480) {
        uni.showToast({ title: "考试时长不能超过480分钟", icon: "none" });
        return;
      }
      // 验证分数格式
      for (let rule of this.questionTypeRules) {
        if (rule.count > 0 && rule.score < 0.1) {
          uni.showToast({ title: "分数不能小于0.1", icon: "none" });
          return;
        }
      }

      this.$reqPost(
        "/front/edu/user-answer/createMockExam/" + this.bankId,
        {
          passScore: this.passScore,
          duration: this.duration,
          optionDisorder: this.optionDisorder,
          questionTypeRules: this.questionTypeRules,
        },
        true,
        "创建中..."
      ).then((res) => {
        if (res.success) {
          this.mockExamId = res.data.id;
          this.continueExam();
        } else {
          uni.showToast({
            title: res.errorMessage || "创建失败",
            icon: "none",
          });
        }
      });
    },
    goHistory() {
      uni.navigateTo({
        url: "/pages/practice/mock-history?bankId=" + this.bankId,
      });
    },

    // +/- 按钮方法
    decreaseCount(index) {
      if (this.questionTypeRules[index].count > 0) {
        this.questionTypeRules[index].count--;
      }
    },
    increaseCount(index) {
      if (
        this.questionTypeRules[index].count <
        this.questionTypeRules[index].questionCount
      ) {
        this.questionTypeRules[index].count++;
      }
    },
    decreaseScore(index) {
      const currentScore =
        parseFloat(this.questionTypeRules[index].score) || 0.5;
      if (currentScore > 0.5) {
        const newScore = Math.round((currentScore - 0.5) * 10) / 10;
        this.questionTypeRules[index].score = newScore.toFixed(1);
      }
    },
    increaseScore(index) {
      const currentScore = parseFloat(this.questionTypeRules[index].score) || 0;
      const newScore = Math.round((currentScore + 0.5) * 10) / 10;
      this.questionTypeRules[index].score = newScore.toFixed(1);
    },
    decreasePassScore() {
      if (this.passScore > 1) {
        this.passScore = this.passScore - 1;
      }
    },
    increasePassScore() {
      if (this.passScore < this.totalScore) {
        this.passScore = this.passScore + 1;
      }
    },
    decreaseDuration() {
      if (this.duration > 1) {
        this.duration--;
      }
    },
    increaseDuration() {
      if (this.duration < 480) {
        this.duration++;
      }
    },

    // 解析并限制分数格式
    parseScore(value) {
      // 移除非数字和小数点的字符
      value = value.replace(/[^\d.]/g, "");

      // 确保只有一个小数点
      const parts = value.split(".");
      if (parts.length > 2) {
        value = parts[0] + "." + parts.slice(1).join("");
      }

      // 限制小数位数为1位
      if (parts.length === 2 && parts[1].length > 1) {
        value = parts[0] + "." + parts[1].substring(0, 1);
      }

      const numValue = parseFloat(value);
      if (isNaN(numValue)) return 0.5;
      if (numValue < 0.5) return 0.5;

      return Math.round(numValue * 10) / 10;
    },

    // 题型分数失焦处理
    onScoreBlur(index, event) {
      // 失焦时进行格式化和验证
      const value = this.questionTypeRules[index].score;
      const formattedScore = this.parseScore(value.toString());
      // 统一显示为一位小数格式
      this.questionTypeRules[index].score = formattedScore.toFixed(1);
    },

    // 及格分数失焦处理
    onPassScoreBlur(event) {
      // 失焦时进行格式化和验证
      const value = this.passScore;
      let score = this.parseScore(value.toString());
      if (score > this.totalScore) {
        score = this.totalScore;
      }
      this.passScore = score;
    },

    // 时长验证方法
    validateDuration() {
      this.$nextTick(() => {
        if (this.duration < 1) {
          this.duration = 1;
        } else if (this.duration > 480) {
          this.duration = 480;
        }
      });
    },
    loadExistExam() {
      this.loading = true;
      this.$reqGet(`/front/edu/user-answer/existsMockExam/${this.bankId}`).then(
        (res) => {
          if (res.success) {
            this.hasUnfinishedExam = res.data.exists;
            this.mockExamId = res.data.id;
            if (!this.hasUnfinishedExam) {
              this.loadQuestionTypeRules();
            }
          } else {
            this.hasUnfinishedExam = false;
            this.loadQuestionTypeRules();
            this.mockExamId = "";
          }
          this.loading = false;
        }
      );
    },
    loadQuestionTypeRules() {
      this.$reqGet(`/front/edu/qbank/questionTypeList/${this.bankId}`).then(
        (res) => {
          if (res.success) {
            this.questionTypeRules = (res.data || []).map((item) => {
              const match = questionTypeEnum.find((q) => q.value === item.type);
              if (item.questionCount > 200) {
                item.count = 200;
              } else if (item.questionCount > 100) {
                item.count = 100;
              } else if (item.questionCount > 50) {
                item.count = 50;
              } else {
                item.count = item.questionCount;
              }
              item.score = "1.0";

              return {
                ...item,
                label: match ? match.label : item.label,
              };
            });
            const totalScore = this.questionTypeRules.reduce(
              (sum, r) => sum + r.count * parseFloat(r.score || 0),
              0
            );
            this.passScore =
              (totalScore ? Math.round(totalScore * 0.6) : 0) + "";
          }
        }
      );
    },
  },
  onLoad(options) {
    this.setNavBarButtonStyle();
    this.setNavBarStyle();
    this.setBackButtonStyle();
    this.setMainContentStyle();
    this.bankId = options.bankId || "";
    this.loadExistExam();
  },
  onShow() {
    if (this._r) {
      this.loadExistExam();
      this._r = false;
    }
  },
};
</script>
