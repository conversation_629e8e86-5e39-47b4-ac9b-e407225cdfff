"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      bankId: "",
      showPasswordModal: false,
      passwordInput: "",
      questionBank: {
        isMember: false,
        needPassword: false,
        id: "1",
        name: "计算机基础知识",
        description: "涵盖计算机组成原理、操作系统、数据结构等基础知识",
        questionCount: 156,
        memberCount: 89,
        chapterCount: 12,
        rating: "4.8"
      },
      practiceMode: [
        {
          id: "chapter",
          name: "章节练习",
          description: "按章节分类练习",
          icon: "fas fa-list",
          bgColor: "bg-blue-500"
        },
        {
          id: "sequence",
          name: "顺序练习",
          description: "按题目顺序练习",
          icon: "fas fa-sort-numeric-up",
          bgColor: "bg-green-500"
        },
        {
          id: "type",
          name: "题型练习",
          description: "按题型分类练习",
          icon: "fas fa-tags",
          bgColor: "bg-purple-500"
        },
        {
          id: "exam",
          name: "模拟考试",
          description: "模拟真实考试",
          icon: "fas fa-clipboard-check",
          bgColor: "bg-red-500"
        },
        {
          id: "random",
          name: "随机练习",
          description: "随机抽取题目",
          icon: "fas fa-random",
          bgColor: "bg-orange-500"
        }
      ],
      myRecords: [
        {
          id: "wrong",
          name: "我的错题",
          count: "12题",
          icon: "fas fa-times-circle",
          bgColor: "bg-red-500"
        },
        {
          id: "favorite",
          name: "我的收藏",
          count: "8题",
          icon: "fas fa-heart",
          bgColor: "bg-pink-500"
        }
      ]
    };
  },
  onLoad(options) {
    if (options.bankId) {
      this.bankId = options.bankId;
      this.loadQuestionBankDetail();
    }
  },
  methods: {
    loadQuestionBankDetail() {
      this.$reqGet("/front/edu-personal/bankHome", {
        id: this.bankId
      }).then((res) => {
        if (res.success) {
          this.questionBank = res.data;
        }
      });
    },
    startPractice(mode) {
      if (mode.id == "chapter") {
        common_vendor.index.navigateTo({
          url: `/pages/practice/chapter?bankId=${this.bankId}`
        });
      } else if (mode.id == "exam") {
        common_vendor.index.navigateTo({
          url: `/pages/practice/mock-exam?bankId=${this.bankId}`
        });
      } else if (mode.id == "type") {
        common_vendor.index.navigateTo({
          url: `/pages/practice/questionType?bankId=${this.bankId}`
        });
      }
    },
    goToRecord(record) {
      switch (record.id) {
        case "wrong":
          common_vendor.index.navigateTo({
            url: `/pages/practice/wrongQuestion?bankId=${this.bankId}`
          });
          break;
        case "favorite":
          common_vendor.index.navigateTo({
            url: `/pages/practice/collectionQuestion?bankId=${this.bankId}`
          });
          break;
      }
    },
    joinQuestionBank() {
      if (this.questionBank.needPassword) {
        this.showPasswordModal = true;
        this.passwordInput = "";
      } else {
        common_vendor.index.showModal({
          title: "提示",
          content: "确认将此题库加入我的题库吗？",
          success: (res) => {
            if (res.confirm) {
              this.requestJoinBank();
            }
          }
        });
      }
    },
    closePasswordModal() {
      this.showPasswordModal = false;
      this.passwordInput = "";
    },
    confirmPassword() {
      if (!this.passwordInput.trim()) {
        common_vendor.index.showToast({
          title: "请输入密码",
          icon: "none"
        });
        return;
      }
      this.requestJoinBank(this.passwordInput);
      this.closePasswordModal();
    },
    requestJoinBank(password = "") {
      const params = {
        id: this.bankId
      };
      if (password) {
        params.pwd = password;
      }
      this.$reqPost("/front/edu-personal/joinBank", params).then((res) => {
        if (res.success) {
          this.questionBank.isMember = true;
          common_vendor.index.showToast({
            title: "加入成功",
            icon: "success"
          });
        } else {
          common_vendor.index.showToast({
            title: res.errorMessage || "加入失败",
            icon: "error"
          });
        }
      }).catch((err) => {
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "error"
        });
      });
    },
    shareQuestionBank() {
      common_vendor.index.showActionSheet({
        itemList: ["微信好友", "朋友圈", "复制链接"],
        success: (res) => {
          common_vendor.index.__f__("log", "at pages/questionbank/detail.vue:348", "分享方式:", res.tapIndex);
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.questionBank.name),
    b: common_vendor.t($data.questionBank.description),
    c: common_vendor.t($data.questionBank.questionCount),
    d: common_vendor.t($data.questionBank.memberCount),
    e: common_vendor.t($data.questionBank.chapterCount),
    f: common_vendor.t($data.questionBank.rating || "--"),
    g: common_vendor.f($data.practiceMode, (mode, k0, i0) => {
      return {
        a: common_vendor.n(mode.icon + " text-white text-lg"),
        b: common_vendor.n(mode.bgColor),
        c: common_vendor.t(mode.name),
        d: common_vendor.t(mode.description),
        e: mode.id,
        f: common_vendor.o(($event) => $options.startPractice(mode), mode.id)
      };
    }),
    h: common_vendor.f($data.myRecords, (record, k0, i0) => {
      return {
        a: common_vendor.n(record.icon + " text-white text-lg"),
        b: common_vendor.n(record.bgColor),
        c: common_vendor.t(record.name),
        d: common_vendor.t(record.count),
        e: record.id,
        f: common_vendor.o(($event) => $options.goToRecord(record), record.id)
      };
    }),
    i: !$data.questionBank.isMember
  }, !$data.questionBank.isMember ? {} : {}, {
    j: common_vendor.o((...args) => $options.shareQuestionBank && $options.shareQuestionBank(...args)),
    k: common_vendor.n($data.questionBank.isMember ? "w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center" : "flex flex-col items-center justify-center w-16 h-16 bg-gray-100 rounded-lg"),
    l: !$data.questionBank.isMember
  }, !$data.questionBank.isMember ? {
    m: common_vendor.o((...args) => $options.joinQuestionBank && $options.joinQuestionBank(...args))
  } : {}, {
    n: $data.showPasswordModal
  }, $data.showPasswordModal ? {
    o: common_vendor.o((...args) => $options.closePasswordModal && $options.closePasswordModal(...args)),
    p: common_vendor.o((...args) => $options.confirmPassword && $options.confirmPassword(...args)),
    q: $data.passwordInput,
    r: common_vendor.o(($event) => $data.passwordInput = $event.detail.value),
    s: common_vendor.o((...args) => $options.confirmPassword && $options.confirmPassword(...args)),
    t: common_vendor.o(() => {
    }),
    v: common_vendor.o((...args) => $options.closePasswordModal && $options.closePasswordModal(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/questionbank/detail.js.map
