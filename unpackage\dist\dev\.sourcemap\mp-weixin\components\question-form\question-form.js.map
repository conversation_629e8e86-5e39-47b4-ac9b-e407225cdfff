{"version": 3, "file": "question-form.js", "sources": ["components/question-form/question-form.vue", "D:/Program Files/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RTovd29yay9jb2RlL2VkdS9lZHUtcGVyc29uYWwtdW5pYXBwL2NvbXBvbmVudHMvcXVlc3Rpb24tZm9ybS9xdWVzdGlvbi1mb3JtLnZ1ZQ"], "sourcesContent": ["<template>\r\n   \r\n     \r\n      \r\n      <!-- 表单内容 -->\r\n \r\n        <view class=\"bg-white rounded-xl p-5 shadow-sm\">\r\n          <!-- 章节选择 -->\r\n          <view class=\"mb-5\">\r\n            <view class=\"mb-2  \">\r\n              <text class=\"text-sm font-medium text-gray-700\">章节</text>\r\n            \r\n            </view>\r\n            <question-chapter-picker v-if=\"!chapterReadOnly\" v-model=\"questionForm.chapterId\" :bankId=\"bankId\"    />\r\n            <view v-else class=\"text-sm text-gray-500\">{{ questionForm.chapterFullName||'--' }}</view>\r\n          </view>\r\n          \r\n          <!-- 题型选择 -->\r\n          <view class=\"mb-5\">\r\n            <view class=\"mb-2\">\r\n              <text class=\"text-sm font-medium text-gray-700\">题型 <text class=\"text-red-500\">*</text></text>\r\n            </view>\r\n            <radio-group @change=\"onQuestionTypeChange\" :disabled=\"isEdit\">\r\n              <view class=\"flex flex-wrap gap-3\">\r\n                <view v-for=\"questionType in questionTypes\" :key=\"questionType.id\" class=\"flex items-center py-1 px-1 rounded-lg\"  >\r\n                  <radio   :value=\"questionType.id+''\" :checked=\"questionForm.questionType == questionType.id\"  >\r\n                  <text class=\"ml-1 text-gray-800\">{{ questionType.name }}</text>\r\n                  </radio>\r\n                </view>\r\n              </view>\r\n            </radio-group>\r\n       \r\n          </view>\r\n          \r\n          <!-- 难度选择 -->\r\n          <view class=\"mb-5\">\r\n            <view class=\"mb-2\">\r\n              <text class=\"text-sm font-medium text-gray-700\">难度 <text class=\"text-red-500\">*</text></text>\r\n            </view>\r\n             \r\n            <radio-group @change=\"onDifficultyChange\">\r\n              <view class=\"flex flex-wrap gap-3\">\r\n                <view v-for=\"difficulty in difficulties\" :key=\"difficulty.id\" class=\"flex items-center p-1 rounded-lg\"  >\r\n                  <radio   :value=\"difficulty.id+''\" :checked=\"questionForm.difficulty == difficulty.id\"   >\r\n                  <text class=\"ml-1 text-gray-800\">{{ difficulty.name }}</text>\r\n                  </radio>\r\n                </view>\r\n              </view>\r\n            </radio-group>\r\n          </view>\r\n          \r\n          <!-- 题干内容 -->\r\n          <view class=\"mb-5\">\r\n            <view class=\"mb-2\">\r\n              <text class=\"text-sm font-medium text-gray-700\">题干 <text class=\"text-red-500\">*</text></text>\r\n            </view>\r\n            <textarea \r\n              v-model=\"questionForm.questionContent\"\r\n              placeholder=\"请输入题目内容...\"\r\n              class=\"w-full px-4 py-3 h-40 border border-solid border-gray-300 rounded-lg text-sm\"\r\n            ></textarea>\r\n          </view>\r\n          \r\n          <!-- 选项设置 (仅单选和多选题) -->\r\n          <view v-if=\"isChoiceQuestion\" class=\"mb-5\">\r\n            <view class=\"mb-3\">\r\n              <text class=\"text-sm font-medium text-gray-700\">选项设置 <text class=\"text-red-500\">*</text></text>\r\n            </view>\r\n            \r\n            <!-- 选项内容 -->\r\n            <view v-for=\"(option, index) in questionForm.options\" :key=\"index\" class=\"mb-3 bg-gray-50 rounded-lg p-3\">\r\n              <view class=\"flex items-center justify-between mb-2\">\r\n                <text class=\"text-sm font-medium\">选项 {{ String.fromCharCode(65 + index) }}</text>\r\n                <view>\r\n                  <button \r\n                  v-if=\"questionForm.options.length > 2\"\r\n                  @click=\"removeOption(index)\" \r\n                  class=\"h-6 w-6 bg-red-100 text-red-500 text-xs rounded-full flex items-center justify-center p-0\"\r\n                >\r\n                  <text class=\"fas fa-times\"></text>\r\n                </button>\r\n                </view>\r\n              </view>\r\n              \r\n              <textarea \r\n                v-model=\"questionForm.options[index]\"\r\n                :placeholder=\"`请输入选项${String.fromCharCode(65 + index)}内容...`\"\r\n                class=\"w-full px-4 py-3 border border-gray-300 border-solid rounded-lg text-sm h-20\"\r\n              ></textarea>\r\n            </view>\r\n            \r\n            <button \r\n                @click=\"addOption\" \r\n                class=\"w-full py-2 mt-2 bg-primary-100 text-primary-600 text-sm rounded-lg flex items-center justify-center\"\r\n              >\r\n                <text class=\"fas fa-plus mr-1.5\"></text>\r\n                <text>添加选项</text>\r\n              </button>\r\n            \r\n            <!-- <view class=\"text-xs text-gray-500 mt-2\">请至少添加2个选项</view> -->\r\n          </view>\r\n  \r\n     \r\n          <view   class=\"mb-5\">\r\n            <view class=\"mb-2\">\r\n              <text class=\"text-sm font-medium text-gray-700\">答案 <text class=\"text-red-500\">*</text></text>\r\n            </view>\r\n            <question-right-answer-edit :questionType=\"questionForm.questionType\" :options=\"questionForm.options\" v-model=\"questionForm.rightAnswer\"></question-right-answer-edit> \r\n          </view>\r\n  \r\n   \r\n          \r\n          <!-- 题目解析 -->\r\n          <view class=\"mb-3\">\r\n            <view class=\"mb-2\">\r\n              <text class=\"text-sm font-medium text-gray-700\">题目解析</text>\r\n            </view>\r\n            <textarea \r\n              v-model=\"questionForm.analysis\"\r\n              placeholder=\"请输入题目解析...\"\r\n              class=\"w-full px-4 py-3 h-32 border border-gray-300 rounded-lg border-solid text-sm\"\r\n            ></textarea>\r\n          </view>\r\n          \r\n          <!-- 提交按钮 -->\r\n   \r\n        </view>\r\n     \r\n   \r\n   \r\n  </template>\r\n  \r\n  <script>\r\n  \r\n  \r\n  export default {\r\n    props:{\r\n      bankId:String,\r\n      chapterReadOnly:Boolean,\r\n      isEdit:Boolean\r\n    },\r\n    data() {\r\n\r\n      return {\r\n      \r\n     \r\n        \r\n        // 用于题型选择的数据\r\n        questionTypes: [\r\n          { id: 1, name: '单选题' },\r\n          { id: 2, name: '多选题' },\r\n          { id: 3, name: '判断题' }\r\n        ],\r\n         \r\n        \r\n        // 用于难度选择的数据\r\n        difficulties: [\r\n          { id: 1, name: '简单' },\r\n          { id: 2, name: '一般' },\r\n          { id: 3, name: '困难' }\r\n        ],\r\n        \r\n    \r\n        // 表单数据\r\n        questionForm: {\r\n          chapterFullName:'',\r\n          chapterId: null,\r\n          questionType: 1, // 1: 单选, 2: 多选, 3: 判断\r\n          questionContent: '',\r\n          options: [\r\n             '',''\r\n          ],\r\n         \r\n          analysis: '',\r\n          difficulty: 2,\r\n          rightAnswer:'',\r\n           \r\n        }\r\n      }\r\n    },\r\n    \r\n    computed: {\r\n      isChoiceQuestion() {\r\n        return this.questionForm.questionType === 1 || this.questionForm.questionType === 2;\r\n      },\r\n    },\r\n    \r\n \r\n \r\n    \r\n    methods: {\r\n        updateFormValue(values){\r\n            this.questionForm = {...this.questionForm,...values};\r\n        },\r\n\r\n        init(){\r\n            this.questionForm.questionType = 1;\r\n            this.questionForm.rightAnswer = '';\r\n            this.questionForm.options = [\r\n              '',''\r\n            ];\r\n            this.questionForm.analysis = '';\r\n            this.questionForm.difficulty = 2;\r\n            this.questionForm.questionContent = '';\r\n            this.questionForm.chapterId = null;\r\n        },\r\n\r\n      // 题型选择相关方法\r\n      onQuestionTypeChange(e) {\r\n        \r\n\r\n        const lastQuestionType =this.questionForm.questionType;\r\n        this.questionForm.questionType =  parseInt(e.detail.value);\r\n        this.questionForm.rightAnswer = '';\r\n        // 重置选项和答案\r\n        if (this.questionForm.questionType === 3) { // 判断题\r\n          this.questionForm.options = [];\r\n         \r\n        } else {\r\n          if (this.questionForm.options.length < 2) {\r\n            if(lastQuestionType===3){\r\n                this.questionForm.options = [\r\n              '',''\r\n            ];\r\n            }\r\n           \r\n          }\r\n        }\r\n      },\r\n      \r\n      // 难度选择相关方法\r\n      onDifficultyChange(e) {\r\n   \r\n        this.questionForm.difficulty = e.detail.value;\r\n      },\r\n      \r\n      // 选项相关方法\r\n      addOption() {\r\n        this.questionForm.options.push('');\r\n      },\r\n      \r\n      removeOption(index) {\r\n        this.questionForm.options.splice(index, 1);\r\n        const rightAnswer = this.questionForm.rightAnswer;\r\n        if(!rightAnswer){\r\n          return;\r\n        }\r\n  \r\n        const questionType =this.questionForm.questionType;\r\n        if(questionType == 1){\r\n          if(rightAnswer!=index){\r\n            this.questionForm.rightAnswer = '';\r\n          }\r\n         \r\n        }else if(questionType == 2){\r\n          this.questionForm.rightAnswer = this.questionForm.rightAnswer.split(',').filter(item=>item!=index).join(',');\r\n        }\r\n      },\r\n      // 验证表单\r\n      validateForm(callback) {\r\n        if (!this.questionForm.questionType) {\r\n          uni.showToast({\r\n            title: '请选择题型',\r\n            icon: 'none'\r\n          });\r\n          return false;\r\n        }\r\n        \r\n        if (!this.questionForm.questionContent.trim()) {\r\n          uni.showToast({\r\n            title: '请输入题干内容',\r\n            icon: 'none'\r\n          });\r\n          return false;\r\n        }\r\n        \r\n        if (this.isChoiceQuestion) {\r\n          // 检查是否至少有2个选项\r\n          if (this.questionForm.options.length < 2) {\r\n            uni.showToast({\r\n              title: '请至少添加2个选项',\r\n              icon: 'none'\r\n            });\r\n            return false;\r\n          }\r\n          \r\n          // 检查是否每个选项都有内容\r\n          for (let i = 0; i < this.questionForm.options.length; i++) {\r\n            if (!this.questionForm.options[i].trim()) {\r\n              uni.showToast({\r\n                title: `请输入选项${String.fromCharCode(65 + i)}内容`,\r\n                icon: 'none'\r\n              });\r\n              return false;\r\n            }\r\n          }\r\n          \r\n          \r\n        }\r\n        \r\n        // 检查是否有正确答案\r\n        if (!this.questionForm.rightAnswer) {\r\n            uni.showToast({\r\n              title: '请选择正确答案',\r\n              icon: 'none'\r\n            });\r\n            return false;\r\n          }\r\n        if(this.questionForm.questionType == 1){\r\n          const rightAnswer =parseInt(this.questionForm.rightAnswer);\r\n          if(rightAnswer<0 || rightAnswer>=this.questionForm.options.length){\r\n            uni.showToast({\r\n              title: '正确答案不在选项范围内',\r\n              icon: 'none'\r\n            });\r\n            return false;\r\n          }\r\n        }\r\n  \r\n        if(this.questionForm.questionType == 2){\r\n          const rightAnswer =this.questionForm.rightAnswer.split(',');\r\n          \r\n  \r\n          if(rightAnswer.length<2){\r\n            uni.showToast({\r\n              title: '请至少选择2个正确答案',\r\n              icon: 'none'\r\n            });\r\n            return false;\r\n          }\r\n  \r\n          for(let i=0;i<rightAnswer.length;i++){\r\n            if(rightAnswer[i]<0 || rightAnswer[i]>=this.questionForm.options.length){\r\n              uni.showToast({\r\n                title: '正确答案不在选项范围内',\r\n                icon: 'none'  \r\n              });\r\n              return false;\r\n            }\r\n          }\r\n        }\r\n  \r\n  \r\n        \r\n        if (!this.questionForm.difficulty) {\r\n          uni.showToast({\r\n            title: '请选择难度',\r\n            icon: 'none'\r\n          });\r\n          return false;\r\n        }\r\n\r\n        this.questionForm.bankId = this.bankId;\r\n        if(callback){\r\n          callback({...this.questionForm,chapterFullName:undefined});\r\n        }\r\n      },   \r\n    }\r\n  }\r\n  </script>\r\n  \r\n  <style scoped>\r\n  </style>", "import Component from 'E:/work/code/edu/edu-personal-uniapp/components/question-form/question-form.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;AAuIE,MAAK,YAAU;AAAA,EACb,OAAM;AAAA,IACJ,QAAO;AAAA,IACP,iBAAgB;AAAA,IAChB,QAAO;AAAA,EACR;AAAA,EACD,OAAO;AAEL,WAAO;AAAA;AAAA,MAKL,eAAe;AAAA,QACb,EAAE,IAAI,GAAG,MAAM,MAAO;AAAA,QACtB,EAAE,IAAI,GAAG,MAAM,MAAO;AAAA,QACtB,EAAE,IAAI,GAAG,MAAM,MAAM;AAAA,MACtB;AAAA;AAAA,MAID,cAAc;AAAA,QACZ,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,QACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,QACrB,EAAE,IAAI,GAAG,MAAM,KAAK;AAAA,MACrB;AAAA;AAAA,MAID,cAAc;AAAA,QACZ,iBAAgB;AAAA,QAChB,WAAW;AAAA,QACX,cAAc;AAAA;AAAA,QACd,iBAAiB;AAAA,QACjB,SAAS;AAAA,UACN;AAAA,UAAG;AAAA,QACL;AAAA,QAED,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,aAAY;AAAA,MAEd;AAAA,IACF;AAAA,EACD;AAAA,EAED,UAAU;AAAA,IACR,mBAAmB;AACjB,aAAO,KAAK,aAAa,iBAAiB,KAAK,KAAK,aAAa,iBAAiB;AAAA,IACnF;AAAA,EACF;AAAA,EAKD,SAAS;AAAA,IACL,gBAAgB,QAAO;AACnB,WAAK,eAAe,EAAC,GAAG,KAAK,cAAa,GAAG,OAAM;AAAA,IACtD;AAAA,IAED,OAAM;AACF,WAAK,aAAa,eAAe;AACjC,WAAK,aAAa,cAAc;AAChC,WAAK,aAAa,UAAU;AAAA,QAC1B;AAAA,QAAG;AAAA;AAEL,WAAK,aAAa,WAAW;AAC7B,WAAK,aAAa,aAAa;AAC/B,WAAK,aAAa,kBAAkB;AACpC,WAAK,aAAa,YAAY;AAAA,IACjC;AAAA;AAAA,IAGH,qBAAqB,GAAG;AAGtB,YAAM,mBAAkB,KAAK,aAAa;AAC1C,WAAK,aAAa,eAAgB,SAAS,EAAE,OAAO,KAAK;AACzD,WAAK,aAAa,cAAc;AAEhC,UAAI,KAAK,aAAa,iBAAiB,GAAG;AACxC,aAAK,aAAa,UAAU;aAEvB;AACL,YAAI,KAAK,aAAa,QAAQ,SAAS,GAAG;AACxC,cAAG,qBAAmB,GAAE;AACpB,iBAAK,aAAa,UAAU;AAAA,cAC9B;AAAA,cAAG;AAAA;UAEL;AAAA,QAEF;AAAA,MACF;AAAA,IACD;AAAA;AAAA,IAGD,mBAAmB,GAAG;AAEpB,WAAK,aAAa,aAAa,EAAE,OAAO;AAAA,IACzC;AAAA;AAAA,IAGD,YAAY;AACV,WAAK,aAAa,QAAQ,KAAK,EAAE;AAAA,IAClC;AAAA,IAED,aAAa,OAAO;AAClB,WAAK,aAAa,QAAQ,OAAO,OAAO,CAAC;AACzC,YAAM,cAAc,KAAK,aAAa;AACtC,UAAG,CAAC,aAAY;AACd;AAAA,MACF;AAEA,YAAM,eAAc,KAAK,aAAa;AACtC,UAAG,gBAAgB,GAAE;AACnB,YAAG,eAAa,OAAM;AACpB,eAAK,aAAa,cAAc;AAAA,QAClC;AAAA,iBAEO,gBAAgB,GAAE;AACzB,aAAK,aAAa,cAAc,KAAK,aAAa,YAAY,MAAM,GAAG,EAAE,OAAO,UAAM,QAAM,KAAK,EAAE,KAAK,GAAG;AAAA,MAC7G;AAAA,IACD;AAAA;AAAA,IAED,aAAa,UAAU;AACrB,UAAI,CAAC,KAAK,aAAa,cAAc;AACnCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,KAAK,aAAa,gBAAgB,KAAI,GAAI;AAC7CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,UAAI,KAAK,kBAAkB;AAEzB,YAAI,KAAK,aAAa,QAAQ,SAAS,GAAG;AACxCA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AACD,iBAAO;AAAA,QACT;AAGA,iBAAS,IAAI,GAAG,IAAI,KAAK,aAAa,QAAQ,QAAQ,KAAK;AACzD,cAAI,CAAC,KAAK,aAAa,QAAQ,CAAC,EAAE,QAAQ;AACxCA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO,QAAQ,OAAO,aAAa,KAAK,CAAC,CAAC;AAAA,cAC1C,MAAM;AAAA,YACR,CAAC;AACD,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MAGF;AAGA,UAAI,CAAC,KAAK,aAAa,aAAa;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AACF,UAAG,KAAK,aAAa,gBAAgB,GAAE;AACrC,cAAM,cAAa,SAAS,KAAK,aAAa,WAAW;AACzD,YAAG,cAAY,KAAK,eAAa,KAAK,aAAa,QAAQ,QAAO;AAChEA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AACD,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,UAAG,KAAK,aAAa,gBAAgB,GAAE;AACrC,cAAM,cAAa,KAAK,aAAa,YAAY,MAAM,GAAG;AAG1D,YAAG,YAAY,SAAO,GAAE;AACtBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AACD,iBAAO;AAAA,QACT;AAEA,iBAAQ,IAAE,GAAE,IAAE,YAAY,QAAO,KAAI;AACnC,cAAG,YAAY,CAAC,IAAE,KAAK,YAAY,CAAC,KAAG,KAAK,aAAa,QAAQ,QAAO;AACtEA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AACD,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAIA,UAAI,CAAC,KAAK,aAAa,YAAY;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,WAAK,aAAa,SAAS,KAAK;AAChC,UAAG,UAAS;AACV,iBAAS,EAAC,GAAG,KAAK,cAAa,iBAAgB,OAAS,CAAC;AAAA,MAC3D;AAAA,IACD;AAAA,EACH;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrWF,GAAG,gBAAgB,SAAS;"}