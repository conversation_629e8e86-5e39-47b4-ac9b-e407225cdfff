<template>
  <view class="min-h-screen bg-gray-50">
   
    
    <!-- 表单内容 -->
    <view class="px-4 pt-2 pb-10">
      <question-form :bankId="bankId" ref="questionForm" />
    </view>

    <view class="h-11"></view>
    <!-- 底部保存按钮 -->
    <view class="fixed bottom-0 left-0 right-0 bg-white p-4 border-t border-gray-200">
      <button @click="saveQuestion" class="w-full h-auto py-3 text-sm bg-primary-500 text-white rounded-lg shadow-sm font-medium  ">
          保存题目
        </button>
    </view>
      <!--安全距离-->
  </view>
</template>

<script>


export default {
  data() {
    return {
    
      bankId:null,
     
    }
  },
  
  
  
  onLoad(options) {
    this.bankId = options.bankId;
 
  },
 
  
  methods: {
    
    
    // 保存题目
    saveQuestion() {

      this.$refs.questionForm.validateForm((formValues)=>{
        this.$reqPost('/front/edu-personal/question/add',formValues,true,'保存中...').then(res=>{
        if(res.success){
          uni.showModal({
            title: '提示',
            content: '保存成功',
            confirmText: '继续添加',
            cancelText: '返回',
            success: (res) => {
              if (!res.confirm) {
                uni.navigateBack();
              }else{
                this.$refs.questionForm.init();
              }
            }
          })
        }else{
          uni.showToast({
            title: res.errorMessage,
            icon: 'none'
          });
        }
      })
      })
    

 


  
    },
    
 
  }
}
</script>

<style scoped>
</style>