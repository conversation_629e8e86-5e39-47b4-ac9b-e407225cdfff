<template>
  <view>
    <view 
            class="flex items-center justify-between px-4 py-3 border border-solid border-gray-300 rounded-lg bg-white"
            @click="showChapterPicker"
          >
            <text class="text-sm" :class="selectedChapterFullName  ? 'text-gray-800' : 'text-gray-400'">
              {{ selectedChapterFullName  || '请选择章节' }}
            </text>
            <text class="fas fa-chevron-right text-gray-400 text-xs"></text>
          </view>

              <!-- 章节选择弹出层 -->
    <view v-if="showChapterPickerModal" class="fixed inset-0 z-50">
      <view class="absolute inset-0 bg-black bg-opacity-50" @click="closeChapterPicker"></view>
      <view class="absolute bottom-0 left-0 right-0 bg-white rounded-t-xl overflow-hidden">
        <view class="px-6 py-3 border-b border-gray-100 flex justify-between items-center">
          <view class="flex items-center">
            <text class="text-lg font-medium text-gray-800">选择章节</text>
            <button @click="navigateToChapterManagement" v-if="chapters.length > 0" class="ml-2 bg-primary-100 text-primary-600 rounded-full px-2 py-1 flex items-center justify-center text-xs hover:bg-primary-200 transition-colors">
              <text class="fas fa-edit mr-1"></text>
              <text>章节管理</text>
            </button>
          </view>
          <view class="text-gray-400   text-xl px-2  " @click="closeChapterPicker">
						<text class="fas fa-close"></text>
					</view>
          
          

          
          
        </view>
        
        <scroll-view scroll-y="true" :style="{height: '400px'}" class="px-4 pt-2 pb-6">
      
            <!-- 空章节提示 -->
            <view v-if="chapters.length === 0" class="flex flex-col items-center justify-center py-16">
              <view class="text-gray-400 mb-4">
                <text class="fas fa-folder-open text-5xl"></text>
              </view>
              <text class="text-gray-500 mb-4">暂无可用章节</text>
              <button @click="navigateToChapterManagement" class="bg-primary-500 text-white rounded-lg px-4 text-sm py-2 flex items-center">
                <text class="fas fa-plus mr-2"></text>
                <text>去章节管理</text>
              </button>
            </view>
            
            <!-- 有章节时显示列表 -->
            <block v-else>
              <!-- 不设置章节选项 -->
              <view class="mb-3">
                <view 
                  class="flex items-center py-3 px-2 bg-gray-50 rounded-lg active:bg-gray-100 border-dashed border" 
                  :class="[selectedChapterId ==0 ? 'bg-primary-50 border-primary-100' : 'border-gray-300']"
                  @click="selectNoChapter"
                >
                  <radio value="" :checked="selectedChapterId ==0"  />
                  <text class="ml-2 text-gray-600 flex-1">不设置章节</text>
                </view>
              </view>
            
              <!-- 一级章节 -->
              <view v-for="(chapter) in chapters" :key="chapter.value" class="mb-2">
                <view 
                  class="flex items-center py-3 px-2 bg-gray-50 rounded-lg  " 
                  :class="[chapter.children && chapter.children.length ? 'mb-2' : '', selectedChapterId == chapter.value ? 'bg-primary-50 ' : '']"
                  @click="selectChapter(chapter)"
                >
                  <radio :value="chapter.value" v-if="!chapter.children || !chapter.children.length" :checked="selectedChapterId == chapter.value"  />
                  <text class="ml-2 text-gray-800 flex-1">{{ chapter.label }}</text>
                </view>
                
                <!-- 二级章节 -->
                <view v-if="chapter.children && chapter.children.length" class="pl-8">
                  <view 
                    v-for="child in chapter.children" 
                    :key="child.value"
                    class="flex items-center py-2 px-2 mb-1 rounded-lg active:bg-gray-100"
                    :class="selectedChapterId == child.value ? 'bg-primary-50 border border-primary-100' : ''"
                    @click="selectChapter(child)"
                  >
                    <radio :value="child.value" :checked="selectedChapterId == child.value"  />
                    <text class="ml-2 text-gray-800 flex-1">{{ child.label }}</text>
                  </view>
                </view>
              </view>
            </block>
        </scroll-view>
        
        <view class="p-4 border-t border-gray-100">
          <button @click="confirmChapterSelection" class="w-full py-3  text-sm bg-primary-500 text-white rounded-lg">
            确认选择
          </button>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  name: 'QuestionChapterPicker',
  props: {
    bankId: {
      type: String,
      required: false
    },
    modelValue: {
      type: String,
      required: false
    },
    reloadChapter: {
      type: Boolean,
      default: false
    }
  },
  data(){
    return {
      chapters:[],
      selectedChapterId:null,
      showChapterPickerModal:false,
      pageShowListener: null
    }
  },
  watch: {
    reloadChapter(newVal){
      if(newVal){
        this.loadChapters();
      }
    }
  },
  computed:{
    selectedChapterFullName(){
        const selectedChapterId = this.modelValue;
        console.log(selectedChapterId)
      // 如果是空值，表示不设置章节
      if (!selectedChapterId) {
        return '';
      }
 
      let fullName = '';
      this.chapters.forEach(chapter => {
  
        if(chapter.value == selectedChapterId){
          
            fullName = chapter.label;
            return;
        }
        if(chapter.children && chapter.children.length){
          chapter.children.forEach(child => {
            if(child.value == selectedChapterId){
            
                fullName = chapter.label + ' / ' + child.label;
                return;
            }
          })
        }
      })
      return fullName;
    }
  },
  created(){
    
    
    // 添加页面显示监听
    this.setupPageShowListener();
  },
  mounted(){
    this.loadChapters();
  },
  beforeUnmount() {
    // 组件销毁前移除监听
    this.removePageShowListener();
  },
  methods:{ 
    // 设置页面显示监听
    setupPageShowListener() {
      // 创建监听函数
      this.pageShowListener = () => {
        console.log('页面显示，刷新章节列表');
        this.loadChapters();
      };
      
      // 添加页面显示监听
      uni.$on('refreshChapterList', this.pageShowListener);
    },
    
    // 移除页面显示监听
    removePageShowListener() {
      if (this.pageShowListener) {
        uni.$off('refreshChapterList', this.pageShowListener);
      }
    },
    
    loadChapters(){
      this.$reqGet('/front/edu-personal/chapter/enumList/'+this.bankId).then((res) => {
        this.chapters = res.data || [];
      })
    },
    showChapterPicker(){
        this.selectedChapterId = this.modelValue;
      this.showChapterPickerModal = true;
    },
    
    closeChapterPicker(){
      this.showChapterPickerModal = false;
      // 取消选择时重置临时选择
      this.selectedChapterId = this.modelValue;
    },
    
    selectChapter(chapter){
        if(chapter.children && chapter.children.length){
            return;
        }
      this.selectedChapterId = chapter.value;
    },
    
    selectNoChapter(){
      this.selectedChapterId = '0';
    },
    
    confirmChapterSelection(){
        console.log(this.selectedChapterId)
      this.$emit('update:modelValue', this.selectedChapterId);
      this.showChapterPickerModal = false;
    },
    navigateToChapterManagement(){
       
      uni.navigateTo({
              url: `/pages/questionbank/chapter?bankId=${this.bankId}`,
          
            });
    }
  }
}
</script>