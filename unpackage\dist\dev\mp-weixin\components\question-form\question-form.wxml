<view class="bg-white rounded-xl p-5 shadow-sm"><view class="mb-5"><view class="mb-2"><text class="text-sm font-medium text-gray-700">章节</text></view><question-chapter-picker wx:if="{{a}}" u-i="5f117dcf-0" bind:__l="__l" bindupdateModelValue="{{b}}" u-p="{{c}}"/><view wx:else class="text-sm text-gray-500">{{d}}</view></view><view class="mb-5"><view class="mb-2"><text class="text-sm font-medium text-gray-700">题型 <text class="text-red-500">*</text></text></view><radio-group bindchange="{{f}}" disabled="{{g}}"><view class="flex flex-wrap gap-3"><view wx:for="{{e}}" wx:for-item="questionType" wx:key="d" class="flex items-center py-1 px-1 rounded-lg"><radio value="{{questionType.b}}" checked="{{questionType.c}}"><text class="ml-1 text-gray-800">{{questionType.a}}</text></radio></view></view></radio-group></view><view class="mb-5"><view class="mb-2"><text class="text-sm font-medium text-gray-700">难度 <text class="text-red-500">*</text></text></view><radio-group bindchange="{{i}}"><view class="flex flex-wrap gap-3"><view wx:for="{{h}}" wx:for-item="difficulty" wx:key="d" class="flex items-center p-1 rounded-lg"><radio value="{{difficulty.b}}" checked="{{difficulty.c}}"><text class="ml-1 text-gray-800">{{difficulty.a}}</text></radio></view></view></radio-group></view><view class="mb-5"><view class="mb-2"><text class="text-sm font-medium text-gray-700">题干 <text class="text-red-500">*</text></text></view><block wx:if="{{r0}}"><textarea placeholder="请输入题目内容..." class="w-full px-4 py-3 h-40 border border-solid border-gray-300 rounded-lg text-sm" value="{{j}}" bindinput="{{k}}"></textarea></block></view><view wx:if="{{l}}" class="mb-5"><view class="mb-3"><text class="text-sm font-medium text-gray-700">选项设置 <text class="text-red-500">*</text></text></view><view wx:for="{{m}}" wx:for-item="option" wx:key="f" class="mb-3 bg-gray-50 rounded-lg p-3"><view class="flex items-center justify-between mb-2"><text class="text-sm font-medium">选项 {{option.a}}</text><view><button wx:if="{{n}}" bindtap="{{option.b}}" class="h-6 w-6 bg-red-100 text-red-500 text-xs rounded-full flex items-center justify-center p-0"><text class="fas fa-times"></text></button></view></view><block wx:if="{{r0}}"><textarea placeholder="{{option.c}}" class="w-full px-4 py-3 border border-gray-300 border-solid rounded-lg text-sm h-20" value="{{option.d}}" bindinput="{{option.e}}"></textarea></block></view><button bindtap="{{o}}" class="w-full py-2 mt-2 bg-primary-100 text-primary-600 text-sm rounded-lg flex items-center justify-center"><text class="fas fa-plus mr-1d5"></text><text>添加选项</text></button></view><view class="mb-5"><view class="mb-2"><text class="text-sm font-medium text-gray-700">答案 <text class="text-red-500">*</text></text></view><question-right-answer-edit wx:if="{{q}}" u-i="5f117dcf-1" bind:__l="__l" bindupdateModelValue="{{p}}" u-p="{{q}}"></question-right-answer-edit></view><view class="mb-3"><view class="mb-2"><text class="text-sm font-medium text-gray-700">题目解析</text></view><block wx:if="{{r0}}"><textarea placeholder="请输入题目解析..." class="w-full px-4 py-3 h-32 border border-gray-300 rounded-lg border-solid text-sm" value="{{r}}" bindinput="{{s}}"></textarea></block></view></view>