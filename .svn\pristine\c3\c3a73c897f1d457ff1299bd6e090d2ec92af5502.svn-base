const path = require("path");
const isH5 = process.env.UNI_PLATFORM === "h5";

 
const resolve = (p) => {
  return path.resolve(__dirname, p);
};
const plugin = require('tailwindcss/plugin');

/** @type {import('tailwindcss').Config} */
module.exports = {
  // 注意此处，一定要 `path.resolve` 一下, 传入绝对路径
  // 你要有其他目录，比如 components，也必须在这里，添加一下
  content: ["./index.html", './components/**/*.{vue,js,ts}',"./pages/**/*.{html,js,ts,jsx,tsx,vue}"].map(resolve),
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
      },
    },
  },
  corePlugins: {
    // 跨多端可以 h5 开启，小程序关闭
	preflight: isH5,
  },
  // 插件配置
  plugins: [
    // 添加插件生成两套样式
  ]
};