"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  props: {
    bankId: String,
    chapterReadOnly: <PERSON><PERSON>an,
    isEdit: Boolean
  },
  data() {
    return {
      // 用于题型选择的数据
      questionTypes: [
        { id: 1, name: "单选题" },
        { id: 2, name: "多选题" },
        { id: 3, name: "判断题" }
      ],
      // 用于难度选择的数据
      difficulties: [
        { id: 1, name: "简单" },
        { id: 2, name: "一般" },
        { id: 3, name: "困难" }
      ],
      // 表单数据
      questionForm: {
        chapterFullName: "",
        chapterId: null,
        questionType: 1,
        // 1: 单选, 2: 多选, 3: 判断
        questionContent: "",
        options: [
          "",
          ""
        ],
        analysis: "",
        difficulty: 2,
        rightAnswer: ""
      }
    };
  },
  computed: {
    isChoiceQuestion() {
      return this.questionForm.questionType === 1 || this.questionForm.questionType === 2;
    }
  },
  methods: {
    updateFormValue(values) {
      this.questionForm = { ...this.questionForm, ...values };
    },
    init() {
      this.questionForm.questionType = 1;
      this.questionForm.rightAnswer = "";
      this.questionForm.options = [
        "",
        ""
      ];
      this.questionForm.analysis = "";
      this.questionForm.difficulty = 2;
      this.questionForm.questionContent = "";
      this.questionForm.chapterId = null;
    },
    // 题型选择相关方法
    onQuestionTypeChange(e) {
      const lastQuestionType = this.questionForm.questionType;
      this.questionForm.questionType = parseInt(e.detail.value);
      this.questionForm.rightAnswer = "";
      if (this.questionForm.questionType === 3) {
        this.questionForm.options = [];
      } else {
        if (this.questionForm.options.length < 2) {
          if (lastQuestionType === 3) {
            this.questionForm.options = [
              "",
              ""
            ];
          }
        }
      }
    },
    // 难度选择相关方法
    onDifficultyChange(e) {
      this.questionForm.difficulty = e.detail.value;
    },
    // 选项相关方法
    addOption() {
      this.questionForm.options.push("");
    },
    removeOption(index) {
      this.questionForm.options.splice(index, 1);
      const rightAnswer = this.questionForm.rightAnswer;
      if (!rightAnswer) {
        return;
      }
      const questionType = this.questionForm.questionType;
      if (questionType == 1) {
        if (rightAnswer != index) {
          this.questionForm.rightAnswer = "";
        }
      } else if (questionType == 2) {
        this.questionForm.rightAnswer = this.questionForm.rightAnswer.split(",").filter((item) => item != index).join(",");
      }
    },
    // 验证表单
    validateForm(callback) {
      if (!this.questionForm.questionType) {
        common_vendor.index.showToast({
          title: "请选择题型",
          icon: "none"
        });
        return false;
      }
      if (!this.questionForm.questionContent.trim()) {
        common_vendor.index.showToast({
          title: "请输入题干内容",
          icon: "none"
        });
        return false;
      }
      if (this.isChoiceQuestion) {
        if (this.questionForm.options.length < 2) {
          common_vendor.index.showToast({
            title: "请至少添加2个选项",
            icon: "none"
          });
          return false;
        }
        for (let i = 0; i < this.questionForm.options.length; i++) {
          if (!this.questionForm.options[i].trim()) {
            common_vendor.index.showToast({
              title: `请输入选项${String.fromCharCode(65 + i)}内容`,
              icon: "none"
            });
            return false;
          }
        }
      }
      if (!this.questionForm.rightAnswer) {
        common_vendor.index.showToast({
          title: "请选择正确答案",
          icon: "none"
        });
        return false;
      }
      if (this.questionForm.questionType == 1) {
        const rightAnswer = parseInt(this.questionForm.rightAnswer);
        if (rightAnswer < 0 || rightAnswer >= this.questionForm.options.length) {
          common_vendor.index.showToast({
            title: "正确答案不在选项范围内",
            icon: "none"
          });
          return false;
        }
      }
      if (this.questionForm.questionType == 2) {
        const rightAnswer = this.questionForm.rightAnswer.split(",");
        if (rightAnswer.length < 2) {
          common_vendor.index.showToast({
            title: "请至少选择2个正确答案",
            icon: "none"
          });
          return false;
        }
        for (let i = 0; i < rightAnswer.length; i++) {
          if (rightAnswer[i] < 0 || rightAnswer[i] >= this.questionForm.options.length) {
            common_vendor.index.showToast({
              title: "正确答案不在选项范围内",
              icon: "none"
            });
            return false;
          }
        }
      }
      if (!this.questionForm.difficulty) {
        common_vendor.index.showToast({
          title: "请选择难度",
          icon: "none"
        });
        return false;
      }
      this.questionForm.bankId = this.bankId;
      if (callback) {
        callback({ ...this.questionForm, chapterFullName: void 0 });
      }
    }
  }
};
if (!Array) {
  const _easycom_question_chapter_picker2 = common_vendor.resolveComponent("question-chapter-picker");
  const _easycom_question_right_answer_edit2 = common_vendor.resolveComponent("question-right-answer-edit");
  (_easycom_question_chapter_picker2 + _easycom_question_right_answer_edit2)();
}
const _easycom_question_chapter_picker = () => "../question-chapter-picker/question-chapter-picker.js";
const _easycom_question_right_answer_edit = () => "../question-right-answer-edit/question-right-answer-edit.js";
if (!Math) {
  (_easycom_question_chapter_picker + _easycom_question_right_answer_edit)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$props.chapterReadOnly
  }, !$props.chapterReadOnly ? {
    b: common_vendor.o(($event) => $data.questionForm.chapterId = $event),
    c: common_vendor.p({
      bankId: $props.bankId,
      modelValue: $data.questionForm.chapterId
    })
  } : {
    d: common_vendor.t($data.questionForm.chapterFullName || "--")
  }, {
    e: common_vendor.f($data.questionTypes, (questionType, k0, i0) => {
      return {
        a: common_vendor.t(questionType.name),
        b: questionType.id + "",
        c: $data.questionForm.questionType == questionType.id,
        d: questionType.id
      };
    }),
    f: common_vendor.o((...args) => $options.onQuestionTypeChange && $options.onQuestionTypeChange(...args)),
    g: $props.isEdit,
    h: common_vendor.f($data.difficulties, (difficulty, k0, i0) => {
      return {
        a: common_vendor.t(difficulty.name),
        b: difficulty.id + "",
        c: $data.questionForm.difficulty == difficulty.id,
        d: difficulty.id
      };
    }),
    i: common_vendor.o((...args) => $options.onDifficultyChange && $options.onDifficultyChange(...args)),
    j: $data.questionForm.questionContent,
    k: common_vendor.o(($event) => $data.questionForm.questionContent = $event.detail.value),
    l: $options.isChoiceQuestion
  }, $options.isChoiceQuestion ? {
    m: common_vendor.f($data.questionForm.options, (option, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(String.fromCharCode(65 + index))
      }, $data.questionForm.options.length > 2 ? {
        b: common_vendor.o(($event) => $options.removeOption(index), index)
      } : {}, {
        c: `请输入选项${String.fromCharCode(65 + index)}内容...`,
        d: $data.questionForm.options[index],
        e: common_vendor.o(($event) => $data.questionForm.options[index] = $event.detail.value, index),
        f: index
      });
    }),
    n: $data.questionForm.options.length > 2,
    o: common_vendor.o((...args) => $options.addOption && $options.addOption(...args))
  } : {}, {
    p: common_vendor.o(($event) => $data.questionForm.rightAnswer = $event),
    q: common_vendor.p({
      questionType: $data.questionForm.questionType,
      options: $data.questionForm.options,
      modelValue: $data.questionForm.rightAnswer
    }),
    r: $data.questionForm.analysis,
    s: common_vendor.o(($event) => $data.questionForm.analysis = $event.detail.value)
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/question-form/question-form.js.map
