<template>
	<view class="h-screen bg-gray-50 flex flex-col">
	 

		<!-- 题目列表 -->
		<scroll-pagination
			class="flex-1 overflow-hidden"
			:page-size="12"
			:auto-load="true"
			ref="scrollPagination"
			:enable-refresh="true"
			@load="loadData"
		>
			<template v-slot="{list}">
				<view class="px-4 py-2">
					<view 
						v-for="item in list" 
						:key="item.id" 
						@click="toAnalysis(item)"
						class="bg-white rounded-lg shadow-sm border border-gray-100 mb-3 p-4"
					>
						<view class="mb-3">
							<mp-html :content="item.questionContent" />
						</view>

						<view class="flex justify-between items-center">
							<view class="flex items-center space-x-3">
								<view class="bg-yellow-100 text-yellow-600 px-2 py-1 rounded text-xs font-medium">
									{{ questionTypeEnum.find(
										(enumItem) => enumItem.value == item.questionType
									)?.label}}
								</view>
							</view>
							<view class="text-sm text-gray-400">{{ item.updateTime }}</view>
						</view>
					</view>
				</view>
			</template>
			
			<template #empty>
				<view class="py-12 px-4 bg-white rounded-xl shadow-sm flex flex-col items-center justify-center">
					<view class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
						<text class="fas fa-bookmark text-gray-300 text-2xl"></text>
					</view>
					<text class="text-gray-500 text-sm">暂无收藏啦，快去学习吧~</text>
				</view>
			</template>
		</scroll-pagination>
	</view>
</template>

<script>
	const questionTypeEnum = [{
			label: "单选题",
			value: 1
		},
		{
			label: "多选题",
			value: 2
		},
		{
			label: "判断题",
			value: 3
		},
		{
			label: "填空题",
			value: 4,
		},
		{
			label: "问答题",
			value: 5
		},
		{
			label: "组合题",
			value: 9
		},
	];
 
	export default {
		data() {
			return {
				// 题目类型枚举
				questionTypeEnum: [
					{ value: 1, label: '单选题' },
					{ value: 2, label: '多选题' },
					{ value: 3, label: '判断题' }
				],
				total: 0
			}
		},
		computed: {
		 
		},
		onLoad(options) {
			this.bankId = options.bankId;
		},
		onShow() {
			if (this._r) {
				this.refresh();
				this._r = false;
			}
		},
		methods: {
			// 加载数据
			loadData(params, callback) {
				return this.$reqGet('/front/edu/user-collection-question/page', {...params,bankId:this.bankId}).then(res => {
					callback(res.data);
					this.total = res.data.total;
				});
			},
			
			// 跳转到解析页面
			toAnalysis(item) {
				uni.navigateTo({
					url: `/pages/practice/analysis?questionId=${item.questionId}`
				});
			}
		}
	}
</script>

<style scoped>
</style>