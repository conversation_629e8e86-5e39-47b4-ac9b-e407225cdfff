{"version": 3, "file": "detail.js", "sources": ["pages/questionbank/detail.vue", "D:/Program Files/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcXVlc3Rpb25iYW5rL2RldGFpbC52dWU"], "sourcesContent": ["<template>\n  <view class=\"min-h-screen bg-gray-50\">\n    <!-- 题库信息头部 -->\n    <view class=\"bg-gradient-to-r from-primary-500 to-primary-600 px-4 py-6 text-white\">\n      <view class=\"flex items-center mb-4\">\n        <view class=\"w-16 h-16 rounded-xl bg-white bg-opacity-20 flex items-center justify-center mr-4\">\n          <text class=\"fas fa-book text-white text-2xl\"></text>\n        </view>\n        <view class=\"flex-1\">\n          <text class=\"text-xl font-bold block mb-1\">{{ questionBank.name }}</text>\n          <text class=\"text-white text-opacity-80 text-sm\">{{ questionBank.description }}</text>\n        </view>\n      </view>\n      \n      <view class=\"grid grid-cols-4 gap-3\">\n        <view class=\"text-center\">\n          <text class=\"text-white text-lg font-bold block\">{{ questionBank.questionCount }}</text>\n          <text class=\"text-white text-opacity-80 text-xs\">题目</text>\n        </view>\n        <view class=\"text-center\">\n          <text class=\"text-white text-lg font-bold block\">{{ questionBank.memberCount }}</text>\n          <text class=\"text-white text-opacity-80 text-xs\">成员</text>\n        </view>\n        <view class=\"text-center\">\n          <text class=\"text-white text-lg font-bold block\">{{ questionBank.chapterCount }}</text>\n          <text class=\"text-white text-opacity-80 text-xs\">章节</text>\n        </view>\n        <view class=\"text-center\">\n          <text class=\"text-white text-lg font-bold block\">{{ questionBank.rating || '--' }}</text>\n          <text class=\"text-white text-opacity-80 text-xs\">评分</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 主要内容区域 -->\n    <view class=\"px-4 py-4\">\n      <!-- 练习模式选择 -->\n      <view class=\"bg-white rounded-xl p-4 shadow-sm mb-4\">\n        <text class=\"text-lg font-bold text-gray-800 mb-4 block\">练习模式</text>\n        <view class=\"grid grid-cols-2 gap-3\">\n          <button \n            v-for=\"mode in practiceMode\" \n            :key=\"mode.id\"\n            @click=\"startPractice(mode)\"\n            class=\"flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-primary-500 hover:bg-primary-50 transition-all\"\n          >\n            <view class=\"w-12 h-12 rounded-xl flex items-center justify-center mb-2\" :class=\"mode.bgColor\">\n              <text :class=\"mode.icon + ' text-white text-lg'\"></text>\n            </view>\n            <text class=\"text-sm font-medium text-gray-800 text-center\">{{ mode.name }}</text>\n            <text class=\"text-xs text-gray-500 text-center mt-1\">{{ mode.description }}</text>\n          </button>\n        </view>\n      </view>\n\n      <!-- 我的学习记录 -->\n      <view class=\"bg-white rounded-xl p-4 shadow-sm mb-4\">\n        <text class=\"text-lg font-bold text-gray-800 mb-4 block\">我的学习</text>\n        <view class=\"grid grid-cols-2 gap-3\">\n          <button \n            v-for=\"record in myRecords\" \n            :key=\"record.id\"\n            @click=\"goToRecord(record)\"\n            class=\"flex flex-col items-center p-4 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg hover:from-gray-100 hover:to-gray-200 transition-all\"\n          >\n            <view class=\"w-12 h-12 rounded-xl flex items-center justify-center mb-2\" :class=\"record.bgColor\">\n              <text :class=\"record.icon + ' text-white text-lg'\"></text>\n            </view>\n            <text class=\"text-sm font-medium text-gray-800 text-center\">{{ record.name }}</text>\n            <text class=\"text-xs text-gray-500 text-center mt-1\">{{ record.count }}</text>\n          </button>\n        </view>\n      </view>\n    </view>\n\n    <!-- 底部固定操作按钮 -->\n    <view class=\"fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-3\">\n      <view class=\"flex items-center justify-between\">\n        <!-- 分享按钮 -->\n        <button \n          @click=\"shareQuestionBank\" \n          :class=\"questionBank.isMember ? 'w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center' : 'flex flex-col items-center justify-center w-16 h-16 bg-gray-100 rounded-lg'\"\n        >\n          <text class=\"fas fa-share text-gray-600 text-2xl\"></text>\n          <text v-if=\"!questionBank.isMember\" class=\"text-xs text-gray-600 mt-1\">分享</text>\n        </button>\n        \n        <!-- 加入题库按钮 -->\n        <button \n          v-if=\"!questionBank.isMember\" \n          @click=\"joinQuestionBank\" \n          class=\"flex-1 ml-4 bg-primary-500 text-white py-3 rounded-lg text-sm font-medium\"\n        >\n          加入题库\n        </button>\n      </view>\n    </view>\n\n    <!-- 底部安全区域 -->\n    <view class=\"h-20\"></view>\n\n    <!-- 密码输入弹窗 -->\n    <view v-if=\"showPasswordModal\" class=\"fixed inset-0 z-50 flex items-center justify-center\" @click=\"closePasswordModal\">\n      <view class=\"absolute inset-0 bg-black bg-opacity-50\"></view>\n      <view class=\"bg-white rounded-3xl p-6 mx-6 relative z-10 w-full max-w-sm shadow-lg\" @click.stop>\n        <view class=\"flex items-center justify-between mb-6\">\n          <view class=\"flex items-center\">\n            <view class=\"w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center mr-3\">\n              <text class=\"fas fa-lock text-primary-500\"></text>\n            </view>\n            <text class=\"text-xl font-bold text-gray-800\">输入密码</text>\n          </view>\n          <view>\n            <button @click=\"closePasswordModal\" class=\"w-8 h-8 rounded-full hover:bg-gray-100 flex items-center justify-center\">\n              <text class=\"fas fa-times text-gray-400\"></text>\n            </button>\n          </view>\n        </view>\n\n        <view class=\"space-y-4\">\n          <view>\n            <text class=\"text-sm font-medium text-gray-700 block mb-2\">题库密码 <text class=\"text-red-500\">*</text></text>\n            <input \n              v-model=\"passwordInput\" \n              type=\"password\"\n              placeholder=\"请输入题库密码\" \n              maxlength=\"20\"\n              class=\"h-auto p-3 border border-solid border-gray-300 rounded-xl text-sm bg-white focus:border-primary-500 hover:border-primary-500\"\n              @confirm=\"confirmPassword\"\n            />\n          </view>\n        </view>\n\n        <button @click=\"confirmPassword\" class=\"w-full mt-6 py-3.5 bg-primary-500 text-white rounded-xl text-sm font-medium\">\n          <text class=\"fas fa-check mr-2\"></text>\n          确认加入\n        </button>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      bankId: '',\n      showPasswordModal: false,\n      passwordInput: '',\n      questionBank: {\n        isMember: false,\n        needPassword: false,\n        id: '1',\n        name: '计算机基础知识',\n        description: '涵盖计算机组成原理、操作系统、数据结构等基础知识',\n        questionCount: 156,\n        memberCount: 89,\n        chapterCount: 12,\n        rating: '4.8'\n      },\n      practiceMode: [\n        {\n          id: 'chapter',\n          name: '章节练习',\n          description: '按章节分类练习',\n          icon: 'fas fa-list',\n          bgColor: 'bg-blue-500'\n        },\n        {\n          id: 'sequence',\n          name: '顺序练习',\n          description: '按题目顺序练习',\n          icon: 'fas fa-sort-numeric-up',\n          bgColor: 'bg-green-500'\n        },\n        {\n          id: 'type',\n          name: '题型练习',\n          description: '按题型分类练习',\n          icon: 'fas fa-tags',\n          bgColor: 'bg-purple-500'\n        },\n        {\n          id: 'exam',\n          name: '模拟考试',\n          description: '模拟真实考试',\n          icon: 'fas fa-clipboard-check',\n          bgColor: 'bg-red-500'\n        },\n        {\n          id: 'random',\n          name: '随机练习',\n          description: '随机抽取题目',\n          icon: 'fas fa-random',\n          bgColor: 'bg-orange-500'\n        }\n      ],\n      myRecords: [\n        {\n          id: 'wrong',\n          name: '我的错题',\n          count: '12题',\n          icon: 'fas fa-times-circle',\n          bgColor: 'bg-red-500'\n        },\n        {\n          id: 'favorite',\n          name: '我的收藏',\n          count: '8题',\n          icon: 'fas fa-heart',\n          bgColor: 'bg-pink-500'\n        }\n      ]\n    }\n  },\n  onLoad(options) {\n    if (options.bankId) {\n      this.bankId = options.bankId;\n      this.loadQuestionBankDetail();\n\n    \n    }\n  },\n  methods: {\n    loadQuestionBankDetail() {\n \n      this.$reqGet('/front/edu-personal/bankHome', {\n        id: this.bankId\n      }).then(res => {\n        if(res.success){\n          this.questionBank = res.data;\n        }\n      })\n\n    },\n    \n    startPractice(mode) {\n      if(mode.id == 'chapter'){\n        uni.navigateTo({\n          url: `/pages/practice/chapter?bankId=${this.bankId}`\n        });\n      }else if(mode.id=='exam'){\n        uni.navigateTo({\n          url: `/pages/practice/mock-exam?bankId=${this.bankId}`\n        });\n      }else if(mode.id=='type'){\n        uni.navigateTo({\n          url: `/pages/practice/questionType?bankId=${this.bankId}`\n        });\n      }\n      // uni.navigateTo({\n      //   url: `/pages/questionbank/practice?bankId=${this.bankId}&mode=${mode.id}`\n      // });\n    },\n    \n    goToRecord(record) {\n      // 跳转到对应记录页面\n      switch (record.id) {\n        case 'wrong':\n          uni.navigateTo({\n            url: `/pages/practice/wrongQuestion?bankId=${this.bankId}`\n          });\n          break;\n        case 'favorite':\n          uni.navigateTo({\n            url: `/pages/practice/collectionQuestion?bankId=${this.bankId}`\n          });\n          break;\n      }\n    },\n    \n    joinQuestionBank() {\n      // 检查是否需要密码\n      if (this.questionBank.needPassword) {\n        // 显示自定义密码输入弹窗\n        this.showPasswordModal = true;\n        this.passwordInput = '';\n      } else {\n        uni.showModal({\n          title: '提示',\n          content: '确认将此题库加入我的题库吗？',\n          success: (res) => {\n            if (res.confirm) {\n              this.requestJoinBank();\n            }\n          }\n        });\n      }\n    },\n    \n    closePasswordModal() {\n      this.showPasswordModal = false;\n      this.passwordInput = '';\n    },\n    \n    confirmPassword() {\n      if (!this.passwordInput.trim()) {\n        uni.showToast({\n          title: '请输入密码',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      this.requestJoinBank(this.passwordInput);\n      this.closePasswordModal();\n    },\n    \n    requestJoinBank(password = '') {\n      // 请求加入题库接口\n      const params = {\n        id: this.bankId\n      };\n      \n      if (password) {\n        params.pwd = password;\n      }\n\n   \n      \n      this.$reqPost('/front/edu-personal/joinBank', params).then(res => {\n        if (res.success) {\n          // 更新题库状态\n          this.questionBank.isMember = true;\n          uni.showToast({\n            title: '加入成功',\n            icon: 'success'\n          });\n        } else {\n          uni.showToast({\n            title: res.errorMessage || '加入失败',\n            icon: 'error'\n          });\n        }\n      }).catch(err => {\n        uni.showToast({\n          title: '网络错误，请重试',\n          icon: 'error'\n        });\n      });\n    },\n    \n    shareQuestionBank() {\n      // 分享题库逻辑\n      uni.showActionSheet({\n        itemList: ['微信好友', '朋友圈', '复制链接'],\n        success: (res) => {\n          console.log('分享方式:', res.tapIndex);\n        }\n      });\n    }\n  }\n}\n</script>\n\n<style scoped>\n \n</style>", "import MiniProgramPage from 'E:/work/code/edu/edu-personal-uniapp/pages/questionbank/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA+IA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,mBAAmB;AAAA,MACnB,eAAe;AAAA,MACf,cAAc;AAAA,QACZ,UAAU;AAAA,QACV,cAAc;AAAA,QACd,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,eAAe;AAAA,QACf,aAAa;AAAA,QACb,cAAc;AAAA,QACd,QAAQ;AAAA,MACT;AAAA,MACD,cAAc;AAAA,QACZ;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACD;AAAA,MACD,WAAW;AAAA,QACT;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,OAAO,SAAS;AACd,QAAI,QAAQ,QAAQ;AAClB,WAAK,SAAS,QAAQ;AACtB,WAAK,uBAAsB;AAAA,IAG7B;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,yBAAyB;AAEvB,WAAK,QAAQ,gCAAgC;AAAA,QAC3C,IAAI,KAAK;AAAA,OACV,EAAE,KAAK,SAAO;AACb,YAAG,IAAI,SAAQ;AACb,eAAK,eAAe,IAAI;AAAA,QAC1B;AAAA,OACD;AAAA,IAEF;AAAA,IAED,cAAc,MAAM;AAClB,UAAG,KAAK,MAAM,WAAU;AACtBA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,kCAAkC,KAAK,MAAM;AAAA,QACpD,CAAC;AAAA,iBACM,KAAK,MAAI,QAAO;AACvBA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,oCAAoC,KAAK,MAAM;AAAA,QACtD,CAAC;AAAA,iBACM,KAAK,MAAI,QAAO;AACvBA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,uCAAuC,KAAK,MAAM;AAAA,QACzD,CAAC;AAAA,MACH;AAAA,IAID;AAAA,IAED,WAAW,QAAQ;AAEjB,cAAQ,OAAO,IAAE;AAAA,QACf,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK,wCAAwC,KAAK,MAAM;AAAA,UAC1D,CAAC;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK,6CAA6C,KAAK,MAAM;AAAA,UAC/D,CAAC;AACD;AAAA,MACJ;AAAA,IACD;AAAA,IAED,mBAAmB;AAEjB,UAAI,KAAK,aAAa,cAAc;AAElC,aAAK,oBAAoB;AACzB,aAAK,gBAAgB;AAAA,aAChB;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AACf,mBAAK,gBAAe;AAAA,YACtB;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACD;AAAA,IAED,qBAAqB;AACnB,WAAK,oBAAoB;AACzB,WAAK,gBAAgB;AAAA,IACtB;AAAA,IAED,kBAAkB;AAChB,UAAI,CAAC,KAAK,cAAc,QAAQ;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,WAAK,gBAAgB,KAAK,aAAa;AACvC,WAAK,mBAAkB;AAAA,IACxB;AAAA,IAED,gBAAgB,WAAW,IAAI;AAE7B,YAAM,SAAS;AAAA,QACb,IAAI,KAAK;AAAA;AAGX,UAAI,UAAU;AACZ,eAAO,MAAM;AAAA,MACf;AAIA,WAAK,SAAS,gCAAgC,MAAM,EAAE,KAAK,SAAO;AAChE,YAAI,IAAI,SAAS;AAEf,eAAK,aAAa,WAAW;AAC7BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,eACI;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,IAAI,gBAAgB;AAAA,YAC3B,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC,EAAE,MAAM,SAAO;AACdA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH,CAAC;AAAA,IACF;AAAA,IAED,oBAAoB;AAElBA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,QAAQ,OAAO,MAAM;AAAA,QAChC,SAAS,CAAC,QAAQ;AAChBA,wBAAY,MAAA,MAAA,OAAA,wCAAA,SAAS,IAAI,QAAQ;AAAA,QACnC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/VA,GAAG,WAAW,eAAe;"}