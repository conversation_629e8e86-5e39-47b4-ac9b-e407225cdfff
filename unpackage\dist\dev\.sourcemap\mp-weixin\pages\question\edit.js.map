{"version": 3, "file": "edit.js", "sources": ["pages/question/edit.vue", "D:/Program Files/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcXVlc3Rpb24vZWRpdC52dWU"], "sourcesContent": ["<template>\n  <view class=\"min-h-screen bg-gray-50\">\n    \n    <!-- 加载中 -->\n    \n      <loading-view :show=\"loading\" />\n    \n    \n    <!-- 表单内容 -->\n    <view class=\"px-4 pt-2 pb-10\">\n      <question-form v-if=\"!loading\" :bankId=\"bankId\"  ref=\"questionForm\" :isEdit=\"true\" />  \n    </view>\n\n    <view class=\"h-11\"></view>\n    <!-- 底部保存按钮 -->\n    <view class=\"fixed bottom-0 left-0 right-0 bg-white p-4 border-t border-gray-200\">\n      <button @click=\"saveQuestion\" class=\"w-full h-auto py-3 text-sm bg-primary-500 text-white rounded-lg shadow-sm font-medium  \">\n          保存题目\n        </button>\n    </view>\n      <!--安全距离-->\n  </view>\n</template>\n\n<script>\n\n\nexport default {\n  data() {\n    return {\n      loading:false,\n      bankId:null,\n     \n    }\n  },\n  \n \n  \n  onLoad(options) {\n    this.id = options.id;\n    this.loadQuestion();\n  },\n\n  methods: {\n    loadQuestion(){\n      this.loading = true;\n      this.$reqGet('/front/edu-personal/question/detail',{id:this.id}).then(res=>{\n        if(res.success){\n          this.loading = false;\n          this.bankId = res.data.bankId; \n          this.$nextTick(()=>{\n            this.$refs.questionForm.updateFormValue(res.data);\n          })\n        }else{\n          uni.showToast({\n            title: res.errorMessage,\n            icon: 'none'\n          });\n        }\n\n      })\n    },\n      // 保存题目\n    saveQuestion() {\n\nthis.$refs.questionForm.validateForm((formValues)=>{\n  formValues.id = this.id;\n  this.$reqPost('/front/edu-personal/question/edit',formValues,true,'保存中...').then(res=>{\n  if(res.success){\n    uni.showToast({\n      title: '保存成功',\n      icon: 'success'\n    });\n    setTimeout(()=>{\n      uni.navigateBack();\n    },1000);\n  }else{\n    uni.showToast({\n      title: res.errorMessage,\n      icon: 'none'\n    });\n  }\n})\n})\n\n\n\n\n\n\n},\n    \n \n  }\n}\n</script>\n\n<style scoped>\n</style>", "import MiniProgramPage from 'E:/work/code/edu/edu-personal-uniapp/pages/question/edit.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA2BA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,SAAQ;AAAA,MACR,QAAO;AAAA,IAET;AAAA,EACD;AAAA,EAID,OAAO,SAAS;AACd,SAAK,KAAK,QAAQ;AAClB,SAAK,aAAY;AAAA,EAClB;AAAA,EAED,SAAS;AAAA,IACP,eAAc;AACZ,WAAK,UAAU;AACf,WAAK,QAAQ,uCAAsC,EAAC,IAAG,KAAK,GAAE,CAAC,EAAE,KAAK,SAAK;AACzE,YAAG,IAAI,SAAQ;AACb,eAAK,UAAU;AACf,eAAK,SAAS,IAAI,KAAK;AACvB,eAAK,UAAU,MAAI;AACjB,iBAAK,MAAM,aAAa,gBAAgB,IAAI,IAAI;AAAA,WACjD;AAAA,eACE;AACHA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,IAAI;AAAA,YACX,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,OAED;AAAA,IACF;AAAA;AAAA,IAED,eAAe;AAEnB,WAAK,MAAM,aAAa,aAAa,CAAC,eAAa;AACjD,mBAAW,KAAK,KAAK;AACrB,aAAK,SAAS,qCAAoC,YAAW,MAAK,QAAQ,EAAE,KAAK,SAAK;AACtF,cAAG,IAAI,SAAQ;AACbA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AACD,uBAAW,MAAI;AACbA,4BAAG,MAAC,aAAY;AAAA,YACjB,GAAC,GAAI;AAAA,iBACH;AACHA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO,IAAI;AAAA,cACX,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACD,CAAC;AAAA,IAOD;AAAA,EAGE;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7FA,GAAG,WAAW,eAAe;"}